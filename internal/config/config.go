package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config represents the application configuration
type Config struct {
	// Server configuration
	Server ServerConfig `json:"server"`
	
	// OttoPay API configuration
	OttoPay OttoPayConfig `json:"ottopay"`
	
	// Database configuration
	Database DatabaseConfig `json:"database"`
	
	// Redis configuration
	Redis RedisConfig `json:"redis"`
	
	// Logging configuration
	Logging LoggingConfig `json:"logging"`
	
	// Security configuration
	Security SecurityConfig `json:"security"`
	
	// Monitoring configuration
	Monitoring MonitoringConfig `json:"monitoring"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
	TLS          TLSConfig     `json:"tls"`
}

// TLSConfig holds TLS configuration
type TLSConfig struct {
	Enabled  bool   `json:"enabled"`
	CertFile string `json:"cert_file"`
	KeyFile  string `json:"key_file"`
}

// OttoPayConfig holds OttoPay API configuration
type OttoPayConfig struct {
	BaseURL     string        `json:"base_url"`
	APIKey      string        `json:"api_key"`
	Username    string        `json:"username"`
	Password    string        `json:"password"`
	Timeout     time.Duration `json:"timeout"`
	MaxRetries  int           `json:"max_retries"`
	RetryDelay  time.Duration `json:"retry_delay"`
	Environment string        `json:"environment"` // development, staging, production
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Driver          string        `json:"driver"`
	Host            string        `json:"host"`
	Port            int           `json:"port"`
	Database        string        `json:"database"`
	Username        string        `json:"username"`
	Password        string        `json:"password"`
	SSLMode         string        `json:"ssl_mode"`
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	Password     string        `json:"password"`
	Database     int           `json:"database"`
	PoolSize     int           `json:"pool_size"`
	DialTimeout  time.Duration `json:"dial_timeout"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level      string `json:"level"`
	Format     string `json:"format"` // json, text
	Output     string `json:"output"` // stdout, stderr, file
	File       string `json:"file"`
	MaxSize    int    `json:"max_size"`    // MB
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`     // days
	Compress   bool   `json:"compress"`
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	EncryptionKey    string        `json:"encryption_key"`
	TokenExpiration  time.Duration `json:"token_expiration"`
	RefreshThreshold time.Duration `json:"refresh_threshold"`
	RateLimit        RateLimitConfig `json:"rate_limit"`
	CORS             CORSConfig    `json:"cors"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled     bool          `json:"enabled"`
	RequestsPerMinute int     `json:"requests_per_minute"`
	BurstSize   int           `json:"burst_size"`
	WindowSize  time.Duration `json:"window_size"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	Enabled          bool     `json:"enabled"`
	AllowedOrigins   []string `json:"allowed_origins"`
	AllowedMethods   []string `json:"allowed_methods"`
	AllowedHeaders   []string `json:"allowed_headers"`
	ExposedHeaders   []string `json:"exposed_headers"`
	AllowCredentials bool     `json:"allow_credentials"`
	MaxAge           int      `json:"max_age"`
}

// MonitoringConfig holds monitoring configuration
type MonitoringConfig struct {
	Enabled    bool          `json:"enabled"`
	MetricsPort int          `json:"metrics_port"`
	HealthPort int           `json:"health_port"`
	Interval   time.Duration `json:"interval"`
	Prometheus PrometheusConfig `json:"prometheus"`
}

// PrometheusConfig holds Prometheus configuration
type PrometheusConfig struct {
	Enabled   bool   `json:"enabled"`
	Namespace string `json:"namespace"`
	Subsystem string `json:"subsystem"`
}

// LoadConfig loads configuration from environment variables
func LoadConfig() (*Config, error) {
	config := &Config{
		Server: ServerConfig{
			Host:         getEnvString("SERVER_HOST", "0.0.0.0"),
			Port:         getEnvInt("SERVER_PORT", 8080),
			ReadTimeout:  getEnvDuration("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getEnvDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
			IdleTimeout:  getEnvDuration("SERVER_IDLE_TIMEOUT", 120*time.Second),
			TLS: TLSConfig{
				Enabled:  getEnvBool("TLS_ENABLED", false),
				CertFile: getEnvString("TLS_CERT_FILE", ""),
				KeyFile:  getEnvString("TLS_KEY_FILE", ""),
			},
		},
		OttoPay: OttoPayConfig{
			BaseURL:     getEnvString("OTTOPAY_BASE_URL", ""),
			APIKey:      getEnvString("OTTOPAY_API_KEY", ""),
			Username:    getEnvString("OTTOPAY_USERNAME", ""),
			Password:    getEnvString("OTTOPAY_PASSWORD", ""),
			Timeout:     getEnvDuration("OTTOPAY_TIMEOUT", 30*time.Second),
			MaxRetries:  getEnvInt("OTTOPAY_MAX_RETRIES", 3),
			RetryDelay:  getEnvDuration("OTTOPAY_RETRY_DELAY", 1*time.Second),
			Environment: getEnvString("OTTOPAY_ENVIRONMENT", "development"),
		},
		Database: DatabaseConfig{
			Driver:          getEnvString("DB_DRIVER", "postgres"),
			Host:            getEnvString("DB_HOST", "localhost"),
			Port:            getEnvInt("DB_PORT", 5432),
			Database:        getEnvString("DB_DATABASE", "ottopay"),
			Username:        getEnvString("DB_USERNAME", "ottopay"),
			Password:        getEnvString("DB_PASSWORD", ""),
			SSLMode:         getEnvString("DB_SSL_MODE", "disable"),
			MaxOpenConns:    getEnvInt("DB_MAX_OPEN_CONNS", 25),
			MaxIdleConns:    getEnvInt("DB_MAX_IDLE_CONNS", 5),
			ConnMaxLifetime: getEnvDuration("DB_CONN_MAX_LIFETIME", 5*time.Minute),
		},
		Redis: RedisConfig{
			Host:         getEnvString("REDIS_HOST", "localhost"),
			Port:         getEnvInt("REDIS_PORT", 6379),
			Password:     getEnvString("REDIS_PASSWORD", ""),
			Database:     getEnvInt("REDIS_DATABASE", 0),
			PoolSize:     getEnvInt("REDIS_POOL_SIZE", 10),
			DialTimeout:  getEnvDuration("REDIS_DIAL_TIMEOUT", 5*time.Second),
			ReadTimeout:  getEnvDuration("REDIS_READ_TIMEOUT", 3*time.Second),
			WriteTimeout: getEnvDuration("REDIS_WRITE_TIMEOUT", 3*time.Second),
		},
		Logging: LoggingConfig{
			Level:      getEnvString("LOG_LEVEL", "info"),
			Format:     getEnvString("LOG_FORMAT", "json"),
			Output:     getEnvString("LOG_OUTPUT", "stdout"),
			File:       getEnvString("LOG_FILE", ""),
			MaxSize:    getEnvInt("LOG_MAX_SIZE", 100),
			MaxBackups: getEnvInt("LOG_MAX_BACKUPS", 3),
			MaxAge:     getEnvInt("LOG_MAX_AGE", 28),
			Compress:   getEnvBool("LOG_COMPRESS", true),
		},
		Security: SecurityConfig{
			EncryptionKey:    getEnvString("ENCRYPTION_KEY", ""),
			TokenExpiration:  getEnvDuration("TOKEN_EXPIRATION", 24*time.Hour),
			RefreshThreshold: getEnvDuration("TOKEN_REFRESH_THRESHOLD", 5*time.Minute),
			RateLimit: RateLimitConfig{
				Enabled:           getEnvBool("RATE_LIMIT_ENABLED", true),
				RequestsPerMinute: getEnvInt("RATE_LIMIT_RPM", 100),
				BurstSize:         getEnvInt("RATE_LIMIT_BURST", 10),
				WindowSize:        getEnvDuration("RATE_LIMIT_WINDOW", 1*time.Minute),
			},
			CORS: CORSConfig{
				Enabled:          getEnvBool("CORS_ENABLED", true),
				AllowedOrigins:   getEnvStringSlice("CORS_ALLOWED_ORIGINS", []string{"*"}),
				AllowedMethods:   getEnvStringSlice("CORS_ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
				AllowedHeaders:   getEnvStringSlice("CORS_ALLOWED_HEADERS", []string{"Content-Type", "Authorization", "X-Auth-Token"}),
				ExposedHeaders:   getEnvStringSlice("CORS_EXPOSED_HEADERS", []string{}),
				AllowCredentials: getEnvBool("CORS_ALLOW_CREDENTIALS", false),
				MaxAge:           getEnvInt("CORS_MAX_AGE", 86400),
			},
		},
		Monitoring: MonitoringConfig{
			Enabled:     getEnvBool("MONITORING_ENABLED", true),
			MetricsPort: getEnvInt("METRICS_PORT", 9090),
			HealthPort:  getEnvInt("HEALTH_PORT", 8081),
			Interval:    getEnvDuration("MONITORING_INTERVAL", 30*time.Second),
			Prometheus: PrometheusConfig{
				Enabled:   getEnvBool("PROMETHEUS_ENABLED", true),
				Namespace: getEnvString("PROMETHEUS_NAMESPACE", "ottopay"),
				Subsystem: getEnvString("PROMETHEUS_SUBSYSTEM", "api"),
			},
		},
	}
	
	return config, nil
}

// Helper functions for environment variable parsing

func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// GetDatabaseDSN returns the database connection string
func (c *DatabaseConfig) GetDatabaseDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.Username, c.Password, c.Database, c.SSLMode)
}

// GetRedisAddr returns the Redis address
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetServerAddr returns the server address
func (c *ServerConfig) GetServerAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// IsProduction checks if the environment is production
func (c *OttoPayConfig) IsProduction() bool {
	return strings.ToLower(c.Environment) == "production"
}

// IsDevelopment checks if the environment is development
func (c *OttoPayConfig) IsDevelopment() bool {
	return strings.ToLower(c.Environment) == "development"
}

// IsStaging checks if the environment is staging
func (c *OttoPayConfig) IsStaging() bool {
	return strings.ToLower(c.Environment) == "staging"
}
