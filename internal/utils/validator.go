package utils

import (
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// Validator provides utility functions for input validation
type Validator struct{}

// NewValidator creates a new validator instance
func NewValidator() *Validator {
	return &Validator{}
}

// Common validation patterns
var (
	EmailRegex       = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	PhoneRegex       = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	AlphanumericRegex = regexp.MustCompile(`^[a-zA-Z0-9]+$`)
	NumericRegex     = regexp.MustCompile(`^[0-9]+$`)
	AlphaRegex       = regexp.MustCompile(`^[a-zA-Z]+$`)
	UUIDRegex        = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error on field '%s': %s", e.Field, e.Message)
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return "validation errors occurred"
	}
	
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	
	return strings.Join(messages, "; ")
}

func (e *ValidationErrors) Add(field, message string, value interface{}) {
	*e = append(*e, ValidationError{
		Field:   field,
		Message: message,
		Value:   value,
	})
}

func (e ValidationErrors) HasErrors() bool {
	return len(e) > 0
}

// String validation functions

// IsEmpty checks if a string is empty or contains only whitespace
func (v *Validator) IsEmpty(value string) bool {
	return strings.TrimSpace(value) == ""
}

// IsNotEmpty checks if a string is not empty
func (v *Validator) IsNotEmpty(value string) bool {
	return !v.IsEmpty(value)
}

// HasMinLength checks if a string has at least the minimum length
func (v *Validator) HasMinLength(value string, minLength int) bool {
	return len(value) >= minLength
}

// HasMaxLength checks if a string does not exceed the maximum length
func (v *Validator) HasMaxLength(value string, maxLength int) bool {
	return len(value) <= maxLength
}

// HasExactLength checks if a string has exactly the specified length
func (v *Validator) HasExactLength(value string, length int) bool {
	return len(value) == length
}

// IsAlphanumeric checks if a string contains only alphanumeric characters
func (v *Validator) IsAlphanumeric(value string) bool {
	return AlphanumericRegex.MatchString(value)
}

// IsNumeric checks if a string contains only numeric characters
func (v *Validator) IsNumeric(value string) bool {
	return NumericRegex.MatchString(value)
}

// IsAlpha checks if a string contains only alphabetic characters
func (v *Validator) IsAlpha(value string) bool {
	return AlphaRegex.MatchString(value)
}

// IsEmail checks if a string is a valid email address
func (v *Validator) IsEmail(value string) bool {
	return EmailRegex.MatchString(value)
}

// IsPhone checks if a string is a valid phone number
func (v *Validator) IsPhone(value string) bool {
	return PhoneRegex.MatchString(value)
}

// IsUUID checks if a string is a valid UUID
func (v *Validator) IsUUID(value string) bool {
	return UUIDRegex.MatchString(strings.ToLower(value))
}

// IsIP checks if a string is a valid IP address
func (v *Validator) IsIP(value string) bool {
	return net.ParseIP(value) != nil
}

// IsIPv4 checks if a string is a valid IPv4 address
func (v *Validator) IsIPv4(value string) bool {
	ip := net.ParseIP(value)
	return ip != nil && ip.To4() != nil
}

// IsIPv6 checks if a string is a valid IPv6 address
func (v *Validator) IsIPv6(value string) bool {
	ip := net.ParseIP(value)
	return ip != nil && ip.To4() == nil
}

// IsURL checks if a string is a valid URL
func (v *Validator) IsURL(value string) bool {
	// Simple URL validation
	return strings.HasPrefix(value, "http://") || strings.HasPrefix(value, "https://")
}

// Numeric validation functions

// IsPositive checks if a number is positive
func (v *Validator) IsPositive(value float64) bool {
	return value > 0
}

// IsNegative checks if a number is negative
func (v *Validator) IsNegative(value float64) bool {
	return value < 0
}

// IsZero checks if a number is zero
func (v *Validator) IsZero(value float64) bool {
	return value == 0
}

// IsInRange checks if a number is within the specified range (inclusive)
func (v *Validator) IsInRange(value, min, max float64) bool {
	return value >= min && value <= max
}

// IsInteger checks if a string represents a valid integer
func (v *Validator) IsInteger(value string) bool {
	_, err := strconv.Atoi(value)
	return err == nil
}

// IsFloat checks if a string represents a valid float
func (v *Validator) IsFloat(value string) bool {
	_, err := strconv.ParseFloat(value, 64)
	return err == nil
}

// Date and time validation functions

// IsValidDate checks if a string is a valid date in the specified format
func (v *Validator) IsValidDate(value, format string) bool {
	_, err := time.Parse(format, value)
	return err == nil
}

// IsValidTime checks if a string is a valid time in the specified format
func (v *Validator) IsValidTime(value, format string) bool {
	_, err := time.Parse(format, value)
	return err == nil
}

// IsFutureDate checks if a date is in the future
func (v *Validator) IsFutureDate(value time.Time) bool {
	return value.After(time.Now())
}

// IsPastDate checks if a date is in the past
func (v *Validator) IsPastDate(value time.Time) bool {
	return value.Before(time.Now())
}

// IsToday checks if a date is today
func (v *Validator) IsToday(value time.Time) bool {
	now := time.Now()
	return value.Year() == now.Year() && value.YearDay() == now.YearDay()
}

// Collection validation functions

// IsInSlice checks if a value exists in a slice
func (v *Validator) IsInSlice(value string, slice []string) bool {
	for _, item := range slice {
		if item == value {
			return true
		}
	}
	return false
}

// HasUniqueElements checks if a slice has unique elements
func (v *Validator) HasUniqueElements(slice []string) bool {
	seen := make(map[string]bool)
	for _, item := range slice {
		if seen[item] {
			return false
		}
		seen[item] = true
	}
	return true
}

// Custom validation functions

// MatchesPattern checks if a string matches a custom regex pattern
func (v *Validator) MatchesPattern(value, pattern string) bool {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return false
	}
	return regex.MatchString(value)
}

// IsValidJSON checks if a string is valid JSON
func (v *Validator) IsValidJSON(value string) bool {
	// Simple JSON validation - check for basic structure
	value = strings.TrimSpace(value)
	return (strings.HasPrefix(value, "{") && strings.HasSuffix(value, "}")) ||
		   (strings.HasPrefix(value, "[") && strings.HasSuffix(value, "]"))
}

// Validation helper functions

// ValidateRequired validates that a field is not empty
func (v *Validator) ValidateRequired(field, value string) *ValidationError {
	if v.IsEmpty(value) {
		return &ValidationError{
			Field:   field,
			Message: "field is required",
			Value:   value,
		}
	}
	return nil
}

// ValidateLength validates string length constraints
func (v *Validator) ValidateLength(field, value string, min, max int) *ValidationError {
	length := len(value)
	if length < min {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("field must be at least %d characters long", min),
			Value:   value,
		}
	}
	if length > max {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("field cannot exceed %d characters", max),
			Value:   value,
		}
	}
	return nil
}

// ValidateEmail validates email format
func (v *Validator) ValidateEmail(field, value string) *ValidationError {
	if !v.IsEmpty(value) && !v.IsEmail(value) {
		return &ValidationError{
			Field:   field,
			Message: "field must be a valid email address",
			Value:   value,
		}
	}
	return nil
}

// ValidateNumeric validates numeric format
func (v *Validator) ValidateNumeric(field, value string) *ValidationError {
	if !v.IsEmpty(value) && !v.IsNumeric(value) {
		return &ValidationError{
			Field:   field,
			Message: "field must contain only numeric characters",
			Value:   value,
		}
	}
	return nil
}

// ValidateRange validates numeric range
func (v *Validator) ValidateRange(field string, value, min, max float64) *ValidationError {
	if !v.IsInRange(value, min, max) {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("field must be between %.2f and %.2f", min, max),
			Value:   value,
		}
	}
	return nil
}

// ValidateOneOf validates that a value is one of the allowed values
func (v *Validator) ValidateOneOf(field, value string, allowed []string) *ValidationError {
	if !v.IsEmpty(value) && !v.IsInSlice(value, allowed) {
		return &ValidationError{
			Field:   field,
			Message: fmt.Sprintf("field must be one of: %s", strings.Join(allowed, ", ")),
			Value:   value,
		}
	}
	return nil
}
