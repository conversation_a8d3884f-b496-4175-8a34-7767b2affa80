package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"runtime"
	"strings"
	"time"
)

// LogLevel represents the logging level
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// String returns the string representation of the log level
func (l LogLevel) String() string {
	switch l {
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelInfo:
		return "INFO"
	case LogLevelWarn:
		return "WARN"
	case LogLevelError:
		return "ERROR"
	case LogLevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger interface for logging operations
type Logger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
	SetLevel(level LogLevel)
	SetOutput(w io.Writer)
}

// SimpleLogger provides a simple logging implementation
type SimpleLogger struct {
	level  LogLevel
	logger *log.Logger
	output io.Writer
}

// NewSimpleLogger creates a new simple logger
func NewSimpleLogger(level LogLevel, output io.Writer) Logger {
	if output == nil {
		output = os.Stdout
	}
	
	return &SimpleLogger{
		level:  level,
		logger: log.New(output, "", 0), // No default prefix or flags
		output: output,
	}
}

// NewDefaultLogger creates a logger with default settings
func NewDefaultLogger() Logger {
	return NewSimpleLogger(LogLevelInfo, os.Stdout)
}

// formatMessage formats a log message with timestamp, level, and caller info
func (l *SimpleLogger) formatMessage(level LogLevel, message string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	
	// Get caller information
	_, file, line, ok := runtime.Caller(3) // Skip 3 frames to get the actual caller
	caller := "unknown"
	if ok {
		// Extract just the filename from the full path
		parts := strings.Split(file, "/")
		if len(parts) > 0 {
			caller = fmt.Sprintf("%s:%d", parts[len(parts)-1], line)
		}
	}
	
	return fmt.Sprintf("[%s] %s [%s] %s", timestamp, level.String(), caller, message)
}

// shouldLog checks if a message should be logged based on the current level
func (l *SimpleLogger) shouldLog(level LogLevel) bool {
	return level >= l.level
}

// log logs a message at the specified level
func (l *SimpleLogger) log(level LogLevel, args ...interface{}) {
	if !l.shouldLog(level) {
		return
	}
	
	message := fmt.Sprint(args...)
	formatted := l.formatMessage(level, message)
	l.logger.Println(formatted)
	
	if level == LogLevelFatal {
		os.Exit(1)
	}
}

// logf logs a formatted message at the specified level
func (l *SimpleLogger) logf(level LogLevel, format string, args ...interface{}) {
	if !l.shouldLog(level) {
		return
	}
	
	message := fmt.Sprintf(format, args...)
	formatted := l.formatMessage(level, message)
	l.logger.Println(formatted)
	
	if level == LogLevelFatal {
		os.Exit(1)
	}
}

// Debug logs a debug message
func (l *SimpleLogger) Debug(args ...interface{}) {
	l.log(LogLevelDebug, args...)
}

// Debugf logs a formatted debug message
func (l *SimpleLogger) Debugf(format string, args ...interface{}) {
	l.logf(LogLevelDebug, format, args...)
}

// Info logs an info message
func (l *SimpleLogger) Info(args ...interface{}) {
	l.log(LogLevelInfo, args...)
}

// Infof logs a formatted info message
func (l *SimpleLogger) Infof(format string, args ...interface{}) {
	l.logf(LogLevelInfo, format, args...)
}

// Warn logs a warning message
func (l *SimpleLogger) Warn(args ...interface{}) {
	l.log(LogLevelWarn, args...)
}

// Warnf logs a formatted warning message
func (l *SimpleLogger) Warnf(format string, args ...interface{}) {
	l.logf(LogLevelWarn, format, args...)
}

// Error logs an error message
func (l *SimpleLogger) Error(args ...interface{}) {
	l.log(LogLevelError, args...)
}

// Errorf logs a formatted error message
func (l *SimpleLogger) Errorf(format string, args ...interface{}) {
	l.logf(LogLevelError, format, args...)
}

// Fatal logs a fatal message and exits the program
func (l *SimpleLogger) Fatal(args ...interface{}) {
	l.log(LogLevelFatal, args...)
}

// Fatalf logs a formatted fatal message and exits the program
func (l *SimpleLogger) Fatalf(format string, args ...interface{}) {
	l.logf(LogLevelFatal, format, args...)
}

// SetLevel sets the logging level
func (l *SimpleLogger) SetLevel(level LogLevel) {
	l.level = level
}

// SetOutput sets the output writer
func (l *SimpleLogger) SetOutput(w io.Writer) {
	l.output = w
	l.logger.SetOutput(w)
}

// NoOpLogger is a logger that does nothing
type NoOpLogger struct{}

// NewNoOpLogger creates a new no-op logger
func NewNoOpLogger() Logger {
	return &NoOpLogger{}
}

func (l *NoOpLogger) Debug(args ...interface{})                 {}
func (l *NoOpLogger) Debugf(format string, args ...interface{}) {}
func (l *NoOpLogger) Info(args ...interface{})                  {}
func (l *NoOpLogger) Infof(format string, args ...interface{})  {}
func (l *NoOpLogger) Warn(args ...interface{})                  {}
func (l *NoOpLogger) Warnf(format string, args ...interface{})  {}
func (l *NoOpLogger) Error(args ...interface{})                 {}
func (l *NoOpLogger) Errorf(format string, args ...interface{}) {}
func (l *NoOpLogger) Fatal(args ...interface{})                 {}
func (l *NoOpLogger) Fatalf(format string, args ...interface{}) {}
func (l *NoOpLogger) SetLevel(level LogLevel)                   {}
func (l *NoOpLogger) SetOutput(w io.Writer)                     {}

// ParseLogLevel parses a string into a LogLevel
func ParseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return LogLevelDebug
	case "INFO":
		return LogLevelInfo
	case "WARN", "WARNING":
		return LogLevelWarn
	case "ERROR":
		return LogLevelError
	case "FATAL":
		return LogLevelFatal
	default:
		return LogLevelInfo
	}
}

// LoggerWithFields provides structured logging with fields
type LoggerWithFields struct {
	logger Logger
	fields map[string]interface{}
}

// NewLoggerWithFields creates a logger with predefined fields
func NewLoggerWithFields(logger Logger, fields map[string]interface{}) *LoggerWithFields {
	return &LoggerWithFields{
		logger: logger,
		fields: fields,
	}
}

// WithField adds a field to the logger
func (l *LoggerWithFields) WithField(key string, value interface{}) *LoggerWithFields {
	fields := make(map[string]interface{})
	for k, v := range l.fields {
		fields[k] = v
	}
	fields[key] = value
	
	return &LoggerWithFields{
		logger: l.logger,
		fields: fields,
	}
}

// WithFields adds multiple fields to the logger
func (l *LoggerWithFields) WithFields(fields map[string]interface{}) *LoggerWithFields {
	newFields := make(map[string]interface{})
	for k, v := range l.fields {
		newFields[k] = v
	}
	for k, v := range fields {
		newFields[k] = v
	}
	
	return &LoggerWithFields{
		logger: l.logger,
		fields: newFields,
	}
}

// formatWithFields formats a message with fields
func (l *LoggerWithFields) formatWithFields(message string) string {
	if len(l.fields) == 0 {
		return message
	}
	
	var fieldStrings []string
	for key, value := range l.fields {
		fieldStrings = append(fieldStrings, fmt.Sprintf("%s=%v", key, value))
	}
	
	return fmt.Sprintf("%s [%s]", message, strings.Join(fieldStrings, " "))
}

// Debug logs a debug message with fields
func (l *LoggerWithFields) Debug(args ...interface{}) {
	message := fmt.Sprint(args...)
	l.logger.Debug(l.formatWithFields(message))
}

// Debugf logs a formatted debug message with fields
func (l *LoggerWithFields) Debugf(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	l.logger.Debug(l.formatWithFields(message))
}

// Info logs an info message with fields
func (l *LoggerWithFields) Info(args ...interface{}) {
	message := fmt.Sprint(args...)
	l.logger.Info(l.formatWithFields(message))
}

// Infof logs a formatted info message with fields
func (l *LoggerWithFields) Infof(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	l.logger.Info(l.formatWithFields(message))
}

// Error logs an error message with fields
func (l *LoggerWithFields) Error(args ...interface{}) {
	message := fmt.Sprint(args...)
	l.logger.Error(l.formatWithFields(message))
}

// Errorf logs a formatted error message with fields
func (l *LoggerWithFields) Errorf(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	l.logger.Error(l.formatWithFields(message))
}
