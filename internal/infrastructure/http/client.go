package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// HTTPClient interface for HTTP operations (for testing/mocking)
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client represents an HTTP client wrapper with additional functionality
type Client struct {
	httpClient    HTTPClient
	baseURL       string
	defaultHeaders map[string]string
	timeout       time.Duration
	retryPolicy   *RetryPolicy
	logger        Logger
}

// Logger interface for logging HTTP operations
type Logger interface {
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Errorf(format string, args ...interface{})
}

// NoOpLogger is a no-operation logger implementation
type NoOpLogger struct{}

func (l *NoOpLogger) Debugf(format string, args ...interface{}) {}
func (l *NoOpLogger) Infof(format string, args ...interface{})  {}
func (l *NoOpLogger) Errorf(format string, args ...interface{}) {}

// ClientConfig holds configuration for the HTTP client
type ClientConfig struct {
	BaseURL        string
	Timeout        time.Duration
	DefaultHeaders map[string]string
	RetryPolicy    *RetryPolicy
	HTTPClient     HTTPClient
	Logger         Logger
}

// NewClient creates a new HTTP client with the provided configuration
func NewClient(config *ClientConfig) *Client {
	if config == nil {
		config = &ClientConfig{}
	}
	
	// Set defaults
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	
	if config.HTTPClient == nil {
		config.HTTPClient = &http.Client{
			Timeout: config.Timeout,
		}
	}
	
	if config.Logger == nil {
		config.Logger = &NoOpLogger{}
	}
	
	if config.DefaultHeaders == nil {
		config.DefaultHeaders = make(map[string]string)
	}
	
	// Set default headers
	if config.DefaultHeaders["Content-Type"] == "" {
		config.DefaultHeaders["Content-Type"] = "application/json"
	}
	if config.DefaultHeaders["Accept"] == "" {
		config.DefaultHeaders["Accept"] = "application/json"
	}
	
	return &Client{
		httpClient:     config.HTTPClient,
		baseURL:        config.BaseURL,
		defaultHeaders: config.DefaultHeaders,
		timeout:        config.Timeout,
		retryPolicy:    config.RetryPolicy,
		logger:         config.Logger,
	}
}

// Request represents an HTTP request
type Request struct {
	Method  string
	Path    string
	Body    interface{}
	Headers map[string]string
	Query   map[string]string
}

// Response represents an HTTP response
type Response struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
	Duration   time.Duration
}

// Do executes an HTTP request and returns the response
func (c *Client) Do(ctx context.Context, req *Request) (*Response, error) {
	startTime := time.Now()
	
	// Build URL
	fullURL, err := c.buildURL(req.Path, req.Query)
	if err != nil {
		return nil, fmt.Errorf("failed to build URL: %w", err)
	}
	
	// Prepare request body
	var bodyReader io.Reader
	if req.Body != nil {
		bodyBytes, err := json.Marshal(req.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
		c.logger.Debugf("Request body: %s", string(bodyBytes))
	}
	
	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, fullURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}
	
	// Set headers
	c.setHeaders(httpReq, req.Headers)
	
	c.logger.Debugf("Making %s request to %s", req.Method, fullURL)
	
	// Execute request with retry policy
	var httpResp *http.Response
	if c.retryPolicy != nil {
		httpResp, err = c.executeWithRetry(ctx, httpReq)
	} else {
		httpResp, err = c.httpClient.Do(httpReq)
	}
	
	if err != nil {
		c.logger.Errorf("HTTP request failed: %v", err)
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer httpResp.Body.Close()
	
	// Read response body
	responseBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	duration := time.Since(startTime)
	
	c.logger.Debugf("Response status: %d, duration: %v, body length: %d", 
		httpResp.StatusCode, duration, len(responseBody))
	
	response := &Response{
		StatusCode: httpResp.StatusCode,
		Headers:    httpResp.Header,
		Body:       responseBody,
		Duration:   duration,
	}
	
	return response, nil
}

// Get executes a GET request
func (c *Client) Get(ctx context.Context, path string, query map[string]string) (*Response, error) {
	req := &Request{
		Method: "GET",
		Path:   path,
		Query:  query,
	}
	return c.Do(ctx, req)
}

// Post executes a POST request
func (c *Client) Post(ctx context.Context, path string, body interface{}) (*Response, error) {
	req := &Request{
		Method: "POST",
		Path:   path,
		Body:   body,
	}
	return c.Do(ctx, req)
}

// Put executes a PUT request
func (c *Client) Put(ctx context.Context, path string, body interface{}) (*Response, error) {
	req := &Request{
		Method: "PUT",
		Path:   path,
		Body:   body,
	}
	return c.Do(ctx, req)
}

// Delete executes a DELETE request
func (c *Client) Delete(ctx context.Context, path string) (*Response, error) {
	req := &Request{
		Method: "DELETE",
		Path:   path,
	}
	return c.Do(ctx, req)
}

// buildURL builds the full URL from base URL, path, and query parameters
func (c *Client) buildURL(path string, query map[string]string) (string, error) {
	baseURL := c.baseURL
	if baseURL == "" {
		return "", fmt.Errorf("base URL is required")
	}
	
	u, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("invalid base URL: %w", err)
	}
	
	// Join path
	u.Path = u.Path + path
	
	// Add query parameters
	if len(query) > 0 {
		q := u.Query()
		for key, value := range query {
			q.Set(key, value)
		}
		u.RawQuery = q.Encode()
	}
	
	return u.String(), nil
}

// setHeaders sets headers on the HTTP request
func (c *Client) setHeaders(req *http.Request, headers map[string]string) {
	// Set default headers
	for key, value := range c.defaultHeaders {
		req.Header.Set(key, value)
	}
	
	// Set request-specific headers (override defaults)
	for key, value := range headers {
		req.Header.Set(key, value)
	}
}

// executeWithRetry executes an HTTP request with retry policy
func (c *Client) executeWithRetry(ctx context.Context, req *http.Request) (*http.Response, error) {
	var lastErr error
	
	for attempt := 0; attempt <= c.retryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			delay := c.retryPolicy.GetDelay(attempt)
			c.logger.Debugf("Retrying request in %v (attempt %d/%d)", delay, attempt, c.retryPolicy.MaxRetries)
			
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(delay):
			}
		}
		
		// Clone request for retry (body might be consumed)
		reqClone, err := c.cloneRequest(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("failed to clone request: %w", err)
		}
		
		resp, err := c.httpClient.Do(reqClone)
		if err != nil {
			lastErr = err
			if !c.retryPolicy.ShouldRetry(0, err) {
				break
			}
			continue
		}
		
		// Check if response status code should trigger retry
		if c.retryPolicy.ShouldRetry(resp.StatusCode, nil) {
			resp.Body.Close()
			lastErr = fmt.Errorf("HTTP %d", resp.StatusCode)
			continue
		}
		
		return resp, nil
	}
	
	return nil, fmt.Errorf("request failed after %d retries: %w", c.retryPolicy.MaxRetries, lastErr)
}

// cloneRequest creates a copy of an HTTP request
func (c *Client) cloneRequest(ctx context.Context, req *http.Request) (*http.Request, error) {
	var bodyReader io.Reader
	
	if req.Body != nil {
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			return nil, err
		}
		req.Body.Close()
		
		// Restore original body
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		// Create new body for clone
		bodyReader = bytes.NewReader(bodyBytes)
	}
	
	clone, err := http.NewRequestWithContext(ctx, req.Method, req.URL.String(), bodyReader)
	if err != nil {
		return nil, err
	}
	
	// Copy headers
	clone.Header = req.Header.Clone()
	
	return clone, nil
}

// SetDefaultHeader sets a default header for all requests
func (c *Client) SetDefaultHeader(key, value string) {
	c.defaultHeaders[key] = value
}

// RemoveDefaultHeader removes a default header
func (c *Client) RemoveDefaultHeader(key string) {
	delete(c.defaultHeaders, key)
}

// SetBaseURL sets the base URL for the client
func (c *Client) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// GetBaseURL returns the current base URL
func (c *Client) GetBaseURL() string {
	return c.baseURL
}
