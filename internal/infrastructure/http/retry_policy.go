package http

import (
	"fmt"
	"math"
	"math/rand/v2"
	"net"
	"net/http"
	"net/url"
	"time"
)

// RetryPolicy defines the retry behavior for HTTP requests
type RetryPolicy struct {
	MaxRetries      int
	InitialDelay    time.Duration
	MaxDelay        time.Duration
	BackoffFactor   float64
	RetryableErrors []error
	RetryableCodes  []int
}

// DefaultRetryPolicy returns a default retry policy
func DefaultRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    3,
		InitialDelay:  1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		RetryableCodes: []int{
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
			http.StatusTooManyRequests,     // 429
		},
	}
}

// ConservativeRetryPolicy returns a conservative retry policy with fewer retries
func ConservativeRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    2,
		InitialDelay:  2 * time.Second,
		MaxDelay:      10 * time.Second,
		BackoffFactor: 1.5,
		RetryableCodes: []int{
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
		},
	}
}

// AggressiveRetryPolicy returns an aggressive retry policy with more retries
func AggressiveRetryPolicy() *RetryPolicy {
	return &RetryPolicy{
		MaxRetries:    5,
		InitialDelay:  500 * time.Millisecond,
		MaxDelay:      60 * time.Second,
		BackoffFactor: 2.5,
		RetryableCodes: []int{
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
			http.StatusTooManyRequests,     // 429
			http.StatusRequestTimeout,      // 408
		},
	}
}

// ShouldRetry determines if a request should be retried based on status code and error
func (rp *RetryPolicy) ShouldRetry(statusCode int, err error) bool {
	// Check for retryable errors
	if err != nil {
		return rp.isRetryableError(err)
	}

	// Check for retryable status codes
	if statusCode > 0 {
		return rp.isRetryableStatusCode(statusCode)
	}

	return false
}

// GetDelay calculates the delay for a given retry attempt
func (rp *RetryPolicy) GetDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return 0
	}

	// Calculate exponential backoff delay
	delay := float64(rp.InitialDelay) * math.Pow(rp.BackoffFactor, float64(attempt-1))

	// Apply jitter (±10% randomization)
	jitter := 0.1 * delay * (2*rand.Float64() - 1)
	delay += jitter

	// Ensure delay doesn't exceed maximum
	if delay > float64(rp.MaxDelay) {
		delay = float64(rp.MaxDelay)
	}

	// Ensure delay is not negative
	if delay < 0 {
		delay = float64(rp.InitialDelay)
	}

	return time.Duration(delay)
}

// isRetryableError checks if an error is retryable
func (rp *RetryPolicy) isRetryableError(err error) bool {
	// Network errors are generally retryable
	if isNetworkError(err) {
		return true
	}

	// Check against configured retryable errors
	for _, retryableErr := range rp.RetryableErrors {
		if err == retryableErr {
			return true
		}
	}

	return false
}

// isRetryableStatusCode checks if a status code is retryable
func (rp *RetryPolicy) isRetryableStatusCode(statusCode int) bool {
	for _, code := range rp.RetryableCodes {
		if statusCode == code {
			return true
		}
	}
	return false
}

// isNetworkError checks if an error is a network-related error
func isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	// Check for common network errors
	switch err := err.(type) {
	case *net.DNSError:
		return true
	case *net.OpError:
		return true
	case *url.Error:
		return isNetworkError(err.Err)
	}

	// Check error message for common network error patterns
	errMsg := err.Error()
	networkErrorPatterns := []string{
		"connection refused",
		"connection reset",
		"connection timeout",
		"network is unreachable",
		"no route to host",
		"temporary failure",
		"timeout",
		"EOF",
	}

	for _, pattern := range networkErrorPatterns {
		if contains(errMsg, pattern) {
			return true
		}
	}

	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOfSubstring(s, substr) >= 0)))
}

// indexOfSubstring finds the index of a substring in a string
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// RetryPolicyBuilder provides a fluent interface for building retry policies
type RetryPolicyBuilder struct {
	policy *RetryPolicy
}

// NewRetryPolicyBuilder creates a new retry policy builder
func NewRetryPolicyBuilder() *RetryPolicyBuilder {
	return &RetryPolicyBuilder{
		policy: &RetryPolicy{
			MaxRetries:     3,
			InitialDelay:   1 * time.Second,
			MaxDelay:       30 * time.Second,
			BackoffFactor:  2.0,
			RetryableCodes: []int{500, 502, 503, 504},
		},
	}
}

// WithMaxRetries sets the maximum number of retries
func (b *RetryPolicyBuilder) WithMaxRetries(maxRetries int) *RetryPolicyBuilder {
	b.policy.MaxRetries = maxRetries
	return b
}

// WithInitialDelay sets the initial delay between retries
func (b *RetryPolicyBuilder) WithInitialDelay(delay time.Duration) *RetryPolicyBuilder {
	b.policy.InitialDelay = delay
	return b
}

// WithMaxDelay sets the maximum delay between retries
func (b *RetryPolicyBuilder) WithMaxDelay(delay time.Duration) *RetryPolicyBuilder {
	b.policy.MaxDelay = delay
	return b
}

// WithBackoffFactor sets the backoff factor for exponential backoff
func (b *RetryPolicyBuilder) WithBackoffFactor(factor float64) *RetryPolicyBuilder {
	b.policy.BackoffFactor = factor
	return b
}

// WithRetryableCodes sets the HTTP status codes that should trigger retries
func (b *RetryPolicyBuilder) WithRetryableCodes(codes []int) *RetryPolicyBuilder {
	b.policy.RetryableCodes = codes
	return b
}

// AddRetryableCode adds an HTTP status code that should trigger retries
func (b *RetryPolicyBuilder) AddRetryableCode(code int) *RetryPolicyBuilder {
	b.policy.RetryableCodes = append(b.policy.RetryableCodes, code)
	return b
}

// WithRetryableErrors sets the errors that should trigger retries
func (b *RetryPolicyBuilder) WithRetryableErrors(errors []error) *RetryPolicyBuilder {
	b.policy.RetryableErrors = errors
	return b
}

// AddRetryableError adds an error that should trigger retries
func (b *RetryPolicyBuilder) AddRetryableError(err error) *RetryPolicyBuilder {
	b.policy.RetryableErrors = append(b.policy.RetryableErrors, err)
	return b
}

// Build returns the configured retry policy
func (b *RetryPolicyBuilder) Build() *RetryPolicy {
	return b.policy
}

// Validate validates the retry policy configuration
func (rp *RetryPolicy) Validate() error {
	if rp.MaxRetries < 0 {
		return fmt.Errorf("max retries cannot be negative")
	}

	if rp.InitialDelay < 0 {
		return fmt.Errorf("initial delay cannot be negative")
	}

	if rp.MaxDelay < rp.InitialDelay {
		return fmt.Errorf("max delay cannot be less than initial delay")
	}

	if rp.BackoffFactor <= 0 {
		return fmt.Errorf("backoff factor must be positive")
	}

	return nil
}

// Clone creates a copy of the retry policy
func (rp *RetryPolicy) Clone() *RetryPolicy {
	clone := &RetryPolicy{
		MaxRetries:    rp.MaxRetries,
		InitialDelay:  rp.InitialDelay,
		MaxDelay:      rp.MaxDelay,
		BackoffFactor: rp.BackoffFactor,
	}

	// Deep copy slices
	if rp.RetryableCodes != nil {
		clone.RetryableCodes = make([]int, len(rp.RetryableCodes))
		copy(clone.RetryableCodes, rp.RetryableCodes)
	}

	if rp.RetryableErrors != nil {
		clone.RetryableErrors = make([]error, len(rp.RetryableErrors))
		copy(clone.RetryableErrors, rp.RetryableErrors)
	}

	return clone
}
