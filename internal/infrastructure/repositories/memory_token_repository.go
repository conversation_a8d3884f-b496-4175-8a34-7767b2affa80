package repositories

import (
	"context"
	"fmt"
	"sync"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
)

// MemoryTokenRepository provides an in-memory implementation of TokenRepository
type MemoryTokenRepository struct {
	mu     sync.RWMutex
	tokens map[string]*entities.Token
	byUser map[string]string // username -> token ID
	byValue map[string]string // token value -> token ID
}

// NewMemoryTokenRepository creates a new in-memory token repository
func NewMemoryTokenRepository() repositories.TokenRepository {
	return &MemoryTokenRepository{
		tokens:  make(map[string]*entities.Token),
		byUser:  make(map[string]string),
		byValue: make(map[string]string),
	}
}

// Store stores a token in the repository
func (r *MemoryTokenRepository) Store(ctx context.Context, token *entities.Token) error {
	if token == nil {
		return fmt.Errorf("token cannot be nil")
	}
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Check if token already exists
	if _, exists := r.tokens[token.ID]; exists {
		return fmt.Errorf("token with ID %s already exists", token.ID)
	}
	
	// Store token
	r.tokens[token.ID] = token.Clone()
	r.byUser[token.Username] = token.ID
	r.byValue[token.Value] = token.ID
	
	return nil
}

// GetByID retrieves a token by its ID
func (r *MemoryTokenRepository) GetByID(ctx context.Context, id string) (*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	token, exists := r.tokens[id]
	if !exists {
		return nil, fmt.Errorf("token with ID %s not found", id)
	}
	
	return token.Clone(), nil
}

// GetByUsername retrieves the latest valid token for a username
func (r *MemoryTokenRepository) GetByUsername(ctx context.Context, username string) (*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	tokenID, exists := r.byUser[username]
	if !exists {
		return nil, fmt.Errorf("no token found for username %s", username)
	}
	
	token, exists := r.tokens[tokenID]
	if !exists {
		// Clean up inconsistent state
		delete(r.byUser, username)
		return nil, fmt.Errorf("token not found for username %s", username)
	}
	
	return token.Clone(), nil
}

// GetByValue retrieves a token by its value
func (r *MemoryTokenRepository) GetByValue(ctx context.Context, value string) (*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	tokenID, exists := r.byValue[value]
	if !exists {
		return nil, fmt.Errorf("token with value not found")
	}
	
	token, exists := r.tokens[tokenID]
	if !exists {
		// Clean up inconsistent state
		delete(r.byValue, value)
		return nil, fmt.Errorf("token not found")
	}
	
	return token.Clone(), nil
}

// Update updates an existing token
func (r *MemoryTokenRepository) Update(ctx context.Context, token *entities.Token) error {
	if token == nil {
		return fmt.Errorf("token cannot be nil")
	}
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	existingToken, exists := r.tokens[token.ID]
	if !exists {
		return fmt.Errorf("token with ID %s not found", token.ID)
	}
	
	// Update indexes if value changed
	if existingToken.Value != token.Value {
		delete(r.byValue, existingToken.Value)
		r.byValue[token.Value] = token.ID
	}
	
	// Update token
	r.tokens[token.ID] = token.Clone()
	
	return nil
}

// Delete removes a token from the repository
func (r *MemoryTokenRepository) Delete(ctx context.Context, id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	token, exists := r.tokens[id]
	if !exists {
		return fmt.Errorf("token with ID %s not found", id)
	}
	
	// Remove from all indexes
	delete(r.tokens, id)
	delete(r.byUser, token.Username)
	delete(r.byValue, token.Value)
	
	return nil
}

// DeleteByUsername removes all tokens for a username
func (r *MemoryTokenRepository) DeleteByUsername(ctx context.Context, username string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	tokenID, exists := r.byUser[username]
	if !exists {
		return nil // No tokens to delete
	}
	
	token, exists := r.tokens[tokenID]
	if exists {
		delete(r.tokens, tokenID)
		delete(r.byValue, token.Value)
	}
	delete(r.byUser, username)
	
	return nil
}

// Revoke revokes a token (marks it as revoked)
func (r *MemoryTokenRepository) Revoke(ctx context.Context, id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	token, exists := r.tokens[id]
	if !exists {
		return fmt.Errorf("token with ID %s not found", id)
	}
	
	if err := token.Revoke(); err != nil {
		return err
	}
	
	return nil
}

// RevokeByUsername revokes all tokens for a username
func (r *MemoryTokenRepository) RevokeByUsername(ctx context.Context, username string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	tokenID, exists := r.byUser[username]
	if !exists {
		return nil // No tokens to revoke
	}
	
	token, exists := r.tokens[tokenID]
	if exists {
		token.Revoke()
	}
	
	return nil
}

// GetExpiredTokens retrieves all expired tokens
func (r *MemoryTokenRepository) GetExpiredTokens(ctx context.Context) ([]*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var expiredTokens []*entities.Token
	for _, token := range r.tokens {
		if token.IsExpired() {
			expiredTokens = append(expiredTokens, token.Clone())
		}
	}
	
	return expiredTokens, nil
}

// GetTokensExpiringBefore retrieves tokens expiring before the specified time
func (r *MemoryTokenRepository) GetTokensExpiringBefore(ctx context.Context, before time.Time) ([]*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var expiringTokens []*entities.Token
	for _, token := range r.tokens {
		if token.ExpiresAt.Before(before) {
			expiringTokens = append(expiringTokens, token.Clone())
		}
	}
	
	return expiringTokens, nil
}

// CleanupExpiredTokens removes expired tokens from the repository
func (r *MemoryTokenRepository) CleanupExpiredTokens(ctx context.Context) (int, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	var expiredIDs []string
	for id, token := range r.tokens {
		if token.IsExpired() {
			expiredIDs = append(expiredIDs, id)
		}
	}
	
	// Remove expired tokens
	for _, id := range expiredIDs {
		token := r.tokens[id]
		delete(r.tokens, id)
		delete(r.byUser, token.Username)
		delete(r.byValue, token.Value)
	}
	
	return len(expiredIDs), nil
}

// IsTokenValid checks if a token is valid (exists, not expired, not revoked)
func (r *MemoryTokenRepository) IsTokenValid(ctx context.Context, value string) (bool, error) {
	token, err := r.GetByValue(ctx, value)
	if err != nil {
		return false, nil // Token not found
	}
	
	return token.IsValid(), nil
}

// GetTokenStats returns statistics about tokens in the repository
func (r *MemoryTokenRepository) GetTokenStats(ctx context.Context) (*repositories.TokenStats, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	stats := &repositories.TokenStats{}
	uniqueUsers := make(map[string]bool)
	
	for _, token := range r.tokens {
		stats.TotalTokens++
		uniqueUsers[token.Username] = true
		
		if token.IsValid() {
			stats.ActiveTokens++
		} else if token.IsExpired() {
			stats.ExpiredTokens++
		}
		
		if token.IsRevoked {
			stats.RevokedTokens++
		}
	}
	
	stats.UniqueUsers = len(uniqueUsers)
	
	return stats, nil
}

// ListTokensByUsername lists all tokens for a username with pagination
func (r *MemoryTokenRepository) ListTokensByUsername(ctx context.Context, username string, limit, offset int) ([]*entities.Token, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var userTokens []*entities.Token
	for _, token := range r.tokens {
		if token.Username == username {
			userTokens = append(userTokens, token.Clone())
		}
	}
	
	// Apply pagination
	start := offset
	if start > len(userTokens) {
		start = len(userTokens)
	}
	
	end := start + limit
	if end > len(userTokens) {
		end = len(userTokens)
	}
	
	return userTokens[start:end], nil
}

// CountTokensByUsername counts tokens for a username
func (r *MemoryTokenRepository) CountTokensByUsername(ctx context.Context, username string) (int, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := 0
	for _, token := range r.tokens {
		if token.Username == username {
			count++
		}
	}
	
	return count, nil
}

// GetActiveTokensCount returns the count of active (valid) tokens
func (r *MemoryTokenRepository) GetActiveTokensCount(ctx context.Context) (int, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := 0
	for _, token := range r.tokens {
		if token.IsValid() {
			count++
		}
	}
	
	return count, nil
}
