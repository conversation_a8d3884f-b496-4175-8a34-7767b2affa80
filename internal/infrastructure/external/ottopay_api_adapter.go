package external

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
	httpClient "repo.nusatek.id/sugeng/ottopay/internal/infrastructure/http"
)

// OttoPayAPIAdapter implements the OttoPayService interface using HTTP API calls
type OttoPayAPIAdapter struct {
	httpClient *httpClient.Client
	apiKey     string
	username   string
	password   string
	logger     Logger
}

// Logger interface for logging operations
type Logger interface {
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Errorf(format string, args ...interface{})
}

// NoOpLogger is a no-operation logger implementation
type NoOpLogger struct{}

func (l *NoOpLogger) Debugf(format string, args ...interface{}) {}
func (l *NoOpLogger) Infof(format string, args ...interface{})  {}
func (l *NoOpLogger) Errorf(format string, args ...interface{}) {}

// OttoPayAPIConfig holds configuration for the OttoPay API adapter
type OttoPayAPIConfig struct {
	BaseURL     string
	APIKey      string
	Username    string
	Password    string
	Timeout     time.Duration
	RetryPolicy *httpClient.RetryPolicy
	Logger      Logger
}

// NewOttoPayAPIAdapter creates a new OttoPay API adapter
func NewOttoPayAPIAdapter(config *OttoPayAPIConfig) services.OttoPayService {
	if config.Logger == nil {
		config.Logger = &NoOpLogger{}
	}

	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.RetryPolicy == nil {
		config.RetryPolicy = httpClient.DefaultRetryPolicy()
	}

	// Create HTTP client
	clientConfig := &httpClient.ClientConfig{
		BaseURL:     config.BaseURL,
		Timeout:     config.Timeout,
		RetryPolicy: config.RetryPolicy,
		DefaultHeaders: map[string]string{
			"Content-Type": "application/json",
			"Accept":       "application/json",
			"x-api-key":    config.APIKey,
		},
		Logger: config.Logger,
	}

	client := httpClient.NewClient(clientConfig)

	return &OttoPayAPIAdapter{
		httpClient: client,
		apiKey:     config.APIKey,
		username:   config.Username,
		password:   config.Password,
		logger:     config.Logger,
	}
}

// AuthenticateUser authenticates a user and returns a token
func (a *OttoPayAPIAdapter) AuthenticateUser(ctx context.Context, username, password string) (*services.AuthenticationResult, error) {
	a.logger.Debugf("Authenticating user: %s", username)

	// Prepare request
	request := map[string]string{
		"username": username,
		"password": password,
	}

	// Make API call
	response, err := a.httpClient.Post(ctx, "/token", request)
	if err != nil {
		a.logger.Errorf("Authentication request failed: %v", err)
		return nil, fmt.Errorf("authentication request failed: %w", err)
	}

	// Parse response
	var apiResponse struct {
		Meta struct {
			Status  bool   `json:"status"`
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"meta"`
		Data struct {
			ID       string `json:"id"`
			Username string `json:"username"`
			IDToken  string `json:"id_token"`
		} `json:"data"`
	}

	if err := json.Unmarshal(response.Body, &apiResponse); err != nil {
		a.logger.Errorf("Failed to parse authentication response: %v", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check response status
	if response.StatusCode != 200 || !apiResponse.Meta.Status {
		a.logger.Errorf("Authentication failed: %s", apiResponse.Meta.Message)
		return &services.AuthenticationResult{
			Success: false,
			Message: apiResponse.Meta.Message,
		}, nil
	}

	// Create token entity
	token, err := entities.NewToken(
		apiResponse.Data.ID,
		entities.TokenTypeAccess,
		apiResponse.Data.IDToken,
		apiResponse.Data.Username,
		24*time.Hour, // Default expiration
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create token entity: %w", err)
	}

	a.logger.Infof("Authentication successful for user: %s", username)

	return &services.AuthenticationResult{
		Token:     token,
		ExpiresIn: 24 * time.Hour,
		Success:   true,
		Message:   "Authentication successful",
	}, nil
}

// RefreshToken refreshes an existing token
func (a *OttoPayAPIAdapter) RefreshToken(ctx context.Context, token string) (*services.AuthenticationResult, error) {
	a.logger.Debugf("Refreshing token")

	// For OttoPay, we might need to re-authenticate
	// This is a simplified implementation
	return a.AuthenticateUser(ctx, a.username, a.password)
}

// ValidateToken validates a token
func (a *OttoPayAPIAdapter) ValidateToken(ctx context.Context, token string) (*services.TokenValidationResult, error) {
	a.logger.Debugf("Validating token")

	// For this implementation, we'll assume the token is valid if it's not empty
	// In a real implementation, you might call an API endpoint to validate
	if token == "" {
		return &services.TokenValidationResult{
			Valid:  false,
			Reason: "Token is empty",
		}, nil
	}

	return &services.TokenValidationResult{
		Valid:     true,
		ExpiresIn: 24 * time.Hour, // Assume 24 hours remaining
	}, nil
}

// RevokeToken revokes a token
func (a *OttoPayAPIAdapter) RevokeToken(ctx context.Context, token string) error {
	a.logger.Debugf("Revoking token")

	// OttoPay might not have a specific revoke endpoint
	// This is a placeholder implementation
	return nil
}

// InquireCustomer performs customer inquiry
func (a *OttoPayAPIAdapter) InquireCustomer(ctx context.Context, req *services.CustomerInquiryRequest) (*services.CustomerInquiryResult, error) {
	a.logger.Debugf("Performing customer inquiry for: %s-%s", req.CompanyCode, req.CustomerNumber)

	// Prepare request
	request := map[string]interface{}{
		"CompanyCode":    req.CompanyCode.String(),
		"CustomerNumber": req.CustomerNumber.String(),
		"RequestID":      req.RequestID.String(),
		"ChannelType":    req.ChannelType,
	}

	if req.TransactionDate != nil {
		request["TransactionDate"] = req.TransactionDate.Format("2006/01/02 15:04:05")
	}

	if req.AdditionalData != "" {
		request["AdditionalData"] = req.AdditionalData
	}

	// Make API call
	response, err := a.httpClient.Post(ctx, "/inquiry", request)
	if err != nil {
		a.logger.Errorf("Inquiry request failed: %v", err)
		return nil, fmt.Errorf("inquiry request failed: %w", err)
	}

	// Parse response
	var apiResponse struct {
		CompanyCode    string `json:"CompanyCode"`
		CustomerNumber string `json:"CustomerNumber"`
		RequestID      string `json:"RequestID"`
		InquiryStatus  string `json:"InquiryStatus"`
		InquiryReason  struct {
			Indonesian string `json:"Indonesian"`
			English    string `json:"English"`
		} `json:"InquiryReason"`
		CustomerName   string      `json:"CustomerName"`
		CurrencyCode   string      `json:"CurrencyCode"`
		TotalAmount    string      `json:"TotalAmount"`
		SubCompany     string      `json:"SubCompany"`
		DetailBills    interface{} `json:"DetailBills"`
		FreeTexts      interface{} `json:"FreeTexts"`
		AdditionalData string      `json:"AdditionalData"`
	}

	if err := json.Unmarshal(response.Body, &apiResponse); err != nil {
		a.logger.Errorf("Failed to parse inquiry response: %v", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check inquiry status
	success := apiResponse.InquiryStatus == "00"

	var customer *entities.Customer
	if success {
		// Create customer entity
		companyCode, _ := valueobjects.NewCompanyCode(apiResponse.CompanyCode)
		customerNumber, _ := valueobjects.NewCustomerNumber(apiResponse.CustomerNumber)
		currencyCode, _ := valueobjects.NewCurrencyCode(apiResponse.CurrencyCode)
		totalAmount, _ := valueobjects.NewAmountFromString(apiResponse.TotalAmount, currencyCode)

		customer, err = entities.NewCustomer(
			companyCode,
			customerNumber,
			apiResponse.CustomerName,
			currencyCode,
			totalAmount,
		)
		if err != nil {
			a.logger.Errorf("Failed to create customer entity: %v", err)
			return nil, fmt.Errorf("failed to create customer entity: %w", err)
		}

		customer.SubCompany = apiResponse.SubCompany
		customer.SetDetailBills(apiResponse.DetailBills)
		customer.SetFreeTexts(apiResponse.FreeTexts)
		customer.SetAdditionalData(apiResponse.AdditionalData)
	}

	result := &services.CustomerInquiryResult{
		Success:       success,
		Customer:      customer,
		InquiryStatus: apiResponse.InquiryStatus,
		InquiryReason: entities.PaymentFlagReason{
			Indonesian: apiResponse.InquiryReason.Indonesian,
			English:    apiResponse.InquiryReason.English,
		},
		ResponseTime: response.Duration,
	}

	if success {
		result.Message = "Customer inquiry successful"
		a.logger.Infof("Customer inquiry successful for: %s-%s", req.CompanyCode, req.CustomerNumber)
	} else {
		result.Message = apiResponse.InquiryReason.English
		a.logger.Infof("Customer inquiry failed for: %s-%s, reason: %s", req.CompanyCode, req.CustomerNumber, result.Message)
	}

	return result, nil
}

// ProcessPayment processes a payment
func (a *OttoPayAPIAdapter) ProcessPayment(ctx context.Context, req *services.PaymentProcessingRequest) (*services.PaymentProcessingResult, error) {
	a.logger.Debugf("Processing payment for: %s-%s, amount: %s", req.CompanyCode, req.CustomerNumber, req.PaidAmount.String())

	// Prepare request
	request := map[string]interface{}{
		"CompanyCode":    req.CompanyCode.String(),
		"CustomerNumber": req.CustomerNumber.String(),
		"RequestID":      req.RequestID.String(),
		"ChannelType":    req.ChannelType,
		"CustomerName":   req.CustomerName,
		"CurrencyCode":   req.CurrencyCode.String(),
		"PaidAmount":     req.PaidAmount.String(),
		"TotalAmount":    req.TotalAmount.String(),
		"Reference":      req.Reference,
	}

	if req.SubCompany != "" {
		request["SubCompany"] = req.SubCompany
	} else {
		request["SubCompany"] = "00000"
	}

	if req.TransactionDate != nil {
		request["TransactionDate"] = req.TransactionDate.Format("2006/01/02 15:04:05")
	}

	if req.DetailBills != nil {
		request["DetailBills"] = req.DetailBills
	} else {
		request["DetailBills"] = []interface{}{}
	}

	if req.AdditionalData != "" {
		request["AdditionalData"] = req.AdditionalData
	}

	// Make API call
	response, err := a.httpClient.Post(ctx, "/payment", request)
	if err != nil {
		a.logger.Errorf("Payment request failed: %v", err)
		return nil, fmt.Errorf("payment request failed: %w", err)
	}

	// Parse response
	var apiResponse struct {
		CompanyCode       string `json:"CompanyCode"`
		CustomerNumber    string `json:"CustomerNumber"`
		RequestID         string `json:"RequestID"`
		PaymentFlagStatus string `json:"PaymentFlagStatus"`
		PaymentFlagReason struct {
			Indonesian string `json:"Indonesian"`
			English    string `json:"English"`
		} `json:"PaymentFlagReason"`
		CustomerName    string      `json:"CustomerName"`
		CurrencyCode    string      `json:"CurrencyCode"`
		PaidAmount      string      `json:"PaidAmount"`
		TotalAmount     string      `json:"TotalAmount"`
		TransactionDate string      `json:"TransactionDate"`
		DetailBills     interface{} `json:"DetailBills"`
		FreeTexts       interface{} `json:"FreeTexts"`
		AdditionalData  string      `json:"AdditionalData"`
	}

	if err := json.Unmarshal(response.Body, &apiResponse); err != nil {
		a.logger.Errorf("Failed to parse payment response: %v", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Map payment status
	var status entities.PaymentStatus
	var success bool

	switch apiResponse.PaymentFlagStatus {
	case "00":
		status = entities.PaymentStatusSuccess
		success = true
	case "01":
		status = entities.PaymentStatusFailed
		success = false
	case "02":
		status = entities.PaymentStatusTimeout
		success = false
	default:
		status = entities.PaymentStatusFailed
		success = false
	}

	result := &services.PaymentProcessingResult{
		Success: success,
		Status:  status,
		Reason: entities.PaymentFlagReason{
			Indonesian: apiResponse.PaymentFlagReason.Indonesian,
			English:    apiResponse.PaymentFlagReason.English,
		},
		TransactionID: apiResponse.RequestID,
		ResponseTime:  response.Duration,
	}

	if success {
		result.Message = "Payment processed successfully"
		a.logger.Infof("Payment successful for: %s-%s, amount: %s", req.CompanyCode, req.CustomerNumber, req.PaidAmount.String())
	} else {
		result.Message = apiResponse.PaymentFlagReason.English
		a.logger.Infof("Payment failed for: %s-%s, reason: %s", req.CompanyCode, req.CustomerNumber, result.Message)
	}

	return result, nil
}

// ValidateInquiryRequest validates an inquiry request
func (a *OttoPayAPIAdapter) ValidateInquiryRequest(ctx context.Context, req *services.CustomerInquiryRequest) error {
	// Basic validation - in a real implementation this might call an API endpoint
	if req.CompanyCode.IsEmpty() {
		return fmt.Errorf("company code is required")
	}
	if req.CustomerNumber.IsEmpty() {
		return fmt.Errorf("customer number is required")
	}
	if req.RequestID.IsEmpty() {
		return fmt.Errorf("request ID is required")
	}
	return nil
}

// ValidatePaymentRequest validates a payment request
func (a *OttoPayAPIAdapter) ValidatePaymentRequest(ctx context.Context, req *services.PaymentProcessingRequest) error {
	// Basic validation - in a real implementation this might call an API endpoint
	if req.CompanyCode.IsEmpty() {
		return fmt.Errorf("company code is required")
	}
	if req.CustomerNumber.IsEmpty() {
		return fmt.Errorf("customer number is required")
	}
	if req.RequestID.IsEmpty() {
		return fmt.Errorf("request ID is required")
	}
	if !req.PaidAmount.IsPositive() {
		return fmt.Errorf("paid amount must be positive")
	}
	return nil
}

// GetPaymentStatus gets the status of a payment
func (a *OttoPayAPIAdapter) GetPaymentStatus(ctx context.Context, requestID valueobjects.RequestID) (*services.PaymentStatusResult, error) {
	// Implementation would go here
	return nil, fmt.Errorf("not implemented")
}

func (a *OttoPayAPIAdapter) CancelPayment(ctx context.Context, requestID valueobjects.RequestID, reason string) error {
	// Implementation would go here
	return fmt.Errorf("not implemented")
}

func (a *OttoPayAPIAdapter) CreateTransaction(ctx context.Context, req *services.TransactionCreationRequest) (*entities.Transaction, error) {
	// Implementation would go here
	return nil, fmt.Errorf("not implemented")
}

func (a *OttoPayAPIAdapter) UpdateTransactionStatus(ctx context.Context, transactionID string, status entities.TransactionState) error {
	// Implementation would go here
	return nil
}

func (a *OttoPayAPIAdapter) GetTransactionHistory(ctx context.Context, filter *services.TransactionFilter) ([]*entities.Transaction, error) {
	// Implementation would go here
	return nil, fmt.Errorf("not implemented")
}

func (a *OttoPayAPIAdapter) CheckServiceHealth(ctx context.Context) (*services.ServiceHealthResult, error) {
	// Implementation would go here
	return nil, fmt.Errorf("not implemented")
}

func (a *OttoPayAPIAdapter) GetServiceMetrics(ctx context.Context) (*services.ServiceMetrics, error) {
	// Implementation would go here
	return nil, fmt.Errorf("not implemented")
}
