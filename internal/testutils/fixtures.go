package testutils

import (
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// TestFixtures provides test data fixtures
type TestFixtures struct{}

// NewTestFixtures creates a new test fixtures instance
func NewTestFixtures() *TestFixtures {
	return &TestFixtures{}
}

// Valid test data
const (
	ValidCompanyCode    = "12345"
	ValidCustomerNumber = "1234567890"
	ValidCurrencyCode   = "IDR"
	ValidAmount         = 100000.0
	ValidCustomerName   = "John Doe"
	ValidReference      = "REF123456789"
	ValidChannelType    = "API"
	ValidUsername       = "testuser"
	ValidPassword       = "testpass123"
	ValidAPIKey         = "test-api-key-123"
)

// CreateValidCompanyCode creates a valid company code for testing
func (f *TestFixtures) CreateValidCompanyCode() valueobjects.CompanyCode {
	code, _ := valueobjects.NewCompanyCode(ValidCompanyCode)
	return code
}

// CreateValidCustomerNumber creates a valid customer number for testing
func (f *TestFixtures) CreateValidCustomerNumber() valueobjects.CustomerNumber {
	number, _ := valueobjects.NewCustomerNumber(ValidCustomerNumber)
	return number
}

// CreateValidCurrencyCode creates a valid currency code for testing
func (f *TestFixtures) CreateValidCurrencyCode() valueobjects.CurrencyCode {
	currency, _ := valueobjects.NewCurrencyCode(ValidCurrencyCode)
	return currency
}

// CreateValidAmount creates a valid amount for testing
func (f *TestFixtures) CreateValidAmount() valueobjects.Amount {
	currency := f.CreateValidCurrencyCode()
	amount, _ := valueobjects.NewAmount(ValidAmount, currency)
	return amount
}

// CreateValidRequestID creates a valid request ID for testing
func (f *TestFixtures) CreateValidRequestID() valueobjects.RequestID {
	requestID, _ := valueobjects.GenerateInquiryRequestID()
	return requestID
}

// CreateValidPaymentRequestID creates a valid payment request ID for testing
func (f *TestFixtures) CreateValidPaymentRequestID() valueobjects.RequestID {
	requestID, _ := valueobjects.GeneratePaymentRequestID()
	return requestID
}

// CreateValidCustomer creates a valid customer entity for testing
func (f *TestFixtures) CreateValidCustomer() *entities.Customer {
	companyCode := f.CreateValidCompanyCode()
	customerNumber := f.CreateValidCustomerNumber()
	currencyCode := f.CreateValidCurrencyCode()
	amount := f.CreateValidAmount()
	
	customer, _ := entities.NewCustomer(
		companyCode,
		customerNumber,
		ValidCustomerName,
		currencyCode,
		amount,
	)
	
	return customer
}

// CreateValidToken creates a valid token entity for testing
func (f *TestFixtures) CreateValidToken() *entities.Token {
	token, _ := entities.NewToken(
		"test-token-id",
		entities.TokenTypeAccess,
		"test-token-value-123",
		ValidUsername,
		24*time.Hour,
	)
	
	return token
}

// CreateExpiredToken creates an expired token entity for testing
func (f *TestFixtures) CreateExpiredToken() *entities.Token {
	token, _ := entities.NewToken(
		"expired-token-id",
		entities.TokenTypeAccess,
		"expired-token-value-123",
		ValidUsername,
		-1*time.Hour, // Expired 1 hour ago
	)
	
	return token
}

// CreateValidPayment creates a valid payment entity for testing
func (f *TestFixtures) CreateValidPayment() *entities.Payment {
	companyCode := f.CreateValidCompanyCode()
	customerNumber := f.CreateValidCustomerNumber()
	requestID := f.CreateValidPaymentRequestID()
	currencyCode := f.CreateValidCurrencyCode()
	paidAmount := f.CreateValidAmount()
	totalAmount := f.CreateValidAmount()
	
	payment, _ := entities.NewPayment(
		"test-payment-id",
		companyCode,
		customerNumber,
		requestID,
		ValidCustomerName,
		currencyCode,
		paidAmount,
		totalAmount,
		ValidReference,
		ValidChannelType,
	)
	
	return payment
}

// CreateValidTransaction creates a valid transaction entity for testing
func (f *TestFixtures) CreateValidTransaction() *entities.Transaction {
	companyCode := f.CreateValidCompanyCode()
	customerNumber := f.CreateValidCustomerNumber()
	requestID := f.CreateValidRequestID()
	
	transaction, _ := entities.NewTransaction(
		"test-transaction-id",
		requestID,
		entities.TransactionTypeInquiry,
		companyCode,
		customerNumber,
		ValidChannelType,
	)
	
	return transaction
}

// CreateValidPaymentTransaction creates a valid payment transaction entity for testing
func (f *TestFixtures) CreateValidPaymentTransaction() *entities.Transaction {
	companyCode := f.CreateValidCompanyCode()
	customerNumber := f.CreateValidCustomerNumber()
	requestID := f.CreateValidPaymentRequestID()
	
	transaction, _ := entities.NewTransaction(
		"test-payment-transaction-id",
		requestID,
		entities.TransactionTypePayment,
		companyCode,
		customerNumber,
		ValidChannelType,
	)
	
	return transaction
}

// Invalid test data for negative testing

// CreateInvalidCompanyCode creates an invalid company code for testing
func (f *TestFixtures) CreateInvalidCompanyCode() string {
	return "INVALID"
}

// CreateInvalidCustomerNumber creates an invalid customer number for testing
func (f *TestFixtures) CreateInvalidCustomerNumber() string {
	return "INVALID"
}

// CreateInvalidCurrencyCode creates an invalid currency code for testing
func (f *TestFixtures) CreateInvalidCurrencyCode() string {
	return "INVALID"
}

// CreateInvalidAmount creates an invalid amount for testing
func (f *TestFixtures) CreateInvalidAmount() float64 {
	return -100.0
}

// CreateEmptyString creates an empty string for testing
func (f *TestFixtures) CreateEmptyString() string {
	return ""
}

// CreateTooLongString creates a string that exceeds typical length limits
func (f *TestFixtures) CreateTooLongString() string {
	return "This is a very long string that exceeds the typical length limits for most fields in the system and should cause validation errors when used in tests"
}

// Test scenarios

// CreateSuccessfulInquiryScenario creates test data for a successful inquiry
func (f *TestFixtures) CreateSuccessfulInquiryScenario() map[string]interface{} {
	return map[string]interface{}{
		"company_code":    f.CreateValidCompanyCode(),
		"customer_number": f.CreateValidCustomerNumber(),
		"request_id":      f.CreateValidRequestID(),
		"channel_type":    ValidChannelType,
		"customer":        f.CreateValidCustomer(),
		"expected_status": "00",
	}
}

// CreateFailedInquiryScenario creates test data for a failed inquiry
func (f *TestFixtures) CreateFailedInquiryScenario() map[string]interface{} {
	return map[string]interface{}{
		"company_code":    f.CreateValidCompanyCode(),
		"customer_number": f.CreateValidCustomerNumber(),
		"request_id":      f.CreateValidRequestID(),
		"channel_type":    ValidChannelType,
		"expected_status": "01",
		"expected_reason": "Customer not found",
	}
}

// CreateSuccessfulPaymentScenario creates test data for a successful payment
func (f *TestFixtures) CreateSuccessfulPaymentScenario() map[string]interface{} {
	return map[string]interface{}{
		"company_code":    f.CreateValidCompanyCode(),
		"customer_number": f.CreateValidCustomerNumber(),
		"request_id":      f.CreateValidPaymentRequestID(),
		"channel_type":    ValidChannelType,
		"customer_name":   ValidCustomerName,
		"currency_code":   f.CreateValidCurrencyCode(),
		"paid_amount":     f.CreateValidAmount(),
		"total_amount":    f.CreateValidAmount(),
		"reference":       ValidReference,
		"payment":         f.CreateValidPayment(),
		"expected_status": entities.PaymentStatusSuccess,
	}
}

// CreateFailedPaymentScenario creates test data for a failed payment
func (f *TestFixtures) CreateFailedPaymentScenario() map[string]interface{} {
	return map[string]interface{}{
		"company_code":    f.CreateValidCompanyCode(),
		"customer_number": f.CreateValidCustomerNumber(),
		"request_id":      f.CreateValidPaymentRequestID(),
		"channel_type":    ValidChannelType,
		"customer_name":   ValidCustomerName,
		"currency_code":   f.CreateValidCurrencyCode(),
		"paid_amount":     f.CreateValidAmount(),
		"total_amount":    f.CreateValidAmount(),
		"reference":       ValidReference,
		"expected_status": entities.PaymentStatusFailed,
		"expected_reason": "Payment processing failed",
	}
}

// CreateAuthenticationScenario creates test data for authentication
func (f *TestFixtures) CreateAuthenticationScenario() map[string]interface{} {
	return map[string]interface{}{
		"username":       ValidUsername,
		"password":       ValidPassword,
		"api_key":        ValidAPIKey,
		"token":          f.CreateValidToken(),
		"expected_success": true,
	}
}

// CreateFailedAuthenticationScenario creates test data for failed authentication
func (f *TestFixtures) CreateFailedAuthenticationScenario() map[string]interface{} {
	return map[string]interface{}{
		"username":         "invalid_user",
		"password":         "invalid_pass",
		"api_key":          "invalid_key",
		"expected_success": false,
		"expected_error":   "Authentication failed",
	}
}

// Utility methods for test data manipulation

// ModifyCustomerForTesting modifies a customer entity for specific test scenarios
func (f *TestFixtures) ModifyCustomerForTesting(customer *entities.Customer, modifications map[string]interface{}) *entities.Customer {
	modified := customer.Clone()
	
	if name, ok := modifications["name"].(string); ok {
		modified.Name = name
	}
	
	if additionalData, ok := modifications["additional_data"].(string); ok {
		modified.SetAdditionalData(additionalData)
	}
	
	return modified
}

// ModifyTokenForTesting modifies a token entity for specific test scenarios
func (f *TestFixtures) ModifyTokenForTesting(token *entities.Token, modifications map[string]interface{}) *entities.Token {
	modified := token.Clone()
	
	if expired, ok := modifications["expired"].(bool); ok && expired {
		modified.ExpiresAt = time.Now().Add(-1 * time.Hour)
	}
	
	if revoked, ok := modifications["revoked"].(bool); ok && revoked {
		modified.Revoke()
	}
	
	return modified
}

// CreateTestTimeStamp creates a consistent timestamp for testing
func (f *TestFixtures) CreateTestTimeStamp() time.Time {
	return time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
}

// CreateFutureTimeStamp creates a future timestamp for testing
func (f *TestFixtures) CreateFutureTimeStamp() time.Time {
	return time.Now().Add(24 * time.Hour)
}

// CreatePastTimeStamp creates a past timestamp for testing
func (f *TestFixtures) CreatePastTimeStamp() time.Time {
	return time.Now().Add(-24 * time.Hour)
}
