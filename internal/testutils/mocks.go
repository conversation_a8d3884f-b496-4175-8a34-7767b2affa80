package testutils

import (
	"context"
	"errors"
	"strings"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// MockTokenRepository provides a mock implementation of TokenRepository for testing
type MockTokenRepository struct {
	tokens map[string]*entities.<PERSON><PERSON>
	calls  map[string]int
}

// NewMockTokenRepository creates a new mock token repository
func NewMockTokenRepository() *MockTokenRepository {
	return &MockTokenRepository{
		tokens: make(map[string]*entities.Token),
		calls:  make(map[string]int),
	}
}

// Store stores a token in the mock repository
func (m *MockTokenRepository) Store(ctx context.Context, token *entities.Token) error {
	m.calls["Store"]++
	m.tokens[token.ID] = token.Clone()
	return nil
}

// GetByID retrieves a token by its ID
func (m *MockTokenRepository) GetByID(ctx context.Context, id string) (*entities.Token, error) {
	m.calls["GetByID"]++
	if token, exists := m.tokens[id]; exists {
		return token.Clone(), nil
	}
	return nil, repositories.NewTokenRepositoryError("GetByID", entities.ErrTokenNotFound)
}

// GetByUsername retrieves the latest valid token for a username
func (m *MockTokenRepository) GetByUsername(ctx context.Context, username string) (*entities.Token, error) {
	m.calls["GetByUsername"]++
	for _, token := range m.tokens {
		if token.Username == username {
			return token.Clone(), nil
		}
	}
	return nil, repositories.NewTokenRepositoryError("GetByUsername", entities.ErrTokenNotFound)
}

// GetByValue retrieves a token by its value
func (m *MockTokenRepository) GetByValue(ctx context.Context, value string) (*entities.Token, error) {
	m.calls["GetByValue"]++
	for _, token := range m.tokens {
		if token.Value == value {
			return token.Clone(), nil
		}
	}
	return nil, repositories.NewTokenRepositoryError("GetByValue", entities.ErrTokenNotFound)
}

// Update updates an existing token
func (m *MockTokenRepository) Update(ctx context.Context, token *entities.Token) error {
	m.calls["Update"]++
	if _, exists := m.tokens[token.ID]; exists {
		m.tokens[token.ID] = token.Clone()
		return nil
	}
	return repositories.NewTokenRepositoryError("Update", entities.ErrTokenNotFound)
}

// Delete removes a token from the repository
func (m *MockTokenRepository) Delete(ctx context.Context, id string) error {
	m.calls["Delete"]++
	if _, exists := m.tokens[id]; exists {
		delete(m.tokens, id)
		return nil
	}
	return repositories.NewTokenRepositoryError("Delete", entities.ErrTokenNotFound)
}

// DeleteByUsername removes all tokens for a username
func (m *MockTokenRepository) DeleteByUsername(ctx context.Context, username string) error {
	m.calls["DeleteByUsername"]++
	for id, token := range m.tokens {
		if token.Username == username {
			delete(m.tokens, id)
		}
	}
	return nil
}

// Revoke revokes a token
func (m *MockTokenRepository) Revoke(ctx context.Context, id string) error {
	m.calls["Revoke"]++
	if token, exists := m.tokens[id]; exists {
		token.Revoke()
		return nil
	}
	return repositories.NewTokenRepositoryError("Revoke", entities.ErrTokenNotFound)
}

// RevokeByUsername revokes all tokens for a username
func (m *MockTokenRepository) RevokeByUsername(ctx context.Context, username string) error {
	m.calls["RevokeByUsername"]++
	for _, token := range m.tokens {
		if token.Username == username {
			token.Revoke()
		}
	}
	return nil
}

// GetExpiredTokens retrieves all expired tokens
func (m *MockTokenRepository) GetExpiredTokens(ctx context.Context) ([]*entities.Token, error) {
	m.calls["GetExpiredTokens"]++
	var expired []*entities.Token
	for _, token := range m.tokens {
		if token.IsExpired() {
			expired = append(expired, token.Clone())
		}
	}
	return expired, nil
}

// GetTokensExpiringBefore retrieves tokens expiring before the specified time
func (m *MockTokenRepository) GetTokensExpiringBefore(ctx context.Context, before time.Time) ([]*entities.Token, error) {
	m.calls["GetTokensExpiringBefore"]++
	var expiring []*entities.Token
	for _, token := range m.tokens {
		if token.ExpiresAt.Before(before) {
			expiring = append(expiring, token.Clone())
		}
	}
	return expiring, nil
}

// CleanupExpiredTokens removes expired tokens from the repository
func (m *MockTokenRepository) CleanupExpiredTokens(ctx context.Context) (int, error) {
	m.calls["CleanupExpiredTokens"]++
	count := 0
	for id, token := range m.tokens {
		if token.IsExpired() {
			delete(m.tokens, id)
			count++
		}
	}
	return count, nil
}

// IsTokenValid checks if a token is valid
func (m *MockTokenRepository) IsTokenValid(ctx context.Context, value string) (bool, error) {
	m.calls["IsTokenValid"]++
	for _, token := range m.tokens {
		if token.Value == value {
			return token.IsValid(), nil
		}
	}
	return false, nil
}

// GetTokenStats returns statistics about tokens in the repository
func (m *MockTokenRepository) GetTokenStats(ctx context.Context) (*repositories.TokenStats, error) {
	m.calls["GetTokenStats"]++
	stats := &repositories.TokenStats{
		TotalTokens: len(m.tokens),
	}

	uniqueUsers := make(map[string]bool)
	for _, token := range m.tokens {
		uniqueUsers[token.Username] = true
		if token.IsValid() {
			stats.ActiveTokens++
		} else if token.IsExpired() {
			stats.ExpiredTokens++
		}
		if token.IsRevoked {
			stats.RevokedTokens++
		}
	}
	stats.UniqueUsers = len(uniqueUsers)

	return stats, nil
}

// ListTokensByUsername lists all tokens for a username with pagination
func (m *MockTokenRepository) ListTokensByUsername(ctx context.Context, username string, limit, offset int) ([]*entities.Token, error) {
	m.calls["ListTokensByUsername"]++
	var userTokens []*entities.Token
	for _, token := range m.tokens {
		if token.Username == username {
			userTokens = append(userTokens, token.Clone())
		}
	}

	// Apply pagination
	start := offset
	if start > len(userTokens) {
		start = len(userTokens)
	}

	end := start + limit
	if end > len(userTokens) {
		end = len(userTokens)
	}

	return userTokens[start:end], nil
}

// CountTokensByUsername counts tokens for a username
func (m *MockTokenRepository) CountTokensByUsername(ctx context.Context, username string) (int, error) {
	m.calls["CountTokensByUsername"]++
	count := 0
	for _, token := range m.tokens {
		if token.Username == username {
			count++
		}
	}
	return count, nil
}

// GetActiveTokensCount returns the count of active tokens
func (m *MockTokenRepository) GetActiveTokensCount(ctx context.Context) (int, error) {
	m.calls["GetActiveTokensCount"]++
	count := 0
	for _, token := range m.tokens {
		if token.IsValid() {
			count++
		}
	}
	return count, nil
}

// Test helper methods

// GetCallCount returns the number of times a method was called
func (m *MockTokenRepository) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all tokens and call counts
func (m *MockTokenRepository) Reset() {
	m.tokens = make(map[string]*entities.Token)
	m.calls = make(map[string]int)
}

// AddToken adds a token directly to the mock repository
func (m *MockTokenRepository) AddToken(token *entities.Token) {
	m.tokens[token.ID] = token.Clone()
}

// MockOttoPayService provides a mock implementation of OttoPayService for testing
type MockOttoPayService struct {
	authResults    map[string]*services.AuthenticationResult
	inquiryResults map[string]*services.CustomerInquiryResult
	paymentResults map[string]*services.PaymentProcessingResult
	calls          map[string]int
	shouldFail     map[string]bool
}

// NewMockOttoPayService creates a new mock OttoPay service
func NewMockOttoPayService() *MockOttoPayService {
	return &MockOttoPayService{
		authResults:    make(map[string]*services.AuthenticationResult),
		inquiryResults: make(map[string]*services.CustomerInquiryResult),
		paymentResults: make(map[string]*services.PaymentProcessingResult),
		calls:          make(map[string]int),
		shouldFail:     make(map[string]bool),
	}
}

// AuthenticateUser authenticates a user and returns a token
func (m *MockOttoPayService) AuthenticateUser(ctx context.Context, username, password string) (*services.AuthenticationResult, error) {
	m.calls["AuthenticateUser"]++

	if m.shouldFail["AuthenticateUser"] {
		return nil, services.NewOttoPayServiceError("AuthenticateUser", "AUTH_FAILED", "Authentication failed", nil)
	}

	key := username + ":" + password
	if result, exists := m.authResults[key]; exists {
		return result, nil
	}

	// Default successful authentication
	fixtures := NewTestFixtures()
	return &services.AuthenticationResult{
		Token:     fixtures.CreateValidToken(),
		ExpiresIn: 24 * time.Hour,
		Success:   true,
		Message:   "Authentication successful",
	}, nil
}

// RefreshToken refreshes an existing token
func (m *MockOttoPayService) RefreshToken(ctx context.Context, token string) (*services.AuthenticationResult, error) {
	m.calls["RefreshToken"]++

	if m.shouldFail["RefreshToken"] {
		return nil, services.NewOttoPayServiceError("RefreshToken", "REFRESH_FAILED", "Token refresh failed", nil)
	}

	fixtures := NewTestFixtures()
	return &services.AuthenticationResult{
		Token:     fixtures.CreateValidToken(),
		ExpiresIn: 24 * time.Hour,
		Success:   true,
		Message:   "Token refreshed successfully",
	}, nil
}

// ValidateToken validates a token
func (m *MockOttoPayService) ValidateToken(ctx context.Context, token string) (*services.TokenValidationResult, error) {
	m.calls["ValidateToken"]++

	if m.shouldFail["ValidateToken"] {
		return &services.TokenValidationResult{
			Valid:  false,
			Reason: "Token validation failed",
		}, nil
	}

	return &services.TokenValidationResult{
		Valid:     true,
		ExpiresIn: 24 * time.Hour,
	}, nil
}

// RevokeToken revokes a token
func (m *MockOttoPayService) RevokeToken(ctx context.Context, token string) error {
	m.calls["RevokeToken"]++

	if m.shouldFail["RevokeToken"] {
		return services.NewOttoPayServiceError("RevokeToken", "REVOKE_FAILED", "Token revocation failed", nil)
	}

	return nil
}

// InquireCustomer performs customer inquiry
func (m *MockOttoPayService) InquireCustomer(ctx context.Context, req *services.CustomerInquiryRequest) (*services.CustomerInquiryResult, error) {
	m.calls["InquireCustomer"]++

	if m.shouldFail["InquireCustomer"] {
		return nil, services.NewOttoPayServiceError("InquireCustomer", "INQUIRY_FAILED", "Customer inquiry failed", nil)
	}

	key := req.CompanyCode.String() + ":" + req.CustomerNumber.String()
	if result, exists := m.inquiryResults[key]; exists {
		return result, nil
	}

	// Default successful inquiry
	fixtures := NewTestFixtures()
	return &services.CustomerInquiryResult{
		Success:       true,
		Customer:      fixtures.CreateValidCustomer(),
		InquiryStatus: "00",
		InquiryReason: entities.PaymentFlagReason{
			Indonesian: "Berhasil",
			English:    "Success",
		},
		Message:      "Customer inquiry successful",
		ResponseTime: 100 * time.Millisecond,
	}, nil
}

// ProcessPayment processes a payment
func (m *MockOttoPayService) ProcessPayment(ctx context.Context, req *services.PaymentProcessingRequest) (*services.PaymentProcessingResult, error) {
	m.calls["ProcessPayment"]++

	if m.shouldFail["ProcessPayment"] {
		return nil, services.NewOttoPayServiceError("ProcessPayment", "PAYMENT_FAILED", "Payment processing failed", nil)
	}

	key := req.CompanyCode.String() + ":" + req.CustomerNumber.String() + ":" + req.RequestID.String()
	if result, exists := m.paymentResults[key]; exists {
		return result, nil
	}

	// Default successful payment
	fixtures := NewTestFixtures()
	return &services.PaymentProcessingResult{
		Success: true,
		Payment: fixtures.CreateValidPayment(),
		Status:  entities.PaymentStatusSuccess,
		Reason: entities.PaymentFlagReason{
			Indonesian: "Pembayaran berhasil",
			English:    "Payment successful",
		},
		TransactionID: "test-transaction-id",
		Message:       "Payment processed successfully",
		ResponseTime:  200 * time.Millisecond,
	}, nil
}

// Test helper methods for MockOttoPayService

// SetAuthResult sets a specific authentication result for testing
func (m *MockOttoPayService) SetAuthResult(username, password string, result *services.AuthenticationResult) {
	key := username + ":" + password
	m.authResults[key] = result
}

// SetInquiryResult sets a specific inquiry result for testing
func (m *MockOttoPayService) SetInquiryResult(companyCode, customerNumber string, result *services.CustomerInquiryResult) {
	key := companyCode + ":" + customerNumber
	m.inquiryResults[key] = result
}

// SetPaymentResult sets a specific payment result for testing
func (m *MockOttoPayService) SetPaymentResult(companyCode, customerNumber, requestID string, result *services.PaymentProcessingResult) {
	key := companyCode + ":" + customerNumber + ":" + requestID
	m.paymentResults[key] = result
}

// SetShouldFail sets whether a method should fail
func (m *MockOttoPayService) SetShouldFail(method string, shouldFail bool) {
	m.shouldFail[method] = shouldFail
}

// GetCallCount returns the number of times a method was called
func (m *MockOttoPayService) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all results and call counts
func (m *MockOttoPayService) Reset() {
	m.authResults = make(map[string]*services.AuthenticationResult)
	m.inquiryResults = make(map[string]*services.CustomerInquiryResult)
	m.paymentResults = make(map[string]*services.PaymentProcessingResult)
	m.calls = make(map[string]int)
	m.shouldFail = make(map[string]bool)
}

// Placeholder implementations for interface compliance
func (m *MockOttoPayService) ValidateInquiryRequest(ctx context.Context, req *services.CustomerInquiryRequest) error {
	return nil
}

func (m *MockOttoPayService) ValidatePaymentRequest(ctx context.Context, req *services.PaymentProcessingRequest) error {
	return nil
}

func (m *MockOttoPayService) GetPaymentStatus(ctx context.Context, requestID valueobjects.RequestID) (*services.PaymentStatusResult, error) {
	return nil, nil
}

func (m *MockOttoPayService) CancelPayment(ctx context.Context, requestID valueobjects.RequestID, reason string) error {
	return nil
}

func (m *MockOttoPayService) CreateTransaction(ctx context.Context, req *services.TransactionCreationRequest) (*entities.Transaction, error) {
	fixtures := NewTestFixtures()
	return fixtures.CreateValidTransaction(), nil
}

func (m *MockOttoPayService) UpdateTransactionStatus(ctx context.Context, transactionID string, status entities.TransactionState) error {
	return nil
}

func (m *MockOttoPayService) GetTransactionHistory(ctx context.Context, filter *services.TransactionFilter) ([]*entities.Transaction, error) {
	return nil, nil
}

func (m *MockOttoPayService) CheckServiceHealth(ctx context.Context) (*services.ServiceHealthResult, error) {
	return nil, nil
}

func (m *MockOttoPayService) GetServiceMetrics(ctx context.Context) (*services.ServiceMetrics, error) {
	return nil, nil
}

// MockAuditRepository provides a mock implementation of AuditRepository for testing
type MockAuditRepository struct {
	events []repositories.AuditEvent
	calls  map[string]int
}

// NewMockAuditRepository creates a new mock audit repository
func NewMockAuditRepository() *MockAuditRepository {
	return &MockAuditRepository{
		events: make([]repositories.AuditEvent, 0),
		calls:  make(map[string]int),
	}
}

// LogEvent logs an audit event
func (m *MockAuditRepository) LogEvent(ctx context.Context, event *repositories.AuditEvent) error {
	m.calls["LogEvent"]++
	m.events = append(m.events, *event)
	return nil
}

// GetCallCount returns the number of times a method was called
func (m *MockAuditRepository) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all events and call counts
func (m *MockAuditRepository) Reset() {
	m.events = make([]repositories.AuditEvent, 0)
	m.calls = make(map[string]int)
}

// GetAllEvents returns all logged events
func (m *MockAuditRepository) GetAllEvents() []repositories.AuditEvent {
	return m.events
}

// Placeholder implementations for interface compliance
func (m *MockAuditRepository) LogEvents(ctx context.Context, events []*repositories.AuditEvent) error {
	return nil
}

func (m *MockAuditRepository) GetEventByID(ctx context.Context, id string) (*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetEvents(ctx context.Context, filter *repositories.AuditFilter, limit, offset int) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) CountEvents(ctx context.Context, filter *repositories.AuditFilter) (int, error) {
	return 0, nil
}

func (m *MockAuditRepository) GetEventsByRequestID(ctx context.Context, requestID valueobjects.RequestID) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetEventsByCustomer(ctx context.Context, companyCode valueobjects.CompanyCode, customerNumber valueobjects.CustomerNumber) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetEventsByType(ctx context.Context, eventType repositories.AuditEventType) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetEventsByLevel(ctx context.Context, level repositories.AuditLevel) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetEventsByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*repositories.AuditEvent, error) {
	return nil, nil
}

func (m *MockAuditRepository) DeleteOldEvents(ctx context.Context, olderThan time.Duration) (int, error) {
	return 0, nil
}

func (m *MockAuditRepository) GetAuditStats(ctx context.Context) (*repositories.AuditStats, error) {
	return nil, nil
}

func (m *MockAuditRepository) GetErrorSummary(ctx context.Context, startDate, endDate time.Time) (*repositories.ErrorSummary, error) {
	return nil, nil
}

// MockTokenCacheRepository provides a mock implementation of TokenCacheRepository for testing
type MockTokenCacheRepository struct {
	cache map[string]*entities.Token
	calls map[string]int
}

// NewMockTokenCacheRepository creates a new mock token cache repository
func NewMockTokenCacheRepository() *MockTokenCacheRepository {
	return &MockTokenCacheRepository{
		cache: make(map[string]*entities.Token),
		calls: make(map[string]int),
	}
}

// Get retrieves a token from cache
func (m *MockTokenCacheRepository) Get(ctx context.Context, key string) (*entities.Token, error) {
	m.calls["Get"]++
	if token, exists := m.cache[key]; exists {
		return token.Clone(), nil
	}
	return nil, repositories.NewTokenRepositoryError("Get", entities.ErrTokenNotFound)
}

// Set stores a token in cache
func (m *MockTokenCacheRepository) Set(ctx context.Context, key string, token *entities.Token, ttl time.Duration) error {
	m.calls["Set"]++
	m.cache[key] = token.Clone()
	return nil
}

// Delete removes a token from cache
func (m *MockTokenCacheRepository) Delete(ctx context.Context, key string) error {
	m.calls["Delete"]++
	delete(m.cache, key)
	return nil
}

// Exists checks if a key exists in cache
func (m *MockTokenCacheRepository) Exists(ctx context.Context, key string) (bool, error) {
	m.calls["Exists"]++
	_, exists := m.cache[key]
	return exists, nil
}

// Clear removes all tokens from cache
func (m *MockTokenCacheRepository) Clear(ctx context.Context) error {
	m.calls["Clear"]++
	m.cache = make(map[string]*entities.Token)
	return nil
}

// GetCacheStats returns cache statistics
func (m *MockTokenCacheRepository) GetCacheStats(ctx context.Context) (*repositories.CacheStats, error) {
	m.calls["GetCacheStats"]++
	return &repositories.CacheStats{
		TotalKeys: len(m.cache),
		HitCount:  0,
		MissCount: 0,
	}, nil
}

// GetTTL returns the TTL for a key
func (m *MockTokenCacheRepository) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	m.calls["GetTTL"]++
	if _, exists := m.cache[key]; exists {
		return 24 * time.Hour, nil // Mock TTL
	}
	return 0, repositories.NewTokenRepositoryError("GetTTL", entities.ErrTokenNotFound)
}

// SetTTL sets the TTL for a key
func (m *MockTokenCacheRepository) SetTTL(ctx context.Context, key string, ttl time.Duration) error {
	m.calls["SetTTL"]++
	// Mock implementation - just check if key exists
	if _, exists := m.cache[key]; !exists {
		return repositories.NewTokenRepositoryError("SetTTL", entities.ErrTokenNotFound)
	}
	return nil
}

// Keys returns all keys in cache
func (m *MockTokenCacheRepository) Keys(ctx context.Context, pattern string) ([]string, error) {
	m.calls["Keys"]++
	var keys []string
	for key := range m.cache {
		keys = append(keys, key)
	}
	return keys, nil
}

// GetCallCount returns the number of times a method was called
func (m *MockTokenCacheRepository) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all cache and call counts
func (m *MockTokenCacheRepository) Reset() {
	m.cache = make(map[string]*entities.Token)
	m.calls = make(map[string]int)
}

// SetToken adds a token directly to the mock cache
func (m *MockTokenCacheRepository) SetToken(key string, token *entities.Token) {
	m.cache[key] = token.Clone()
}

// MockValidationService provides a mock implementation of ValidationService for testing
type MockValidationService struct {
	calls      map[string]int
	shouldFail map[string]bool
}

// NewMockValidationService creates a new mock validation service
func NewMockValidationService() *MockValidationService {
	return &MockValidationService{
		calls:      make(map[string]int),
		shouldFail: make(map[string]bool),
	}
}

// ValidateAuthenticationRequest validates authentication request
func (m *MockValidationService) ValidateAuthenticationRequest(ctx context.Context, username, password string) error {
	m.calls["ValidateAuthenticationRequest"]++
	if m.shouldFail["ValidateAuthenticationRequest"] {
		return errors.New("validation failed")
	}
	return nil
}

// SetShouldFail sets whether a method should fail
func (m *MockValidationService) SetShouldFail(method string, shouldFail bool) {
	m.shouldFail[method] = shouldFail
}

// GetCallCount returns the number of times a method was called
func (m *MockValidationService) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all call counts and failure settings
func (m *MockValidationService) Reset() {
	m.calls = make(map[string]int)
	m.shouldFail = make(map[string]bool)
}

// MockEncryptionService provides a mock implementation of EncryptionService for testing
type MockEncryptionService struct {
	calls      map[string]int
	shouldFail map[string]bool
}

// NewMockEncryptionService creates a new mock encryption service
func NewMockEncryptionService() *MockEncryptionService {
	return &MockEncryptionService{
		calls:      make(map[string]int),
		shouldFail: make(map[string]bool),
	}
}

// Encrypt encrypts data
func (m *MockEncryptionService) Encrypt(ctx context.Context, data string) (string, error) {
	m.calls["Encrypt"]++
	if m.shouldFail["Encrypt"] {
		return "", errors.New("encryption failed")
	}
	return "encrypted_" + data, nil
}

// Decrypt decrypts data
func (m *MockEncryptionService) Decrypt(ctx context.Context, encryptedData string) (string, error) {
	m.calls["Decrypt"]++
	if m.shouldFail["Decrypt"] {
		return "", errors.New("decryption failed")
	}
	// Simple mock decryption
	if strings.HasPrefix(encryptedData, "encrypted_") {
		return strings.TrimPrefix(encryptedData, "encrypted_"), nil
	}
	return encryptedData, nil
}

// SetShouldFail sets whether a method should fail
func (m *MockEncryptionService) SetShouldFail(method string, shouldFail bool) {
	m.shouldFail[method] = shouldFail
}

// GetCallCount returns the number of times a method was called
func (m *MockEncryptionService) GetCallCount(method string) int {
	return m.calls[method]
}

// Reset clears all call counts and failure settings
func (m *MockEncryptionService) Reset() {
	m.calls = make(map[string]int)
	m.shouldFail = make(map[string]bool)
}
