package inquiry

import (
	"context"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// CustomerInquiryUseCase handles customer inquiry operations
type CustomerInquiryUseCase struct {
	ottoPayService    services.OttoPayService
	validationService services.ValidationService
	auditRepo         repositories.AuditRepository
	tokenRepo         repositories.TokenRepository
}

// NewCustomerInquiryUseCase creates a new customer inquiry use case
func NewCustomerInquiryUseCase(
	ottoPayService services.OttoPayService,
	validationService services.ValidationService,
	auditRepo repositories.AuditRepository,
	tokenRepo repositories.TokenRepository,
) *CustomerInquiryUseCase {
	return &CustomerInquiryUseCase{
		ottoPayService:    ottoPayService,
		validationService: validationService,
		auditRepo:         auditRepo,
		tokenRepo:         tokenRepo,
	}
}

// CustomerInquiryRequest represents a customer inquiry request
type CustomerInquiryRequest struct {
	CompanyCode     valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber  valueobjects.CustomerNumber `json:"customer_number"`
	RequestID       valueobjects.RequestID      `json:"request_id"`
	ChannelType     string                      `json:"channel_type"`
	TransactionDate *time.Time                  `json:"transaction_date,omitempty"`
	AdditionalData  string                      `json:"additional_data,omitempty"`

	// Authentication
	Token string `json:"token"`

	// Optional fields for audit
	SourceIP  string `json:"source_ip,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// CustomerInquiryResponse represents a customer inquiry response
type CustomerInquiryResponse struct {
	Success       bool                       `json:"success"`
	Customer      *entities.Customer         `json:"customer,omitempty"`
	InquiryStatus string                     `json:"inquiry_status"`
	InquiryReason entities.PaymentFlagReason `json:"inquiry_reason"`
	Message       string                     `json:"message"`
	ResponseTime  time.Duration              `json:"response_time"`
	RequestID     string                     `json:"request_id"`
	TransactionID string                     `json:"transaction_id,omitempty"`
}

// Execute executes the customer inquiry use case
func (uc *CustomerInquiryUseCase) Execute(ctx context.Context, req *CustomerInquiryRequest) (*CustomerInquiryResponse, error) {
	startTime := time.Now()

	// Log audit event for inquiry request
	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-REQUEST",
		Type:           repositories.AuditEventTypeInquiryRequest,
		Level:          repositories.AuditLevelInfo,
		Message:        "Customer inquiry request initiated",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		RequestData: map[string]interface{}{
			"company_code":    req.CompanyCode.String(),
			"customer_number": req.CustomerNumber.String(),
			"channel_type":    req.ChannelType,
			"additional_data": req.AdditionalData,
		},
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to log inquiry audit event: %v\n", err)
	}

	// Validate token
	if req.Token == "" {
		uc.logInquiryError(ctx, req, "MISSING_TOKEN", "Authentication token is required", startTime)
		return &CustomerInquiryResponse{
			Success:       false,
			InquiryStatus: "01",
			InquiryReason: entities.PaymentFlagReason{
				Indonesian: "Token autentikasi diperlukan",
				English:    "Authentication token is required",
			},
			Message:      "Authentication token is required",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Get and validate token
	token, err := uc.tokenRepo.GetByValue(ctx, req.Token)
	if err != nil || token == nil || !token.IsValid() {
		uc.logInquiryError(ctx, req, "INVALID_TOKEN", "Invalid or expired token", startTime)
		return &CustomerInquiryResponse{
			Success:       false,
			InquiryStatus: "01",
			InquiryReason: entities.PaymentFlagReason{
				Indonesian: "Token tidak valid atau telah kedaluwarsa",
				English:    "Invalid or expired token",
			},
			Message:      "Invalid or expired token",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Validate inquiry request
	serviceReq := &services.CustomerInquiryRequest{
		CompanyCode:     req.CompanyCode,
		CustomerNumber:  req.CustomerNumber,
		RequestID:       req.RequestID,
		ChannelType:     req.ChannelType,
		TransactionDate: req.TransactionDate,
		AdditionalData:  req.AdditionalData,
	}

	if err := uc.validationService.ValidateInquiryRequest(ctx, serviceReq); err != nil {
		uc.logInquiryError(ctx, req, "VALIDATION_FAILED", err.Error(), startTime)
		return &CustomerInquiryResponse{
			Success:       false,
			InquiryStatus: "01",
			InquiryReason: entities.PaymentFlagReason{
				Indonesian: "Permintaan tidak valid",
				English:    "Invalid request",
			},
			Message:      "Validation failed: " + err.Error(),
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Check request ID uniqueness
	if err := uc.validationService.ValidateRequestIDUniqueness(ctx, req.RequestID); err != nil {
		uc.logInquiryError(ctx, req, "DUPLICATE_REQUEST", err.Error(), startTime)
		return &CustomerInquiryResponse{
			Success:       false,
			InquiryStatus: "01",
			InquiryReason: entities.PaymentFlagReason{
				Indonesian: "ID permintaan sudah digunakan",
				English:    "Request ID already used",
			},
			Message:      "Duplicate request ID",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Create transaction record
	transactionReq := &services.TransactionCreationRequest{
		RequestID:      req.RequestID,
		Type:           entities.TransactionTypeInquiry,
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		ChannelType:    req.ChannelType,
		Metadata: map[string]interface{}{
			"source_ip":       req.SourceIP,
			"user_agent":      req.UserAgent,
			"additional_data": req.AdditionalData,
			"username":        token.Username,
		},
	}

	transaction, err := uc.ottoPayService.CreateTransaction(ctx, transactionReq)
	if err != nil {
		uc.logInquiryError(ctx, req, "TRANSACTION_CREATION_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Start transaction processing
	if err := transaction.StartProcessing(); err != nil {
		uc.logInquiryError(ctx, req, "TRANSACTION_START_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to start transaction processing: %w", err)
	}

	// Update transaction status
	if err := uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateProcessing); err != nil {
		// Log error but continue
		fmt.Printf("Failed to update transaction status: %v\n", err)
	}

	// Perform customer inquiry
	inquiryResult, err := uc.ottoPayService.InquireCustomer(ctx, serviceReq)
	if err != nil {
		// Mark transaction as failed
		transaction.Fail("INQUIRY_ERROR", err.Error())
		uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateFailed)

		uc.logInquiryError(ctx, req, "INQUIRY_FAILED", err.Error(), startTime)
		return &CustomerInquiryResponse{
			Success:       false,
			InquiryStatus: "01",
			InquiryReason: entities.PaymentFlagReason{
				Indonesian: "Gagal melakukan inquiry",
				English:    "Inquiry failed",
			},
			Message:       "Inquiry failed: " + err.Error(),
			ResponseTime:  time.Since(startTime),
			RequestID:     req.RequestID.String(),
			TransactionID: transaction.ID,
		}, err
	}

	// Complete transaction
	if err := transaction.Complete(); err != nil {
		// Log error but continue
		fmt.Printf("Failed to complete transaction: %v\n", err)
	} else {
		uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateCompleted)
	}

	// Log successful inquiry
	responseTime := time.Since(startTime)
	uc.logInquirySuccess(ctx, req, inquiryResult, responseTime, transaction.ID)

	// Map inquiry status
	var inquiryStatus string
	if inquiryResult.Success {
		inquiryStatus = "00" // Success
	} else {
		inquiryStatus = "01" // Failed
	}

	return &CustomerInquiryResponse{
		Success:       inquiryResult.Success,
		Customer:      inquiryResult.Customer,
		InquiryStatus: inquiryStatus,
		InquiryReason: inquiryResult.InquiryReason,
		Message:       inquiryResult.Message,
		ResponseTime:  responseTime,
		RequestID:     req.RequestID.String(),
		TransactionID: transaction.ID,
	}, nil
}

// logInquirySuccess logs successful inquiry operations
func (uc *CustomerInquiryUseCase) logInquirySuccess(ctx context.Context, req *CustomerInquiryRequest, result *services.CustomerInquiryResult, duration time.Duration, transactionID string) {
	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-SUCCESS",
		Type:           repositories.AuditEventTypeInquiryResponse,
		Level:          repositories.AuditLevelInfo,
		Message:        "Customer inquiry completed successfully",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		ResponseData: map[string]interface{}{
			"success":        result.Success,
			"inquiry_status": result.InquiryStatus,
			"message":        result.Message,
			"transaction_id": transactionID,
		},
		Duration:    &duration,
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if result.Customer != nil {
		auditEvent.ResponseData["customer_name"] = result.Customer.Name
		auditEvent.ResponseData["total_amount"] = result.Customer.TotalAmount.String()
		auditEvent.ResponseData["currency_code"] = result.Customer.CurrencyCode.String()
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log inquiry success audit event: %v\n", err)
	}
}

// logInquiryError logs inquiry operation errors
func (uc *CustomerInquiryUseCase) logInquiryError(ctx context.Context, req *CustomerInquiryRequest, errorCode, errorMessage string, startTime time.Time) {
	duration := time.Since(startTime)

	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-ERROR",
		Type:           repositories.AuditEventTypeAPIError,
		Level:          repositories.AuditLevelError,
		Message:        "Customer inquiry failed",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		ErrorCode:      &errorCode,
		ErrorMessage:   &errorMessage,
		Duration:       &duration,
		SourceIP:       &req.SourceIP,
		UserAgent:      &req.UserAgent,
		ChannelType:    &req.ChannelType,
		Timestamp:      time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log inquiry error audit event: %v\n", err)
	}
}
