package inquiry

import (
	"context"
	"errors"
	"testing"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// Mock implementations
type mockOttoPayService struct {
	inquiryResult *entities.Customer
	inquiryError  error
}

func (m *mockOttoPayService) InquireCustomer(ctx context.Context, companyCode valueobjects.CompanyCode, customerNumber valueobjects.CustomerNumber) (*entities.Customer, error) {
	return m.inquiryResult, m.inquiryError
}

func (m *mockOttoPayService) ProcessPayment(ctx context.Context, payment *entities.Payment) error {
	return nil
}

type mockValidationService struct {
	validateError error
}

func (m *mockValidationService) ValidateRequest(ctx context.Context, req interface{}) error {
	return m.validateError
}

func (m *mockValidationService) ValidateBusinessRules(ctx context.Context, data interface{}) error {
	return nil
}

type mockAuditRepository struct {
	events []repositories.AuditEvent
}

func (m *mockAuditRepository) LogEvent(ctx context.Context, event *repositories.AuditEvent) error {
	m.events = append(m.events, *event)
	return nil
}

func (m *mockAuditRepository) GetEvents(ctx context.Context, filter repositories.AuditFilter) ([]repositories.AuditEvent, error) {
	return m.events, nil
}

type mockTokenRepository struct {
	tokens map[string]*entities.Token
}

func (m *mockTokenRepository) Store(ctx context.Context, token *entities.Token) error {
	if m.tokens == nil {
		m.tokens = make(map[string]*entities.Token)
	}
	m.tokens[token.Value] = token
	return nil
}

func (m *mockTokenRepository) GetByValue(ctx context.Context, value string) (*entities.Token, error) {
	if token, exists := m.tokens[value]; exists {
		return token, nil
	}
	return nil, errors.New("token not found")
}

func (m *mockTokenRepository) GetByUsername(ctx context.Context, username string) (*entities.Token, error) {
	for _, token := range m.tokens {
		if token.Username == username {
			return token, nil
		}
	}
	return nil, errors.New("token not found")
}

func (m *mockTokenRepository) Delete(ctx context.Context, value string) error {
	delete(m.tokens, value)
	return nil
}

func (m *mockTokenRepository) DeleteExpired(ctx context.Context) error {
	now := time.Now()
	for value, token := range m.tokens {
		if token.ExpiresAt.Before(now) {
			delete(m.tokens, value)
		}
	}
	return nil
}

func TestCustomerInquiryUseCase_Execute(t *testing.T) {
	// Setup test data
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("56751590099")
	requestID, _ := valueobjects.NewRequestID("REQ123456789")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	validCustomer, _ := entities.NewCustomer(
		companyCode,
		customerNumber,
		"John Doe",
		currencyCode,
		totalAmount,
	)

	validToken := &entities.Token{
		Value:     "valid-token-123",
		Username:  "testuser",
		ExpiresAt: time.Now().Add(1 * time.Hour),
		IssuedAt:  time.Now(),
		IsActive:  true,
	}

	tests := []struct {
		name                string
		request             *CustomerInquiryRequest
		ottoPayResult       *entities.Customer
		ottoPayError        error
		tokenExists         bool
		expectedSuccess     bool
		expectedStatus      string
		expectedMessage     string
	}{
		{
			name: "Successful inquiry",
			request: &CustomerInquiryRequest{
				CompanyCode:    companyCode,
				CustomerNumber: customerNumber,
				RequestID:      requestID,
				ChannelType:    "API",
				Token:          "valid-token-123",
				SourceIP:       "***********",
				UserAgent:      "TestAgent/1.0",
			},
			ottoPayResult:   validCustomer,
			ottoPayError:    nil,
			tokenExists:     true,
			expectedSuccess: true,
			expectedStatus:  "SUCCESS",
			expectedMessage: "Customer inquiry completed successfully",
		},
		{
			name: "Missing token",
			request: &CustomerInquiryRequest{
				CompanyCode:    companyCode,
				CustomerNumber: customerNumber,
				RequestID:      requestID,
				ChannelType:    "API",
				Token:          "",
				SourceIP:       "***********",
				UserAgent:      "TestAgent/1.0",
			},
			ottoPayResult:   nil,
			ottoPayError:    nil,
			tokenExists:     false,
			expectedSuccess: false,
			expectedStatus:  "FAILED",
			expectedMessage: "Authentication token is required",
		},
		{
			name: "Invalid token",
			request: &CustomerInquiryRequest{
				CompanyCode:    companyCode,
				CustomerNumber: customerNumber,
				RequestID:      requestID,
				ChannelType:    "API",
				Token:          "invalid-token",
				SourceIP:       "***********",
				UserAgent:      "TestAgent/1.0",
			},
			ottoPayResult:   nil,
			ottoPayError:    nil,
			tokenExists:     false,
			expectedSuccess: false,
			expectedStatus:  "FAILED",
			expectedMessage: "Invalid or expired authentication token",
		},
		{
			name: "Customer not found",
			request: &CustomerInquiryRequest{
				CompanyCode:    companyCode,
				CustomerNumber: customerNumber,
				RequestID:      requestID,
				ChannelType:    "API",
				Token:          "valid-token-123",
				SourceIP:       "***********",
				UserAgent:      "TestAgent/1.0",
			},
			ottoPayResult:   nil,
			ottoPayError:    errors.New("customer not found"),
			tokenExists:     true,
			expectedSuccess: false,
			expectedStatus:  "FAILED",
			expectedMessage: "Customer not found",
		},
		{
			name: "OttoPay service error",
			request: &CustomerInquiryRequest{
				CompanyCode:    companyCode,
				CustomerNumber: customerNumber,
				RequestID:      requestID,
				ChannelType:    "API",
				Token:          "valid-token-123",
				SourceIP:       "***********",
				UserAgent:      "TestAgent/1.0",
			},
			ottoPayResult:   nil,
			ottoPayError:    errors.New("service unavailable"),
			tokenExists:     true,
			expectedSuccess: false,
			expectedStatus:  "FAILED",
			expectedMessage: "Service temporarily unavailable",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			ottoPayService := &mockOttoPayService{
				inquiryResult: tt.ottoPayResult,
				inquiryError:  tt.ottoPayError,
			}
			validationService := &mockValidationService{}
			auditRepo := &mockAuditRepository{}
			tokenRepo := &mockTokenRepository{}

			if tt.tokenExists {
				tokenRepo.Store(context.Background(), validToken)
			}

			// Create use case
			uc := NewCustomerInquiryUseCase(
				ottoPayService,
				validationService,
				auditRepo,
				tokenRepo,
			)

			// Execute
			ctx := context.Background()
			response, err := uc.Execute(ctx, tt.request)

			// Assertions
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if response == nil {
				t.Fatal("Response should not be nil")
			}

			if response.Success != tt.expectedSuccess {
				t.Errorf("Expected success %v, got %v", tt.expectedSuccess, response.Success)
			}

			if response.InquiryStatus != tt.expectedStatus {
				t.Errorf("Expected status %s, got %s", tt.expectedStatus, response.InquiryStatus)
			}

			if response.Message != tt.expectedMessage {
				t.Errorf("Expected message '%s', got '%s'", tt.expectedMessage, response.Message)
			}

			if response.RequestID != tt.request.RequestID.String() {
				t.Errorf("Expected request ID %s, got %s", tt.request.RequestID.String(), response.RequestID)
			}

			if response.ResponseTime <= 0 {
				t.Errorf("Expected positive response time, got %v", response.ResponseTime)
			}

			// Verify customer data for successful cases
			if tt.expectedSuccess && tt.ottoPayResult != nil {
				if response.Customer == nil {
					t.Error("Expected customer data in successful response")
				} else {
					if !response.Customer.CompanyCode.Equals(tt.ottoPayResult.CompanyCode) {
						t.Errorf("Expected company code %s, got %s", 
							tt.ottoPayResult.CompanyCode.String(), 
							response.Customer.CompanyCode.String())
					}
				}
			}

			// Verify audit logging
			if len(auditRepo.events) == 0 {
				t.Error("Expected audit events to be logged")
			}
		})
	}
}

func TestCustomerInquiryUseCase_RequestValidation(t *testing.T) {
	// Setup
	ottoPayService := &mockOttoPayService{}
	validationService := &mockValidationService{}
	auditRepo := &mockAuditRepository{}
	tokenRepo := &mockTokenRepository{}

	uc := NewCustomerInquiryUseCase(
		ottoPayService,
		validationService,
		auditRepo,
		tokenRepo,
	)

	tests := []struct {
		name        string
		request     *CustomerInquiryRequest
		expectError bool
	}{
		{
			name:        "Nil request",
			request:     nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			response, err := uc.Execute(ctx, tt.request)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error for invalid request")
				}
				if response != nil {
					t.Error("Expected nil response for invalid request")
				}
			}
		})
	}
}

func TestCustomerInquiryUseCase_AuditLogging(t *testing.T) {
	// Setup test data
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("56751590099")
	requestID, _ := valueobjects.NewRequestID("REQ123456789")

	validToken := &entities.Token{
		Value:     "valid-token-123",
		Username:  "testuser",
		ExpiresAt: time.Now().Add(1 * time.Hour),
		IssuedAt:  time.Now(),
		IsActive:  true,
	}

	request := &CustomerInquiryRequest{
		CompanyCode:    companyCode,
		CustomerNumber: customerNumber,
		RequestID:      requestID,
		ChannelType:    "API",
		Token:          "valid-token-123",
		SourceIP:       "***********",
		UserAgent:      "TestAgent/1.0",
	}

	// Setup mocks
	ottoPayService := &mockOttoPayService{
		inquiryResult: nil,
		inquiryError:  errors.New("test error"),
	}
	validationService := &mockValidationService{}
	auditRepo := &mockAuditRepository{}
	tokenRepo := &mockTokenRepository{}
	tokenRepo.Store(context.Background(), validToken)

	uc := NewCustomerInquiryUseCase(
		ottoPayService,
		validationService,
		auditRepo,
		tokenRepo,
	)

	// Execute
	ctx := context.Background()
	_, err := uc.Execute(ctx, request)

	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify audit events were logged
	if len(auditRepo.events) < 2 {
		t.Errorf("Expected at least 2 audit events (request + response), got %d", len(auditRepo.events))
	}

	// Check request audit event
	requestEvent := auditRepo.events[0]
	if requestEvent.Type != repositories.AuditEventTypeInquiryRequest {
		t.Errorf("Expected request audit event type %s, got %s", 
			repositories.AuditEventTypeInquiryRequest, requestEvent.Type)
	}

	if requestEvent.CompanyCode == nil || !requestEvent.CompanyCode.Equals(companyCode) {
		t.Error("Expected company code in audit event")
	}

	if requestEvent.CustomerNumber == nil || !requestEvent.CustomerNumber.Equals(customerNumber) {
		t.Error("Expected customer number in audit event")
	}

	// Check response audit event
	responseEvent := auditRepo.events[1]
	if responseEvent.Type != repositories.AuditEventTypeInquiryResponse {
		t.Errorf("Expected response audit event type %s, got %s", 
			repositories.AuditEventTypeInquiryResponse, responseEvent.Type)
	}
}
