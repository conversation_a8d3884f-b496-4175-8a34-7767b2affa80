package payment

import (
	"context"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// ProcessPaymentUseCase handles payment processing operations
type ProcessPaymentUseCase struct {
	paymentRepo       repositories.PaymentRepository
	ottoPayService    services.OttoPayService
	validationService services.ValidationService
	auditRepo         repositories.AuditRepository
	tokenRepo         repositories.TokenRepository
}

// NewProcessPaymentUseCase creates a new process payment use case
func NewProcessPaymentUseCase(
	paymentRepo repositories.PaymentRepository,
	ottoPayService services.OttoPayService,
	validationService services.ValidationService,
	auditRepo repositories.AuditRepository,
	tokenRepo repositories.TokenRepository,
) *ProcessPaymentUseCase {
	return &ProcessPaymentUseCase{
		paymentRepo:       paymentRepo,
		ottoPayService:    ottoPayService,
		validationService: validationService,
		auditRepo:         auditRepo,
		tokenRepo:         tokenRepo,
	}
}

// ProcessPaymentRequest represents a payment processing request
type ProcessPaymentRequest struct {
	CompanyCode     valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber  valueobjects.CustomerNumber `json:"customer_number"`
	RequestID       valueobjects.RequestID      `json:"request_id"`
	ChannelType     string                      `json:"channel_type"`
	CustomerName    string                      `json:"customer_name"`
	CurrencyCode    valueobjects.CurrencyCode   `json:"currency_code"`
	PaidAmount      valueobjects.Amount         `json:"paid_amount"`
	TotalAmount     valueobjects.Amount         `json:"total_amount"`
	Reference       string                      `json:"reference"`
	SubCompany      string                      `json:"sub_company,omitempty"`
	TransactionDate *time.Time                  `json:"transaction_date,omitempty"`
	DetailBills     interface{}                 `json:"detail_bills,omitempty"`
	AdditionalData  string                      `json:"additional_data,omitempty"`

	// Authentication
	Token string `json:"token"`

	// Optional fields for audit
	SourceIP  string `json:"source_ip,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// ProcessPaymentResponse represents a payment processing response
type ProcessPaymentResponse struct {
	Success       bool                       `json:"success"`
	Payment       *entities.Payment          `json:"payment,omitempty"`
	Status        entities.PaymentStatus     `json:"status"`
	Reason        entities.PaymentFlagReason `json:"reason"`
	TransactionID string                     `json:"transaction_id"`
	Message       string                     `json:"message"`
	ResponseTime  time.Duration              `json:"response_time"`
	RequestID     string                     `json:"request_id"`
}

// Execute executes the process payment use case
func (uc *ProcessPaymentUseCase) Execute(ctx context.Context, req *ProcessPaymentRequest) (*ProcessPaymentResponse, error) {
	startTime := time.Now()

	// Log audit event for payment request
	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-REQUEST",
		Type:           repositories.AuditEventTypePaymentRequest,
		Level:          repositories.AuditLevelInfo,
		Message:        "Payment processing request initiated",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		RequestData: map[string]interface{}{
			"company_code":    req.CompanyCode.String(),
			"customer_number": req.CustomerNumber.String(),
			"customer_name":   req.CustomerName,
			"currency_code":   req.CurrencyCode.String(),
			"paid_amount":     req.PaidAmount.String(),
			"total_amount":    req.TotalAmount.String(),
			"reference":       req.Reference,
			"channel_type":    req.ChannelType,
		},
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to log payment audit event: %v\n", err)
	}

	// Validate token
	if req.Token == "" {
		uc.logPaymentError(ctx, req, "MISSING_TOKEN", "Authentication token is required", startTime)
		return &ProcessPaymentResponse{
			Success: false,
			Status:  entities.PaymentStatusFailed,
			Reason: entities.PaymentFlagReason{
				Indonesian: "Token autentikasi diperlukan",
				English:    "Authentication token is required",
			},
			Message:      "Authentication token is required",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Get and validate token
	token, err := uc.tokenRepo.GetByValue(ctx, req.Token)
	if err != nil || token == nil || !token.IsValid() {
		uc.logPaymentError(ctx, req, "INVALID_TOKEN", "Invalid or expired token", startTime)
		return &ProcessPaymentResponse{
			Success: false,
			Status:  entities.PaymentStatusFailed,
			Reason: entities.PaymentFlagReason{
				Indonesian: "Token tidak valid atau telah kedaluwarsa",
				English:    "Invalid or expired token",
			},
			Message:      "Invalid or expired token",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Validate payment request
	serviceReq := &services.PaymentProcessingRequest{
		CompanyCode:     req.CompanyCode,
		CustomerNumber:  req.CustomerNumber,
		RequestID:       req.RequestID,
		ChannelType:     req.ChannelType,
		CustomerName:    req.CustomerName,
		CurrencyCode:    req.CurrencyCode,
		PaidAmount:      req.PaidAmount,
		TotalAmount:     req.TotalAmount,
		Reference:       req.Reference,
		SubCompany:      req.SubCompany,
		TransactionDate: req.TransactionDate,
		DetailBills:     req.DetailBills,
		AdditionalData:  req.AdditionalData,
	}

	if err := uc.validationService.ValidatePaymentRequest(ctx, serviceReq); err != nil {
		uc.logPaymentError(ctx, req, "VALIDATION_FAILED", err.Error(), startTime)
		return &ProcessPaymentResponse{
			Success: false,
			Status:  entities.PaymentStatusFailed,
			Reason: entities.PaymentFlagReason{
				Indonesian: "Permintaan tidak valid",
				English:    "Invalid request",
			},
			Message:      "Validation failed: " + err.Error(),
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Check request ID uniqueness
	if err := uc.validationService.ValidateRequestIDUniqueness(ctx, req.RequestID); err != nil {
		uc.logPaymentError(ctx, req, "DUPLICATE_REQUEST", err.Error(), startTime)
		return &ProcessPaymentResponse{
			Success: false,
			Status:  entities.PaymentStatusFailed,
			Reason: entities.PaymentFlagReason{
				Indonesian: "ID permintaan sudah digunakan",
				English:    "Request ID already used",
			},
			Message:      "Duplicate request ID",
			ResponseTime: time.Since(startTime),
			RequestID:    req.RequestID.String(),
		}, nil
	}

	// Check if payment already exists
	existingPayment, err := uc.paymentRepo.GetByRequestID(ctx, req.RequestID)
	if err == nil && existingPayment != nil {
		uc.logPaymentError(ctx, req, "PAYMENT_EXISTS", "Payment already exists for this request ID", startTime)
		return &ProcessPaymentResponse{
			Success:       existingPayment.IsSuccess(),
			Payment:       existingPayment,
			Status:        existingPayment.Status,
			Reason:        existingPayment.Reason,
			TransactionID: existingPayment.ID,
			Message:       "Payment already processed",
			ResponseTime:  time.Since(startTime),
			RequestID:     req.RequestID.String(),
		}, nil
	}

	// Create payment entity
	paymentID := fmt.Sprintf("PAY-%d", time.Now().UnixNano())
	payment, err := entities.NewPayment(
		paymentID,
		req.CompanyCode,
		req.CustomerNumber,
		req.RequestID,
		req.CustomerName,
		req.CurrencyCode,
		req.PaidAmount,
		req.TotalAmount,
		req.Reference,
		req.ChannelType,
	)
	if err != nil {
		uc.logPaymentError(ctx, req, "PAYMENT_CREATION_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to create payment entity: %w", err)
	}

	// Set optional fields
	if req.SubCompany != "" {
		payment.SubCompany = req.SubCompany
	}
	if req.DetailBills != nil {
		payment.SetDetailBills(req.DetailBills)
	}
	if req.AdditionalData != "" {
		payment.SetAdditionalData(req.AdditionalData)
	}

	// Store payment in repository
	if err := uc.paymentRepo.Store(ctx, payment); err != nil {
		uc.logPaymentError(ctx, req, "PAYMENT_STORAGE_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to store payment: %w", err)
	}

	// Create transaction record
	transactionReq := &services.TransactionCreationRequest{
		RequestID:      req.RequestID,
		Type:           entities.TransactionTypePayment,
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		ChannelType:    req.ChannelType,
		Metadata: map[string]interface{}{
			"payment_id":      payment.ID,
			"source_ip":       req.SourceIP,
			"user_agent":      req.UserAgent,
			"additional_data": req.AdditionalData,
			"username":        token.Username,
			"reference":       req.Reference,
		},
	}

	transaction, err := uc.ottoPayService.CreateTransaction(ctx, transactionReq)
	if err != nil {
		uc.logPaymentError(ctx, req, "TRANSACTION_CREATION_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Start transaction processing
	if err := transaction.StartProcessing(); err != nil {
		uc.logPaymentError(ctx, req, "TRANSACTION_START_FAILED", err.Error(), startTime)
		return nil, fmt.Errorf("failed to start transaction processing: %w", err)
	}

	// Update transaction status
	if err := uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateProcessing); err != nil {
		// Log error but continue
		fmt.Printf("Failed to update transaction status: %v\n", err)
	}

	// Process payment with OttoPay service
	paymentResult, err := uc.ottoPayService.ProcessPayment(ctx, serviceReq)
	if err != nil {
		// Mark transaction as failed
		transaction.Fail("PAYMENT_ERROR", err.Error())
		uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateFailed)

		// Update payment status
		payment.MarkAsFailed(entities.PaymentFlagReason{
			Indonesian: "Gagal memproses pembayaran",
			English:    "Payment processing failed",
		})
		uc.paymentRepo.Update(ctx, payment)

		uc.logPaymentError(ctx, req, "PAYMENT_PROCESSING_FAILED", err.Error(), startTime)
		return &ProcessPaymentResponse{
			Success:       false,
			Payment:       payment,
			Status:        entities.PaymentStatusFailed,
			Reason:        payment.Reason,
			TransactionID: transaction.ID,
			Message:       "Payment processing failed: " + err.Error(),
			ResponseTime:  time.Since(startTime),
			RequestID:     req.RequestID.String(),
		}, err
	}

	// Update payment based on result
	switch paymentResult.Status {
	case entities.PaymentStatusSuccess:
		payment.MarkAsSuccess(paymentResult.Reason)
	case entities.PaymentStatusFailed:
		payment.MarkAsFailed(paymentResult.Reason)
	case entities.PaymentStatusTimeout:
		payment.MarkAsTimeout(paymentResult.Reason)
	}

	// Update payment in repository
	if err := uc.paymentRepo.Update(ctx, payment); err != nil {
		// Log error but continue
		fmt.Printf("Failed to update payment: %v\n", err)
	}

	// Complete transaction
	if paymentResult.Success {
		if err := transaction.Complete(); err != nil {
			// Log error but continue
			fmt.Printf("Failed to complete transaction: %v\n", err)
		} else {
			uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateCompleted)
		}
	} else {
		transaction.Fail("PAYMENT_REJECTED", paymentResult.Message)
		uc.ottoPayService.UpdateTransactionStatus(ctx, transaction.ID, entities.TransactionStateFailed)
	}

	// Log payment result
	responseTime := time.Since(startTime)
	if paymentResult.Success {
		uc.logPaymentSuccess(ctx, req, paymentResult, responseTime, transaction.ID)
	} else {
		uc.logPaymentError(ctx, req, "PAYMENT_REJECTED", paymentResult.Message, startTime)
	}

	return &ProcessPaymentResponse{
		Success:       paymentResult.Success,
		Payment:       payment,
		Status:        paymentResult.Status,
		Reason:        paymentResult.Reason,
		TransactionID: transaction.ID,
		Message:       paymentResult.Message,
		ResponseTime:  responseTime,
		RequestID:     req.RequestID.String(),
	}, nil
}

// logPaymentSuccess logs successful payment operations
func (uc *ProcessPaymentUseCase) logPaymentSuccess(ctx context.Context, req *ProcessPaymentRequest, result *services.PaymentProcessingResult, duration time.Duration, transactionID string) {
	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-SUCCESS",
		Type:           repositories.AuditEventTypePaymentSuccess,
		Level:          repositories.AuditLevelInfo,
		Message:        "Payment processed successfully",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		ResponseData: map[string]interface{}{
			"success":        result.Success,
			"status":         result.Status,
			"transaction_id": transactionID,
			"paid_amount":    req.PaidAmount.String(),
			"currency_code":  req.CurrencyCode.String(),
		},
		Duration:    &duration,
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log payment success audit event: %v\n", err)
	}
}

// logPaymentError logs payment operation errors
func (uc *ProcessPaymentUseCase) logPaymentError(ctx context.Context, req *ProcessPaymentRequest, errorCode, errorMessage string, startTime time.Time) {
	duration := time.Since(startTime)

	auditEvent := &repositories.AuditEvent{
		ID:             req.RequestID.String() + "-ERROR",
		Type:           repositories.AuditEventTypePaymentFailed,
		Level:          repositories.AuditLevelError,
		Message:        "Payment processing failed",
		CompanyCode:    &req.CompanyCode,
		CustomerNumber: &req.CustomerNumber,
		RequestID:      &req.RequestID,
		ErrorCode:      &errorCode,
		ErrorMessage:   &errorMessage,
		Duration:       &duration,
		SourceIP:       &req.SourceIP,
		UserAgent:      &req.UserAgent,
		ChannelType:    &req.ChannelType,
		Timestamp:      time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log payment error audit event: %v\n", err)
	}
}
