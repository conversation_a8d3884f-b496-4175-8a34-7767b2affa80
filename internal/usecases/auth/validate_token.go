package auth

import (
	"context"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
)

// ValidateTokenUseCase handles token validation operations
type ValidateTokenUseCase struct {
	tokenRepo         repositories.TokenRepository
	tokenCacheRepo    repositories.TokenCacheRepository
	validationService services.ValidationService
	auditRepo         repositories.AuditRepository
}

// NewValidateTokenUseCase creates a new validate token use case
func NewValidateTokenUseCase(
	tokenRepo repositories.TokenRepository,
	tokenCacheRepo repositories.TokenCacheRepository,
	validationService services.ValidationService,
	auditRepo repositories.AuditRepository,
) *ValidateTokenUseCase {
	return &ValidateTokenUseCase{
		tokenRepo:         tokenRepo,
		tokenCacheRepo:    tokenCacheRepo,
		validationService: validationService,
		auditRepo:         auditRepo,
	}
}

// ValidateTokenRequest represents a token validation request
type ValidateTokenRequest struct {
	Token string `json:"token"`

	// Optional fields for audit
	SourceIP    string `json:"source_ip,omitempty"`
	UserAgent   string `json:"user_agent,omitempty"`
	ChannelType string `json:"channel_type,omitempty"`
	Operation   string `json:"operation,omitempty"`
}

// ValidateTokenResponse represents a token validation response
type ValidateTokenResponse struct {
	Valid     bool            `json:"valid"`
	Token     *entities.Token `json:"token,omitempty"`
	ExpiresIn time.Duration   `json:"expires_in"`
	Reason    string          `json:"reason,omitempty"`
	Username  string          `json:"username,omitempty"`
	TokenID   string          `json:"token_id,omitempty"`
	RequestID string          `json:"request_id"`
}

// Execute executes the validate token use case
func (uc *ValidateTokenUseCase) Execute(ctx context.Context, req *ValidateTokenRequest) (*ValidateTokenResponse, error) {
	startTime := time.Now()

	// Generate request ID for tracking
	requestID := fmt.Sprintf("VAL-%d", time.Now().UnixNano())

	// Basic validation
	if req.Token == "" {
		uc.logValidationEvent(ctx, requestID, "EMPTY_TOKEN", "Token is empty", req, nil, time.Since(startTime))
		return &ValidateTokenResponse{
			Valid:     false,
			Reason:    "Token is required",
			RequestID: requestID,
		}, nil
	}

	// Try to get token from cache first
	var token *entities.Token
	var err error

	if uc.tokenCacheRepo != nil {
		cacheKey := fmt.Sprintf("token_value:%s", req.Token)
		token, err = uc.tokenCacheRepo.Get(ctx, cacheKey)
		if err == nil && token != nil {
			// Validate cached token
			if token.IsValid() {
				duration := time.Since(startTime)
				uc.logValidationEvent(ctx, requestID, "CACHED_TOKEN_VALID", "Token validated from cache", req, token, duration)

				return &ValidateTokenResponse{
					Valid:     true,
					Token:     token,
					ExpiresIn: token.GetRemainingTime(),
					Username:  token.Username,
					TokenID:   token.ID,
					RequestID: requestID,
				}, nil
			} else {
				// Remove invalid token from cache
				uc.tokenCacheRepo.Delete(ctx, cacheKey)
			}
		}
	}

	// Get token from repository
	token, err = uc.tokenRepo.GetByValue(ctx, req.Token)
	if err != nil {
		uc.logValidationEvent(ctx, requestID, "TOKEN_NOT_FOUND", "Token not found in repository", req, nil, time.Since(startTime))
		return &ValidateTokenResponse{
			Valid:     false,
			Reason:    "Token not found",
			RequestID: requestID,
		}, nil
	}

	// Validate token entity (if validation service is available)
	if uc.validationService != nil {
		if err := uc.validationService.ValidateToken(ctx, token); err != nil {
			uc.logValidationEvent(ctx, requestID, "TOKEN_VALIDATION_FAILED", err.Error(), req, token, time.Since(startTime))
			return &ValidateTokenResponse{
				Valid:     false,
				Reason:    "Token validation failed: " + err.Error(),
				Username:  token.Username,
				TokenID:   token.ID,
				RequestID: requestID,
			}, nil
		}
	}

	// Check if token is expired
	if token.IsExpired() {
		uc.logValidationEvent(ctx, requestID, "TOKEN_EXPIRED", "Token has expired", req, token, time.Since(startTime))

		// Mark token as expired in repository
		token.Revoke()
		uc.tokenRepo.Update(ctx, token)

		return &ValidateTokenResponse{
			Valid:     false,
			Token:     token,
			Reason:    "Token has expired",
			Username:  token.Username,
			TokenID:   token.ID,
			RequestID: requestID,
		}, nil
	}

	// Check if token is revoked
	if token.IsRevoked {
		uc.logValidationEvent(ctx, requestID, "TOKEN_REVOKED", "Token has been revoked", req, token, time.Since(startTime))
		return &ValidateTokenResponse{
			Valid:     false,
			Reason:    "Token has been revoked",
			Username:  token.Username,
			TokenID:   token.ID,
			RequestID: requestID,
		}, nil
	}

	// Token is valid - cache it for future use
	if uc.tokenCacheRepo != nil {
		cacheKey := fmt.Sprintf("token_value:%s", req.Token)
		ttl := token.GetRemainingTime()
		if ttl > 0 {
			if err := uc.tokenCacheRepo.Set(ctx, cacheKey, token, ttl); err != nil {
				// Log error but don't fail validation
				fmt.Printf("Failed to cache token %s: %v\n", token.ID, err)
			}
		}
	}

	duration := time.Since(startTime)
	uc.logValidationEvent(ctx, requestID, "TOKEN_VALID", "Token is valid", req, token, duration)

	return &ValidateTokenResponse{
		Valid:     true,
		Token:     token,
		ExpiresIn: token.GetRemainingTime(),
		Username:  token.Username,
		TokenID:   token.ID,
		RequestID: requestID,
	}, nil
}

// IsTokenValid is a convenience method that returns only the validation result
func (uc *ValidateTokenUseCase) IsTokenValid(ctx context.Context, tokenValue string) (bool, error) {
	req := &ValidateTokenRequest{
		Token: tokenValue,
	}

	response, err := uc.Execute(ctx, req)
	if err != nil {
		return false, err
	}

	return response.Valid, nil
}

// GetTokenInfo returns token information without full validation
func (uc *ValidateTokenUseCase) GetTokenInfo(ctx context.Context, tokenValue string) (*entities.Token, error) {
	// Try cache first
	if uc.tokenCacheRepo != nil {
		cacheKey := fmt.Sprintf("token_value:%s", tokenValue)
		if token, err := uc.tokenCacheRepo.Get(ctx, cacheKey); err == nil && token != nil {
			return token, nil
		}
	}

	// Get from repository
	return uc.tokenRepo.GetByValue(ctx, tokenValue)
}

// logValidationEvent logs token validation events
func (uc *ValidateTokenUseCase) logValidationEvent(ctx context.Context, requestID, eventCode, message string, req *ValidateTokenRequest, token *entities.Token, duration time.Duration) {
	// Skip logging if audit repository is not available
	if uc.auditRepo == nil {
		return
	}

	var level repositories.AuditLevel
	var eventType repositories.AuditEventType

	switch eventCode {
	case "TOKEN_VALID", "CACHED_TOKEN_VALID":
		level = repositories.AuditLevelInfo
		eventType = repositories.AuditEventTypeTokenRequest
	case "TOKEN_EXPIRED", "TOKEN_REVOKED":
		level = repositories.AuditLevelWarning
		eventType = repositories.AuditEventTypeTokenExpired
	default:
		level = repositories.AuditLevelError
		eventType = repositories.AuditEventTypeAPIError
	}

	auditEvent := &repositories.AuditEvent{
		ID:      requestID,
		Type:    eventType,
		Level:   level,
		Message: message,
		RequestData: map[string]interface{}{
			"operation":    req.Operation,
			"event_code":   eventCode,
			"token_length": len(req.Token),
		},
		Duration:    &duration,
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if token != nil {
		auditEvent.Username = &token.Username
		auditEvent.ResponseData = map[string]interface{}{
			"token_id":   token.ID,
			"username":   token.Username,
			"expires_at": token.ExpiresAt,
			"is_expired": token.IsExpired(),
			"is_revoked": token.IsRevoked,
		}
	}

	if eventCode != "TOKEN_VALID" && eventCode != "CACHED_TOKEN_VALID" {
		auditEvent.ErrorCode = &eventCode
		auditEvent.ErrorMessage = &message
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log validation audit event: %v\n", err)
	}
}
