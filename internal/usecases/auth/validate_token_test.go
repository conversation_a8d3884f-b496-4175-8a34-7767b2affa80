package auth

import (
	"context"
	"testing"

	"repo.nusatek.id/sugeng/ottopay/internal/testutils"
)

func TestValidateTokenUseCase_Execute(t *testing.T) {
	// Setup
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()
	mockAuditRepo := testutils.NewMockAuditRepository()

	useCase := NewValidateTokenUseCase(
		mockTokenRepo,
		nil, // tokenCacheRepo
		nil, // validationService
		mockAuditRepo,
	)

	ctx := context.Background()

	t.Run("Valid token", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockAuditRepo.Reset()

		// Create and store a valid token
		validToken := fixtures.CreateValidToken()
		mockTokenRepo.AddToken(validToken)

		req := &ValidateTokenRequest{
			Token:       validToken.Value,
			SourceIP:    "127.0.0.1",
			UserAgent:   "test-agent",
			ChannelType: testutils.ValidChannelType,
			Operation:   "test-operation",
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response == nil {
			t.Errorf("Expected response, got nil")
			return
		}

		if !response.Valid {
			t.Errorf("Expected token to be valid, got: %s", response.Reason)
		}

		if response.Token == nil {
			t.Errorf("Expected token in response")
		}

		if response.Username != validToken.Username {
			t.Errorf("Expected username %s, got %s", validToken.Username, response.Username)
		}

		if response.TokenID != validToken.ID {
			t.Errorf("Expected token ID %s, got %s", validToken.ID, response.TokenID)
		}

		if response.ExpiresIn <= 0 {
			t.Errorf("Expected positive expiration time")
		}

		// Verify mock calls
		if mockTokenRepo.GetCallCount("GetByValue") != 1 {
			t.Errorf("Expected GetByValue to be called once")
		}

		if mockAuditRepo.GetCallCount("LogEvent") < 1 {
			t.Errorf("Expected audit events to be logged")
		}
	})

	t.Run("Token not found", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockAuditRepo.Reset()

		req := &ValidateTokenRequest{
			Token: "non-existent-token",
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response == nil {
			t.Errorf("Expected response, got nil")
			return
		}

		if response.Valid {
			t.Errorf("Expected token to be invalid")
		}

		if response.Reason == "" {
			t.Errorf("Expected reason for invalid token")
		}

		// Verify mock calls
		if mockTokenRepo.GetCallCount("GetByValue") != 1 {
			t.Errorf("Expected GetByValue to be called once")
		}
	})

	t.Run("Expired token", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockAuditRepo.Reset()

		// Create and store an expired token
		expiredToken := fixtures.CreateExpiredToken()
		mockTokenRepo.AddToken(expiredToken)

		req := &ValidateTokenRequest{
			Token: expiredToken.Value,
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response.Valid {
			t.Errorf("Expected expired token to be invalid")
		}

		if response.Reason == "" {
			t.Errorf("Expected reason for expired token")
		}

		// Should still return token info even if expired
		if response.Token == nil {
			t.Errorf("Expected token info in response even for expired token")
		}
	})

	t.Run("Revoked token", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockAuditRepo.Reset()

		// Create a valid token and revoke it
		revokedToken := fixtures.CreateValidToken()
		revokedToken.Revoke()
		mockTokenRepo.AddToken(revokedToken)

		req := &ValidateTokenRequest{
			Token: revokedToken.Value,
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response.Valid {
			t.Errorf("Expected revoked token to be invalid")
		}

		if response.Reason == "" {
			t.Errorf("Expected reason for revoked token")
		}
	})

	t.Run("Empty token", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockAuditRepo.Reset()

		req := &ValidateTokenRequest{
			Token: "",
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response.Valid {
			t.Errorf("Expected empty token to be invalid")
		}

		if response.Reason == "" {
			t.Errorf("Expected reason for empty token")
		}

		// Should not call repository for empty token
		if mockTokenRepo.GetCallCount("GetByValue") != 0 {
			t.Errorf("Expected GetByValue to not be called for empty token")
		}
	})
}

func TestValidateTokenUseCase_IsTokenValid(t *testing.T) {
	// Setup
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()

	useCase := NewValidateTokenUseCase(
		mockTokenRepo,
		nil,
		nil,
		nil,
	)

	ctx := context.Background()

	t.Run("Valid token", func(t *testing.T) {
		mockTokenRepo.Reset()

		validToken := fixtures.CreateValidToken()
		mockTokenRepo.AddToken(validToken)

		isValid, err := useCase.IsTokenValid(ctx, validToken.Value)

		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if !isValid {
			t.Errorf("Expected token to be valid")
		}
	})

	t.Run("Invalid token", func(t *testing.T) {
		mockTokenRepo.Reset()

		expiredToken := fixtures.CreateExpiredToken()
		mockTokenRepo.AddToken(expiredToken)

		isValid, err := useCase.IsTokenValid(ctx, expiredToken.Value)

		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if isValid {
			t.Errorf("Expected token to be invalid")
		}
	})

	t.Run("Non-existent token", func(t *testing.T) {
		mockTokenRepo.Reset()

		isValid, err := useCase.IsTokenValid(ctx, "non-existent")

		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if isValid {
			t.Errorf("Expected non-existent token to be invalid")
		}
	})
}

func TestValidateTokenUseCase_GetTokenInfo(t *testing.T) {
	// Setup
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()

	useCase := NewValidateTokenUseCase(
		mockTokenRepo,
		nil,
		nil,
		nil,
	)

	ctx := context.Background()

	t.Run("Existing token", func(t *testing.T) {
		mockTokenRepo.Reset()

		existingToken := fixtures.CreateValidToken()
		mockTokenRepo.AddToken(existingToken)

		token, err := useCase.GetTokenInfo(ctx, existingToken.Value)

		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if token == nil {
			t.Errorf("Expected token info")
		}

		if token.ID != existingToken.ID {
			t.Errorf("Expected token ID %s, got %s", existingToken.ID, token.ID)
		}
	})

	t.Run("Non-existent token", func(t *testing.T) {
		mockTokenRepo.Reset()

		token, err := useCase.GetTokenInfo(ctx, "non-existent")

		if err == nil {
			t.Errorf("Expected error for non-existent token")
		}

		if token != nil {
			t.Errorf("Expected no token info for non-existent token")
		}
	})
}

func TestValidateTokenUseCase_CacheIntegration(t *testing.T) {
	// Setup with cache
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()
	mockTokenCacheRepo := testutils.NewMockTokenCacheRepository()

	useCase := NewValidateTokenUseCase(
		mockTokenRepo,
		mockTokenCacheRepo,
		nil,
		nil,
	)

	ctx := context.Background()

	t.Run("Cache hit", func(t *testing.T) {
		mockTokenRepo.Reset()
		mockTokenCacheRepo.Reset()

		validToken := fixtures.CreateValidToken()

		// Add token to cache
		cacheKey := "token_value:" + validToken.Value
		mockTokenCacheRepo.SetToken(cacheKey, validToken)

		req := &ValidateTokenRequest{
			Token: validToken.Value,
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if !response.Valid {
			t.Errorf("Expected cached token to be valid")
		}

		// Should not call repository if found in cache
		if mockTokenRepo.GetCallCount("GetByValue") != 0 {
			t.Errorf("Expected repository to not be called when token found in cache")
		}

		if mockTokenCacheRepo.GetCallCount("Get") != 1 {
			t.Errorf("Expected cache to be checked")
		}
	})

	t.Run("Cache miss", func(t *testing.T) {
		mockTokenRepo.Reset()
		mockTokenCacheRepo.Reset()

		validToken := fixtures.CreateValidToken()
		mockTokenRepo.AddToken(validToken)

		req := &ValidateTokenRequest{
			Token: validToken.Value,
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if !response.Valid {
			t.Errorf("Expected token to be valid")
		}

		// Should call repository when not in cache
		if mockTokenRepo.GetCallCount("GetByValue") != 1 {
			t.Errorf("Expected repository to be called when token not in cache")
		}

		// Should cache the token after validation
		if mockTokenCacheRepo.GetCallCount("Set") != 1 {
			t.Errorf("Expected token to be cached after validation")
		}
	})
}
