package auth

import (
	"context"
	"testing"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/testutils"
)

func TestGetTokenUseCase_Execute(t *testing.T) {
	// Setup
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()
	mockOttoPayService := testutils.NewMockOttoPayService()

	mockAuditRepo := testutils.NewMockAuditRepository()

	useCase := NewGetTokenUseCase(
		mockTokenRepo,
		nil, // tokenCacheRepo
		mockOttoPayService,
		nil, // validationService - skip for now
		nil, // encryptionService - skip for now
		mockAuditRepo,
	)

	ctx := context.Background()

	t.Run("Successful token generation", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockOttoPayService.Reset()

		req := &GetTokenRequest{
			Username:    testutils.ValidUsername,
			Password:    testutils.ValidPassword,
			SourceIP:    "127.0.0.1",
			UserAgent:   "test-agent",
			ChannelType: testutils.ValidChannelType,
		}

		// Set up mock to return successful authentication
		authResult := &services.AuthenticationResult{
			Token:     fixtures.CreateValidToken(),
			ExpiresIn: 24 * time.Hour,
			Success:   true,
			Message:   "Authentication successful",
		}
		mockOttoPayService.SetAuthResult(req.Username, req.Password, authResult)

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response == nil {
			t.Errorf("Expected response, got nil")
			return
		}

		if !response.Success {
			t.Errorf("Expected successful response, got: %s", response.Message)
		}

		if response.Token == nil {
			t.Errorf("Expected token in response")
		}

		if response.ExpiresIn <= 0 {
			t.Errorf("Expected positive expiration time")
		}

		// Verify mock calls
		if mockOttoPayService.GetCallCount("AuthenticateUser") != 1 {
			t.Errorf("Expected AuthenticateUser to be called once")
		}

		if mockTokenRepo.GetCallCount("Store") != 1 {
			t.Errorf("Expected Store to be called once")
		}
	})

	t.Run("Authentication failure", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockOttoPayService.Reset()

		req := &GetTokenRequest{
			Username: "invalid_user",
			Password: "invalid_pass",
		}

		// Set up mock to fail authentication
		mockOttoPayService.SetShouldFail("AuthenticateUser", true)

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err == nil {
			t.Errorf("Expected error for failed authentication")
			return
		}

		if response == nil {
			t.Errorf("Expected response even for failed authentication")
			return
		}

		if response.Success {
			t.Errorf("Expected unsuccessful response for failed authentication")
		}

		// Verify mock calls
		if mockOttoPayService.GetCallCount("AuthenticateUser") != 1 {
			t.Errorf("Expected AuthenticateUser to be called once")
		}

		if mockTokenRepo.GetCallCount("Store") != 0 {
			t.Errorf("Expected Store to not be called for failed authentication")
		}
	})

	t.Run("Empty username", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockOttoPayService.Reset()

		req := &GetTokenRequest{
			Username: "",
			Password: testutils.ValidPassword,
		}

		// Execute
		response, _ := useCase.Execute(ctx, req)

		// For this test, we expect the validation to be handled by the validation service
		// Since we're passing nil for validation service, the behavior might vary
		// This test demonstrates the structure - in a real implementation,
		// you would have proper validation

		if response != nil && response.Success {
			t.Errorf("Expected failure for empty username")
		}
	})

	t.Run("Existing valid token", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockOttoPayService.Reset()

		// Add existing valid token to repository
		existingToken := fixtures.CreateValidToken()
		existingToken.Username = testutils.ValidUsername
		mockTokenRepo.AddToken(existingToken)

		req := &GetTokenRequest{
			Username: testutils.ValidUsername,
			Password: testutils.ValidPassword,
		}

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if response == nil {
			t.Errorf("Expected response, got nil")
			return
		}

		if !response.Success {
			t.Errorf("Expected successful response")
		}

		// Should not call OttoPay service if valid token exists
		if mockOttoPayService.GetCallCount("AuthenticateUser") != 0 {
			t.Errorf("Expected AuthenticateUser to not be called when valid token exists")
		}
	})

	t.Run("Expired token replacement", func(t *testing.T) {
		// Reset mocks
		mockTokenRepo.Reset()
		mockOttoPayService.Reset()

		// Add expired token to repository
		expiredToken := fixtures.CreateExpiredToken()
		mockTokenRepo.AddToken(expiredToken)

		req := &GetTokenRequest{
			Username: testutils.ValidUsername,
			Password: testutils.ValidPassword,
		}

		// Set up mock to return successful authentication
		authResult := &services.AuthenticationResult{
			Token:     fixtures.CreateValidToken(),
			ExpiresIn: 24 * time.Hour,
			Success:   true,
			Message:   "Authentication successful",
		}
		mockOttoPayService.SetAuthResult(req.Username, req.Password, authResult)

		// Execute
		response, err := useCase.Execute(ctx, req)

		// Assertions
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if !response.Success {
			t.Errorf("Expected successful response")
		}

		// Should call OttoPay service to get new token
		if mockOttoPayService.GetCallCount("AuthenticateUser") != 1 {
			t.Errorf("Expected AuthenticateUser to be called for expired token")
		}
	})
}

func TestGetTokenUseCase_RequestValidation(t *testing.T) {
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()
	mockOttoPayService := testutils.NewMockOttoPayService()

	mockAuditRepo := testutils.NewMockAuditRepository()

	useCase := NewGetTokenUseCase(
		mockTokenRepo,
		nil,
		mockOttoPayService,
		nil,
		nil,
		mockAuditRepo,
	)

	ctx := context.Background()

	tests := []struct {
		name        string
		request     *GetTokenRequest
		expectError bool
	}{
		{
			name: "Valid request",
			request: &GetTokenRequest{
				Username:    testutils.ValidUsername,
				Password:    testutils.ValidPassword,
				SourceIP:    "127.0.0.1",
				UserAgent:   "test-agent",
				ChannelType: testutils.ValidChannelType,
			},
			expectError: false,
		},
		{
			name:        "Nil request",
			request:     nil,
			expectError: true,
		},
		{
			name: "Empty username",
			request: &GetTokenRequest{
				Username: "",
				Password: testutils.ValidPassword,
			},
			expectError: true,
		},
		{
			name: "Empty password",
			request: &GetTokenRequest{
				Username: testutils.ValidUsername,
				Password: "",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockTokenRepo.Reset()
			mockOttoPayService.Reset()

			// Set up successful authentication for valid cases
			if !tt.expectError {
				authResult := &services.AuthenticationResult{
					Token:     fixtures.CreateValidToken(),
					ExpiresIn: 24 * time.Hour,
					Success:   true,
					Message:   "Authentication successful",
				}
				if tt.request != nil {
					mockOttoPayService.SetAuthResult(tt.request.Username, tt.request.Password, authResult)
				}
			}

			// Execute
			response, err := useCase.Execute(ctx, tt.request)

			// Check expectations
			if tt.expectError {
				if err == nil && (response == nil || response.Success) {
					t.Errorf("Expected error or failure for invalid request")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid request: %v", err)
				}
				if response == nil || !response.Success {
					t.Errorf("Expected successful response for valid request")
				}
			}
		})
	}
}

func TestGetTokenUseCase_AuditLogging(t *testing.T) {
	fixtures := testutils.NewTestFixtures()
	mockTokenRepo := testutils.NewMockTokenRepository()
	mockOttoPayService := testutils.NewMockOttoPayService()
	mockAuditRepo := testutils.NewMockAuditRepository()

	useCase := NewGetTokenUseCase(
		mockTokenRepo,
		nil,
		mockOttoPayService,
		nil,
		nil,
		mockAuditRepo,
	)

	ctx := context.Background()

	t.Run("Audit events logged", func(t *testing.T) {
		req := &GetTokenRequest{
			Username:    testutils.ValidUsername,
			Password:    testutils.ValidPassword,
			SourceIP:    "127.0.0.1",
			UserAgent:   "test-agent",
			ChannelType: testutils.ValidChannelType,
		}

		// Set up successful authentication
		authResult := &services.AuthenticationResult{
			Token:     fixtures.CreateValidToken(),
			ExpiresIn: 24 * time.Hour,
			Success:   true,
			Message:   "Authentication successful",
		}
		mockOttoPayService.SetAuthResult(req.Username, req.Password, authResult)

		// Execute
		_, err := useCase.Execute(ctx, req)

		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		// Verify audit events were logged
		if mockAuditRepo.GetCallCount("LogEvent") < 1 {
			t.Errorf("Expected audit events to be logged")
		}
	})
}
