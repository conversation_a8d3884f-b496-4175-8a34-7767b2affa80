package auth

import (
	"context"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// GetTokenUseCase handles token acquisition operations
type GetTokenUseCase struct {
	tokenRepo         repositories.TokenRepository
	tokenCacheRepo    repositories.TokenCacheRepository
	ottoPayService    services.OttoPayService
	validationService services.ValidationService
	encryptionService services.EncryptionService
	auditRepo         repositories.AuditRepository
}

// NewGetTokenUseCase creates a new get token use case
func NewGetTokenUseCase(
	tokenRepo repositories.TokenRepository,
	tokenCacheRepo repositories.TokenCacheRepository,
	ottoPayService services.OttoPayService,
	validationService services.ValidationService,
	encryptionService services.EncryptionService,
	auditRepo repositories.AuditRepository,
) *GetTokenUseCase {
	return &GetTokenUseCase{
		tokenRepo:         tokenRepo,
		tokenCacheRepo:    tokenCacheRepo,
		ottoPayService:    ottoPayService,
		validationService: validationService,
		encryptionService: encryptionService,
		auditRepo:         auditRepo,
	}
}

// GetTokenRequest represents a token acquisition request
type GetTokenRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`

	// Optional fields for audit
	SourceIP    string `json:"source_ip,omitempty"`
	UserAgent   string `json:"user_agent,omitempty"`
	ChannelType string `json:"channel_type,omitempty"`
}

// GetTokenResponse represents a token acquisition response
type GetTokenResponse struct {
	Token     *entities.Token `json:"token"`
	ExpiresIn time.Duration   `json:"expires_in"`
	Success   bool            `json:"success"`
	Message   string          `json:"message"`
	RequestID string          `json:"request_id"`
}

// Execute executes the get token use case
func (uc *GetTokenUseCase) Execute(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error) {
	// Generate request ID for tracking
	requestID, err := valueobjects.GenerateTokenRequestID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate request ID: %w", err)
	}

	// Log audit event for token request
	auditEvent := &repositories.AuditEvent{
		ID:       requestID.String() + "-REQUEST",
		Type:     repositories.AuditEventTypeTokenRequest,
		Level:    repositories.AuditLevelInfo,
		Message:  "Token request initiated",
		Username: &req.Username,
		RequestData: map[string]interface{}{
			"username":     req.Username,
			"source_ip":    req.SourceIP,
			"user_agent":   req.UserAgent,
			"channel_type": req.ChannelType,
		},
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to log audit event: %v\n", err)
	}

	startTime := time.Now()

	// Validate authentication request
	if uc.validationService != nil {
		if err := uc.validationService.ValidateAuthenticationRequest(ctx, req.Username, req.Password); err != nil {
			uc.logTokenError(ctx, requestID.String(), "VALIDATION_FAILED", err.Error(), req)
			return &GetTokenResponse{
				Success:   false,
				Message:   "Invalid authentication request: " + err.Error(),
				RequestID: requestID.String(),
			}, nil
		}
	}

	// Check if user already has a valid token in cache
	cacheKey := fmt.Sprintf("token:%s", req.Username)
	if uc.tokenCacheRepo != nil {
		if cachedToken, err := uc.tokenCacheRepo.Get(ctx, cacheKey); err == nil && cachedToken != nil {
			if cachedToken.IsValid() {
				duration := time.Since(startTime)
				uc.logTokenSuccess(ctx, requestID.String(), "CACHED_TOKEN_USED", cachedToken, req, duration)

				return &GetTokenResponse{
					Token:     cachedToken,
					ExpiresIn: cachedToken.GetRemainingTime(),
					Success:   true,
					Message:   "Token retrieved from cache",
					RequestID: requestID.String(),
				}, nil
			}
		}
	}

	// Check if user has a valid token in repository
	existingToken, err := uc.tokenRepo.GetByUsername(ctx, req.Username)
	if err == nil && existingToken != nil && existingToken.IsValid() {
		// Cache the existing token
		if uc.tokenCacheRepo != nil {
			ttl := existingToken.GetRemainingTime()
			if ttl > 0 {
				uc.tokenCacheRepo.Set(ctx, cacheKey, existingToken, ttl)
			}
		}

		duration := time.Since(startTime)
		uc.logTokenSuccess(ctx, requestID.String(), "EXISTING_TOKEN_USED", existingToken, req, duration)

		return &GetTokenResponse{
			Token:     existingToken,
			ExpiresIn: existingToken.GetRemainingTime(),
			Success:   true,
			Message:   "Existing valid token found",
			RequestID: requestID.String(),
		}, nil
	}

	// Authenticate with OttoPay service
	authResult, err := uc.ottoPayService.AuthenticateUser(ctx, req.Username, req.Password)
	if err != nil {
		uc.logTokenError(ctx, requestID.String(), "AUTHENTICATION_FAILED", err.Error(), req)
		return &GetTokenResponse{
			Success:   false,
			Message:   "Authentication failed: " + err.Error(),
			RequestID: requestID.String(),
		}, err
	}

	if !authResult.Success {
		uc.logTokenError(ctx, requestID.String(), "AUTHENTICATION_REJECTED", authResult.Message, req)
		return &GetTokenResponse{
			Success:   false,
			Message:   authResult.Message,
			RequestID: requestID.String(),
		}, nil
	}

	// Create new token entity
	var tokenID string
	if uc.encryptionService != nil {
		var err error
		tokenID, err = uc.encryptionService.GenerateSecureToken(ctx, 16)
		if err != nil {
			uc.logTokenError(ctx, requestID.String(), "TOKEN_GENERATION_FAILED", err.Error(), req)
			return nil, fmt.Errorf("failed to generate token ID: %w", err)
		}
	} else {
		// Fallback for testing
		tokenID = "test-token-id-" + requestID.String()
	}

	token, err := entities.NewToken(
		tokenID,
		entities.TokenTypeAccess,
		authResult.Token.Value,
		req.Username,
		authResult.ExpiresIn,
	)
	if err != nil {
		uc.logTokenError(ctx, requestID.String(), "TOKEN_CREATION_FAILED", err.Error(), req)
		return nil, fmt.Errorf("failed to create token entity: %w", err)
	}

	// Set token metadata
	token.SetMetadata("request_id", requestID.String())
	token.SetMetadata("source_ip", req.SourceIP)
	token.SetMetadata("user_agent", req.UserAgent)
	token.SetMetadata("channel_type", req.ChannelType)

	// Revoke existing tokens for the user
	if err := uc.tokenRepo.RevokeByUsername(ctx, req.Username); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to revoke existing tokens for user %s: %v\n", req.Username, err)
	}

	// Store token in repository
	if err := uc.tokenRepo.Store(ctx, token); err != nil {
		uc.logTokenError(ctx, requestID.String(), "TOKEN_STORAGE_FAILED", err.Error(), req)
		return nil, fmt.Errorf("failed to store token: %w", err)
	}

	// Cache the token
	if uc.tokenCacheRepo != nil {
		ttl := token.GetRemainingTime()
		if ttl > 0 {
			if err := uc.tokenCacheRepo.Set(ctx, cacheKey, token, ttl); err != nil {
				// Log error but don't fail the operation
				fmt.Printf("Failed to cache token for user %s: %v\n", req.Username, err)
			}
		}
	}

	duration := time.Since(startTime)
	uc.logTokenSuccess(ctx, requestID.String(), "TOKEN_GENERATED", token, req, duration)

	return &GetTokenResponse{
		Token:     token,
		ExpiresIn: token.GetRemainingTime(),
		Success:   true,
		Message:   "Token generated successfully",
		RequestID: requestID.String(),
	}, nil
}

// logTokenSuccess logs successful token operations
func (uc *GetTokenUseCase) logTokenSuccess(ctx context.Context, requestID, operation string, token *entities.Token, req *GetTokenRequest, duration time.Duration) {
	auditEvent := &repositories.AuditEvent{
		ID:       requestID + "-SUCCESS",
		Type:     repositories.AuditEventTypeTokenGenerated,
		Level:    repositories.AuditLevelInfo,
		Message:  fmt.Sprintf("Token %s successful", operation),
		Username: &req.Username,
		ResponseData: map[string]interface{}{
			"token_id":   token.ID,
			"expires_at": token.ExpiresAt,
			"operation":  operation,
		},
		Duration:    &duration,
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log success audit event: %v\n", err)
	}
}

// logTokenError logs token operation errors
func (uc *GetTokenUseCase) logTokenError(ctx context.Context, requestID, errorCode, errorMessage string, req *GetTokenRequest) {
	auditEvent := &repositories.AuditEvent{
		ID:           requestID + "-ERROR",
		Type:         repositories.AuditEventTypeAPIError,
		Level:        repositories.AuditLevelError,
		Message:      "Token operation failed",
		Username:     &req.Username,
		ErrorCode:    &errorCode,
		ErrorMessage: &errorMessage,
		SourceIP:     &req.SourceIP,
		UserAgent:    &req.UserAgent,
		ChannelType:  &req.ChannelType,
		Timestamp:    time.Now(),
	}

	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log error audit event: %v\n", err)
	}
}
