package auth

import (
	"context"
	"fmt"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
)

// RefreshTokenUseCase handles token refresh operations
type RefreshTokenUseCase struct {
	tokenRepo         repositories.TokenRepository
	tokenCacheRepo    repositories.TokenCacheRepository
	ottoPayService    services.OttoPayService
	validationService services.ValidationService
	encryptionService services.EncryptionService
	auditRepo         repositories.AuditRepository
}

// NewRefreshTokenUseCase creates a new refresh token use case
func NewRefreshTokenUseCase(
	tokenRepo repositories.TokenRepository,
	tokenCacheRepo repositories.TokenCacheRepository,
	ottoPayService services.OttoPayService,
	validationService services.ValidationService,
	encryptionService services.EncryptionService,
	auditRepo repositories.AuditRepository,
) *RefreshTokenUseCase {
	return &RefreshTokenUseCase{
		tokenRepo:         tokenRepo,
		tokenCacheRepo:    tokenCacheRepo,
		ottoPayService:    ottoPayService,
		validationService: validationService,
		encryptionService: encryptionService,
		auditRepo:         auditRepo,
	}
}

// RefreshTokenRequest represents a token refresh request
type RefreshTokenRequest struct {
	Token string `json:"token"`
	
	// Optional fields for audit
	SourceIP    string `json:"source_ip,omitempty"`
	UserAgent   string `json:"user_agent,omitempty"`
	ChannelType string `json:"channel_type,omitempty"`
}

// RefreshTokenResponse represents a token refresh response
type RefreshTokenResponse struct {
	Token     *entities.Token `json:"token,omitempty"`
	ExpiresIn time.Duration   `json:"expires_in"`
	Success   bool            `json:"success"`
	Message   string          `json:"message"`
	RequestID string          `json:"request_id"`
}

// Execute executes the refresh token use case
func (uc *RefreshTokenUseCase) Execute(ctx context.Context, req *RefreshTokenRequest) (*RefreshTokenResponse, error) {
	startTime := time.Now()
	
	// Generate request ID for tracking
	requestID := fmt.Sprintf("REF-%d", time.Now().UnixNano())
	
	// Log audit event for token refresh request
	uc.logRefreshEvent(ctx, requestID, "REFRESH_REQUEST", "Token refresh requested", req, nil, time.Since(startTime))
	
	// Basic validation
	if req.Token == "" {
		uc.logRefreshError(ctx, requestID, "EMPTY_TOKEN", "Token is empty", req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   "Token is required",
			RequestID: requestID,
		}, nil
	}
	
	// Get existing token
	existingToken, err := uc.tokenRepo.GetByValue(ctx, req.Token)
	if err != nil {
		uc.logRefreshError(ctx, requestID, "TOKEN_NOT_FOUND", "Token not found", req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   "Token not found",
			RequestID: requestID,
		}, nil
	}
	
	// Validate existing token
	if err := uc.validationService.ValidateToken(ctx, existingToken); err != nil {
		uc.logRefreshError(ctx, requestID, "TOKEN_VALIDATION_FAILED", err.Error(), req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   "Token validation failed: " + err.Error(),
			RequestID: requestID,
		}, nil
	}
	
	// Check if token is revoked
	if existingToken.IsRevoked {
		uc.logRefreshError(ctx, requestID, "TOKEN_REVOKED", "Token has been revoked", req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   "Token has been revoked",
			RequestID: requestID,
		}, nil
	}
	
	// Check if token is eligible for refresh (not completely expired)
	// Allow refresh if token expired within the last hour
	if existingToken.IsExpired() {
		expiredDuration := time.Since(existingToken.ExpiresAt)
		if expiredDuration > time.Hour {
			uc.logRefreshError(ctx, requestID, "TOKEN_TOO_OLD", "Token expired too long ago", req)
			return &RefreshTokenResponse{
				Success:   false,
				Message:   "Token expired too long ago, please re-authenticate",
				RequestID: requestID,
			}, nil
		}
	}
	
	// Check if token should be refreshed (expires within threshold)
	refreshThreshold := 5 * time.Minute
	if !existingToken.ShouldRefresh(refreshThreshold) && !existingToken.IsExpired() {
		// Token is still valid and doesn't need refresh yet
		duration := time.Since(startTime)
		uc.logRefreshEvent(ctx, requestID, "REFRESH_NOT_NEEDED", "Token is still valid", req, existingToken, duration)
		
		return &RefreshTokenResponse{
			Token:     existingToken,
			ExpiresIn: existingToken.GetRemainingTime(),
			Success:   true,
			Message:   "Token is still valid, no refresh needed",
			RequestID: requestID,
		}, nil
	}
	
	// Refresh token with OttoPay service
	refreshResult, err := uc.ottoPayService.RefreshToken(ctx, existingToken.Value)
	if err != nil {
		uc.logRefreshError(ctx, requestID, "REFRESH_FAILED", err.Error(), req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   "Token refresh failed: " + err.Error(),
			RequestID: requestID,
		}, err
	}
	
	if !refreshResult.Success {
		uc.logRefreshError(ctx, requestID, "REFRESH_REJECTED", refreshResult.Message, req)
		return &RefreshTokenResponse{
			Success:   false,
			Message:   refreshResult.Message,
			RequestID: requestID,
		}, nil
	}
	
	// Update existing token with new value and expiration
	newTokenValue := refreshResult.Token.Value
	err = existingToken.Refresh(newTokenValue, refreshResult.ExpiresIn)
	if err != nil {
		uc.logRefreshError(ctx, requestID, "TOKEN_UPDATE_FAILED", err.Error(), req)
		return nil, fmt.Errorf("failed to update token: %w", err)
	}
	
	// Update token metadata
	existingToken.SetMetadata("refresh_request_id", requestID)
	existingToken.SetMetadata("refresh_source_ip", req.SourceIP)
	existingToken.SetMetadata("refresh_user_agent", req.UserAgent)
	existingToken.SetMetadata("refresh_channel_type", req.ChannelType)
	existingToken.SetMetadata("refresh_count", fmt.Sprintf("%d", uc.getRefreshCount(existingToken)+1))
	
	// Update token in repository
	if err := uc.tokenRepo.Update(ctx, existingToken); err != nil {
		uc.logRefreshError(ctx, requestID, "TOKEN_STORAGE_FAILED", err.Error(), req)
		return nil, fmt.Errorf("failed to update token in repository: %w", err)
	}
	
	// Update token in cache
	if uc.tokenCacheRepo != nil {
		// Remove old cache entries
		oldCacheKey := fmt.Sprintf("token_value:%s", req.Token)
		uc.tokenCacheRepo.Delete(ctx, oldCacheKey)
		
		userCacheKey := fmt.Sprintf("token:%s", existingToken.Username)
		uc.tokenCacheRepo.Delete(ctx, userCacheKey)
		
		// Add new cache entries
		newCacheKey := fmt.Sprintf("token_value:%s", newTokenValue)
		ttl := existingToken.GetRemainingTime()
		if ttl > 0 {
			if err := uc.tokenCacheRepo.Set(ctx, newCacheKey, existingToken, ttl); err != nil {
				// Log error but don't fail the operation
				fmt.Printf("Failed to cache refreshed token: %v\n", err)
			}
			
			if err := uc.tokenCacheRepo.Set(ctx, userCacheKey, existingToken, ttl); err != nil {
				// Log error but don't fail the operation
				fmt.Printf("Failed to cache refreshed token by user: %v\n", err)
			}
		}
	}
	
	duration := time.Since(startTime)
	uc.logRefreshEvent(ctx, requestID, "REFRESH_SUCCESS", "Token refreshed successfully", req, existingToken, duration)
	
	return &RefreshTokenResponse{
		Token:     existingToken,
		ExpiresIn: existingToken.GetRemainingTime(),
		Success:   true,
		Message:   "Token refreshed successfully",
		RequestID: requestID,
	}, nil
}

// getRefreshCount gets the current refresh count from token metadata
func (uc *RefreshTokenUseCase) getRefreshCount(token *entities.Token) int {
	if countStr, exists := token.GetMetadata("refresh_count"); exists {
		if count, err := fmt.Sscanf(countStr, "%d"); err == nil && count > 0 {
			return count
		}
	}
	return 0
}

// logRefreshEvent logs token refresh events
func (uc *RefreshTokenUseCase) logRefreshEvent(ctx context.Context, requestID, eventCode, message string, req *RefreshTokenRequest, token *entities.Token, duration time.Duration) {
	auditEvent := &repositories.AuditEvent{
		ID:      requestID,
		Type:    repositories.AuditEventTypeTokenRequest,
		Level:   repositories.AuditLevelInfo,
		Message: message,
		RequestData: map[string]interface{}{
			"event_code":   eventCode,
			"token_length": len(req.Token),
		},
		Duration:    &duration,
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}
	
	if token != nil {
		auditEvent.Username = &token.Username
		auditEvent.ResponseData = map[string]interface{}{
			"token_id":      token.ID,
			"username":      token.Username,
			"expires_at":    token.ExpiresAt,
			"refresh_count": uc.getRefreshCount(token),
		}
	}
	
	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log refresh audit event: %v\n", err)
	}
}

// logRefreshError logs token refresh errors
func (uc *RefreshTokenUseCase) logRefreshError(ctx context.Context, requestID, errorCode, errorMessage string, req *RefreshTokenRequest) {
	auditEvent := &repositories.AuditEvent{
		ID:           requestID + "-ERROR",
		Type:         repositories.AuditEventTypeAPIError,
		Level:        repositories.AuditLevelError,
		Message:      "Token refresh failed",
		ErrorCode:    &errorCode,
		ErrorMessage: &errorMessage,
		RequestData: map[string]interface{}{
			"token_length": len(req.Token),
		},
		SourceIP:    &req.SourceIP,
		UserAgent:   &req.UserAgent,
		ChannelType: &req.ChannelType,
		Timestamp:   time.Now(),
	}
	
	if err := uc.auditRepo.LogEvent(ctx, auditEvent); err != nil {
		fmt.Printf("Failed to log refresh error audit event: %v\n", err)
	}
}
