package entities

import (
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// TransactionType represents the type of transaction
type TransactionType string

const (
	TransactionTypeInquiry TransactionType = "INQUIRY"
	TransactionTypePayment TransactionType = "PAYMENT"
)

// TransactionState represents the state of a transaction
type TransactionState string

const (
	TransactionStateInitiated TransactionState = "INITIATED"
	TransactionStateProcessing TransactionState = "PROCESSING"
	TransactionStateCompleted TransactionState = "COMPLETED"
	TransactionStateFailed    TransactionState = "FAILED"
	TransactionStateCancelled TransactionState = "CANCELLED"
)

// Transaction represents a transaction entity in the domain
type Transaction struct {
	// Core identification
	ID        string                      `json:"id"`
	RequestID valueobjects.RequestID      `json:"request_id"`
	Type      TransactionType             `json:"type"`
	State     TransactionState            `json:"state"`
	
	// Related entities
	CompanyCode    valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber valueobjects.CustomerNumber `json:"customer_number"`
	
	// Transaction details
	ChannelType string `json:"channel_type"`
	
	// Metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	
	// Timestamps
	InitiatedAt time.Time  `json:"initiated_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	
	// Error information
	ErrorCode    string `json:"error_code,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

// NewTransaction creates a new transaction entity
func NewTransaction(
	id string,
	requestID valueobjects.RequestID,
	transactionType TransactionType,
	companyCode valueobjects.CompanyCode,
	customerNumber valueobjects.CustomerNumber,
	channelType string,
) (*Transaction, error) {
	now := time.Now()
	
	transaction := &Transaction{
		ID:             id,
		RequestID:      requestID,
		Type:           transactionType,
		State:          TransactionStateInitiated,
		CompanyCode:    companyCode,
		CustomerNumber: customerNumber,
		ChannelType:    channelType,
		Metadata:       make(map[string]interface{}),
		InitiatedAt:    now,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	
	if err := transaction.Validate(); err != nil {
		return nil, err
	}
	
	return transaction, nil
}

// Validate performs domain validation on the transaction entity
func (t *Transaction) Validate() error {
	if err := t.RequestID.Validate(); err != nil {
		return err
	}
	
	if err := t.CompanyCode.Validate(); err != nil {
		return err
	}
	
	if err := t.CustomerNumber.Validate(); err != nil {
		return err
	}
	
	if t.Type != TransactionTypeInquiry && t.Type != TransactionTypePayment {
		return ErrInvalidTransactionType
	}
	
	if t.ChannelType == "" {
		return ErrInvalidTransactionType
	}
	
	return nil
}

// StartProcessing marks the transaction as processing
func (t *Transaction) StartProcessing() error {
	if t.State != TransactionStateInitiated {
		return ErrInvalidTransactionState
	}
	
	t.State = TransactionStateProcessing
	t.UpdatedAt = time.Now()
	return nil
}

// Complete marks the transaction as completed
func (t *Transaction) Complete() error {
	if t.State != TransactionStateProcessing {
		return ErrInvalidTransactionState
	}
	
	t.State = TransactionStateCompleted
	now := time.Now()
	t.CompletedAt = &now
	t.UpdatedAt = now
	return nil
}

// Fail marks the transaction as failed
func (t *Transaction) Fail(errorCode, errorMessage string) error {
	if t.State == TransactionStateCompleted {
		return ErrInvalidTransactionState
	}
	
	t.State = TransactionStateFailed
	t.ErrorCode = errorCode
	t.ErrorMessage = errorMessage
	now := time.Now()
	t.CompletedAt = &now
	t.UpdatedAt = now
	return nil
}

// Cancel marks the transaction as cancelled
func (t *Transaction) Cancel() error {
	if t.State == TransactionStateCompleted || t.State == TransactionStateFailed {
		return ErrInvalidTransactionState
	}
	
	t.State = TransactionStateCancelled
	now := time.Now()
	t.CompletedAt = &now
	t.UpdatedAt = now
	return nil
}

// IsCompleted checks if the transaction is completed
func (t *Transaction) IsCompleted() bool {
	return t.State == TransactionStateCompleted
}

// IsFailed checks if the transaction failed
func (t *Transaction) IsFailed() bool {
	return t.State == TransactionStateFailed
}

// IsCancelled checks if the transaction was cancelled
func (t *Transaction) IsCancelled() bool {
	return t.State == TransactionStateCancelled
}

// IsProcessing checks if the transaction is currently processing
func (t *Transaction) IsProcessing() bool {
	return t.State == TransactionStateProcessing
}

// IsFinished checks if the transaction is in a final state
func (t *Transaction) IsFinished() bool {
	return t.IsCompleted() || t.IsFailed() || t.IsCancelled()
}

// SetMetadata sets metadata for the transaction
func (t *Transaction) SetMetadata(key string, value interface{}) {
	if t.Metadata == nil {
		t.Metadata = make(map[string]interface{})
	}
	t.Metadata[key] = value
	t.UpdatedAt = time.Now()
}

// GetMetadata gets metadata from the transaction
func (t *Transaction) GetMetadata(key string) (interface{}, bool) {
	if t.Metadata == nil {
		return nil, false
	}
	value, exists := t.Metadata[key]
	return value, exists
}

// GetDuration returns the duration of the transaction
func (t *Transaction) GetDuration() time.Duration {
	if t.CompletedAt != nil {
		return t.CompletedAt.Sub(t.InitiatedAt)
	}
	return time.Since(t.InitiatedAt)
}

// GetIdentifier returns a unique identifier for the transaction
func (t *Transaction) GetIdentifier() string {
	return string(t.CompanyCode) + "-" + string(t.CustomerNumber) + "-" + string(t.RequestID)
}

// Clone creates a deep copy of the transaction entity
func (t *Transaction) Clone() *Transaction {
	clone := *t
	
	// Deep copy metadata
	if t.Metadata != nil {
		clone.Metadata = make(map[string]interface{})
		for k, v := range t.Metadata {
			clone.Metadata[k] = v
		}
	}
	
	// Deep copy completed at
	if t.CompletedAt != nil {
		completedAt := *t.CompletedAt
		clone.CompletedAt = &completedAt
	}
	
	return &clone
}
