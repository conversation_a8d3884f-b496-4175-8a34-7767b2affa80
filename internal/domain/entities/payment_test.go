package entities

import (
	"testing"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

func TestNewPayment(t *testing.T) {
	tests := []struct {
		name         string
		companyCode  string
		customerNum  string
		customerName string
		currencyCode string
		paidAmount   string
		totalAmount  string
		reference    string
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "Valid payment",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			paidAmount:   "150000.00",
			totalAmount:  "150000.00",
			reference:    "REF123456",
			expectError:  false,
		},
		{
			name:         "Valid partial payment",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "<PERSON>",
			currencyCode: "IDR",
			paidAmount:   "100000.00",
			totalAmount:  "150000.00",
			reference:    "REF123456",
			expectError:  false,
		},
		{
			name:         "Valid USD payment",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "<PERSON>",
			currencyCode: "USD",
			paidAmount:   "100.50",
			totalAmount:  "100.50",
			reference:    "REF123456",
			expectError:  false,
		},
		{
			name:         "Empty customer name",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "",
			currencyCode: "IDR",
			paidAmount:   "150000.00",
			totalAmount:  "150000.00",
			reference:    "REF123456",
			expectError:  true,
			errorMsg:     "customer name cannot be empty",
		},
		{
			name:         "Zero paid amount",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			paidAmount:   "0.00",
			totalAmount:  "150000.00",
			reference:    "REF123456",
			expectError:  true,
			errorMsg:     "payment amount must be positive",
		},
		{
			name:         "Negative paid amount",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			paidAmount:   "-100.00",
			totalAmount:  "150000.00",
			reference:    "REF123456",
			expectError:  true,
			errorMsg:     "amount cannot be negative",
		},

		{
			name:         "Empty reference",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			paidAmount:   "150000.00",
			totalAmount:  "150000.00",
			reference:    "",
			expectError:  true,
			errorMsg:     "payment reference cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode, err := valueobjects.NewCompanyCode(tt.companyCode)
			if err != nil {
				t.Fatalf("Failed to create company code: %v", err)
			}

			customerNumber, err := valueobjects.NewCustomerNumber(tt.customerNum)
			if err != nil {
				t.Fatalf("Failed to create customer number: %v", err)
			}

			currencyCode, err := valueobjects.NewCurrencyCode(tt.currencyCode)
			if err != nil {
				t.Fatalf("Failed to create currency code: %v", err)
			}

			var paidAmount, totalAmount valueobjects.Amount
			if !tt.expectError || tt.errorMsg != "amount cannot be negative" {
				paidAmount, err = valueobjects.NewAmountFromString(tt.paidAmount, currencyCode)
				if err != nil && !tt.expectError {
					t.Fatalf("Failed to create paid amount: %v", err)
				}

				totalAmount, err = valueobjects.NewAmountFromString(tt.totalAmount, currencyCode)
				if err != nil && !tt.expectError {
					t.Fatalf("Failed to create total amount: %v", err)
				}
			}

			requestID, _ := valueobjects.NewRequestID("REQ*********")
			payment, err := NewPayment(
				"PAY*********",
				companyCode,
				customerNumber,
				requestID,
				tt.customerName,
				currencyCode,
				paidAmount,
				totalAmount,
				tt.reference,
				"BINA",
			)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for payment creation, but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if payment != nil {
					t.Errorf("Expected nil payment for invalid input, got %v", payment)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid payment: %v", err)
					return
				}
				if payment == nil {
					t.Errorf("Expected valid payment, got nil")
					return
				}

				// Verify payment properties
				if !payment.CompanyCode.Equals(companyCode) {
					t.Errorf("Expected company code %s, got %s", companyCode.String(), payment.CompanyCode.String())
				}
				if !payment.CustomerNumber.Equals(customerNumber) {
					t.Errorf("Expected customer number %s, got %s", customerNumber.String(), payment.CustomerNumber.String())
				}
				if payment.CustomerName != tt.customerName {
					t.Errorf("Expected customer name '%s', got '%s'", tt.customerName, payment.CustomerName)
				}
				if !payment.CurrencyCode.Equals(currencyCode) {
					t.Errorf("Expected currency code %s, got %s", currencyCode.String(), payment.CurrencyCode.String())
				}
				if !payment.PaidAmount.Equals(paidAmount) {
					t.Errorf("Expected paid amount %s, got %s", paidAmount.String(), payment.PaidAmount.String())
				}
				if !payment.TotalAmount.Equals(totalAmount) {
					t.Errorf("Expected total amount %s, got %s", totalAmount.String(), payment.TotalAmount.String())
				}
				if payment.Reference != tt.reference {
					t.Errorf("Expected reference '%s', got '%s'", tt.reference, payment.Reference)
				}

				// Verify default status
				if payment.Status != PaymentStatusPending {
					t.Errorf("Expected default status to be Pending, got %s", payment.Status)
				}

				// Verify timestamps
				if payment.CreatedAt.IsZero() {
					t.Errorf("Expected CreatedAt to be set")
				}
				if payment.UpdatedAt.IsZero() {
					t.Errorf("Expected UpdatedAt to be set")
				}
			}
		})
	}
}

func TestPaymentStatusTransitions(t *testing.T) {
	// Create valid payment
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	paidAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	requestID, _ := valueobjects.NewRequestID("REQ*********")
	payment, err := NewPayment(
		"PAY*********",
		companyCode,
		customerNumber,
		requestID,
		"John Doe",
		currencyCode,
		paidAmount,
		totalAmount,
		"REF123456",
		"BINA",
	)
	if err != nil {
		t.Fatalf("Failed to create payment: %v", err)
	}

	reason := PaymentFlagReason{
		Indonesian: "Pembayaran berhasil",
		English:    "Payment successful",
	}

	tests := []struct {
		name           string
		action         func() error
		expectError    bool
		expectedStatus PaymentStatus
	}{
		{
			name:           "Mark as Success",
			action:         func() error { return payment.MarkAsSuccess(reason) },
			expectError:    false,
			expectedStatus: PaymentStatusSuccess,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset payment status
			payment.Status = PaymentStatusPending
			oldUpdatedAt := payment.UpdatedAt

			// Wait a bit to ensure timestamp difference
			time.Sleep(1 * time.Millisecond)

			err := tt.action()

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for action")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid action: %v", err)
				}
				if payment.Status != tt.expectedStatus {
					t.Errorf("Expected status to be %s, got %s", tt.expectedStatus, payment.Status)
				}
				if !payment.UpdatedAt.After(oldUpdatedAt) {
					t.Errorf("Expected UpdatedAt to be updated after status change")
				}
			}
		})
	}

	// Test invalid transitions
	t.Run("Already processed payment", func(t *testing.T) {
		payment.Status = PaymentStatusSuccess
		err := payment.MarkAsFailed(reason)
		if err == nil {
			t.Errorf("Expected error when trying to change already processed payment")
		}
	})
}

func TestPaymentValidation(t *testing.T) {
	// Create valid payment
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	paidAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	requestID, _ := valueobjects.NewRequestID("REQ*********")
	payment, err := NewPayment(
		"PAY*********",
		companyCode,
		customerNumber,
		requestID,
		"John Doe",
		currencyCode,
		paidAmount,
		totalAmount,
		"REF123456",
		"BINA",
	)
	if err != nil {
		t.Fatalf("Failed to create payment: %v", err)
	}

	// Test validation
	err = payment.Validate()
	if err != nil {
		t.Errorf("Expected valid payment to pass validation, got error: %v", err)
	}
}

func TestPaymentBusinessRules(t *testing.T) {
	t.Run("Full payment", func(t *testing.T) {
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		amount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		requestID, _ := valueobjects.NewRequestID("REQ*********")

		payment, err := NewPayment(
			"PAY*********",
			companyCode,
			customerNumber,
			requestID,
			"John Doe",
			currencyCode,
			amount, // paid amount equals total amount
			amount, // total amount
			"REF123456",
			"BINA",
		)

		if err != nil {
			t.Errorf("Expected full payment to be valid, got error: %v", err)
		}

		// Check if paid amount equals total amount (full payment)
		if !payment.PaidAmount.Equals(payment.TotalAmount) {
			t.Errorf("Expected paid amount to equal total amount for full payment")
		}
	})

	t.Run("Partial payment", func(t *testing.T) {
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		paidAmount, _ := valueobjects.NewAmountFromString("100000.00", currencyCode)
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		requestID, _ := valueobjects.NewRequestID("REQ*********")

		payment, err := NewPayment(
			"PAY*********",
			companyCode,
			customerNumber,
			requestID,
			"John Doe",
			currencyCode,
			paidAmount,
			totalAmount,
			"REF123456",
			"BINA",
		)

		if err != nil {
			t.Errorf("Expected partial payment to be valid, got error: %v", err)
		}

		// Check if paid amount is less than total amount (partial payment)
		if !payment.PaidAmount.LessThan(payment.TotalAmount) {
			t.Errorf("Expected paid amount to be less than total amount for partial payment")
		}
	})

	t.Run("Currency consistency", func(t *testing.T) {
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		paidAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		requestID, _ := valueobjects.NewRequestID("REQ*********")

		payment, err := NewPayment(
			"PAY*********",
			companyCode,
			customerNumber,
			requestID,
			"John Doe",
			currencyCode,
			paidAmount,
			totalAmount,
			"REF123456",
			"BINA",
		)

		if err != nil {
			t.Fatalf("Failed to create payment: %v", err)
		}

		// Verify all currency codes match
		if !payment.CurrencyCode.Equals(payment.PaidAmount.Currency()) {
			t.Errorf("Payment currency code should match paid amount currency")
		}
		if !payment.CurrencyCode.Equals(payment.TotalAmount.Currency()) {
			t.Errorf("Payment currency code should match total amount currency")
		}
	})

	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test payment creation with OttoPay example data
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		paidAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)
		requestID, _ := valueobjects.NewRequestID("REQ*********")

		payment, err := NewPayment(
			"PAY*********",
			companyCode,
			customerNumber,
			requestID,
			"Customer Virtual Account",
			currencyCode,
			paidAmount,
			totalAmount,
			"*********0",
			"BINA",
		)

		if err != nil {
			t.Errorf("Expected OttoPay example payment to be valid, got error: %v", err)
		}
		if payment == nil {
			t.Errorf("Expected non-nil payment for OttoPay example")
		}
	})
}
