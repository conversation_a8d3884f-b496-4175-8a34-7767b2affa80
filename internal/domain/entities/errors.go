package entities

import "errors"

// Domain errors for entities
var (
	// Customer errors
	ErrInvalidCustomerName   = errors.New("customer name cannot be empty")
	ErrCustomerNameTooLong   = errors.New("customer name cannot exceed 30 characters")
	ErrAdditionalDataTooLong = errors.New("additional data cannot exceed 255 characters")

	// Payment errors
	ErrInvalidPaymentAmount    = errors.New("payment amount must be positive")
	ErrInvalidPaymentStatus    = errors.New("invalid payment status")
	ErrPaymentAlreadyProcessed = errors.New("payment has already been processed")
	ErrPaymentExpired          = errors.New("payment has expired")
	ErrInvalidReference        = errors.New("payment reference cannot be empty")
	ErrReferenceTooLong        = errors.New("payment reference cannot exceed 255 characters")

	// Transaction errors
	ErrInvalidTransactionType   = errors.New("invalid transaction type")
	ErrTransactionNotFound      = errors.New("transaction not found")
	ErrInvalidTransactionState  = errors.New("invalid transaction state")
	ErrTransactionAlreadyExists = errors.New("transaction already exists")

	// Token errors
	ErrTokenExpired  = errors.New("token has expired")
	ErrInvalidToken  = errors.New("invalid token")
	ErrTokenNotFound = errors.New("token not found")
)
