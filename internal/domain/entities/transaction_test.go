package entities

import (
	"testing"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

func TestNewTransaction(t *testing.T) {
	tests := []struct {
		name            string
		transactionID   string
		requestID       string
		transactionType TransactionType
		companyCode     string
		customerNum     string
		expectError     bool
		errorMsg        string
	}{
		{
			name:            "Valid inquiry transaction",
			transactionID:   "TXN123456789",
			requestID:       "REQ123456789",
			transactionType: TransactionTypeInquiry,
			companyCode:     "12173",
			customerNum:     "***********",
			expectError:     false,
		},
		{
			name:            "Valid payment transaction",
			transactionID:   "TXN123456789",
			requestID:       "REQ123456789",
			transactionType: TransactionTypePayment,
			companyCode:     "12173",
			customerNum:     "***********",
			expectError:     false,
		},

		{
			name:            "Invalid transaction type",
			transactionID:   "TXN123456789",
			requestID:       "REQ123456789",
			transactionType: TransactionType("INVALID"),
			companyCode:     "12173",
			customerNum:     "***********",
			expectError:     true,
			errorMsg:        "invalid transaction type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestID, err := valueobjects.NewRequestID(tt.requestID)
			if err != nil {
				t.Fatalf("Failed to create request ID: %v", err)
			}

			companyCode, err := valueobjects.NewCompanyCode(tt.companyCode)
			if err != nil {
				t.Fatalf("Failed to create company code: %v", err)
			}

			customerNumber, err := valueobjects.NewCustomerNumber(tt.customerNum)
			if err != nil {
				t.Fatalf("Failed to create customer number: %v", err)
			}

			transaction, err := NewTransaction(
				tt.transactionID,
				requestID,
				tt.transactionType,
				companyCode,
				customerNumber,
				"BINA",
			)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for transaction creation, but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if transaction != nil {
					t.Errorf("Expected nil transaction for invalid input, got %v", transaction)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid transaction: %v", err)
					return
				}
				if transaction == nil {
					t.Errorf("Expected valid transaction, got nil")
					return
				}

				// Verify transaction properties
				if transaction.ID != tt.transactionID {
					t.Errorf("Expected transaction ID '%s', got '%s'", tt.transactionID, transaction.ID)
				}
				if !transaction.RequestID.Equals(requestID) {
					t.Errorf("Expected request ID %s, got %s", requestID.String(), transaction.RequestID.String())
				}
				if transaction.Type != tt.transactionType {
					t.Errorf("Expected transaction type %s, got %s", tt.transactionType, transaction.Type)
				}
				if !transaction.CompanyCode.Equals(companyCode) {
					t.Errorf("Expected company code %s, got %s", companyCode.String(), transaction.CompanyCode.String())
				}
				if !transaction.CustomerNumber.Equals(customerNumber) {
					t.Errorf("Expected customer number %s, got %s", customerNumber.String(), transaction.CustomerNumber.String())
				}

				// Verify default state
				if transaction.State != TransactionStateInitiated {
					t.Errorf("Expected default state to be Initiated, got %s", transaction.State)
				}

				// Verify timestamps
				if transaction.CreatedAt.IsZero() {
					t.Errorf("Expected CreatedAt to be set")
				}
				if transaction.UpdatedAt.IsZero() {
					t.Errorf("Expected UpdatedAt to be set")
				}
			}
		})
	}
}

func TestTransactionStateTransitions(t *testing.T) {
	// Create valid transaction
	requestID, _ := valueobjects.NewRequestID("REQ123456789")
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")

	transaction, err := NewTransaction(
		"TXN123456789",
		requestID,
		TransactionTypeInquiry,
		companyCode,
		customerNumber,
		"BINA",
	)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	tests := []struct {
		name          string
		action        func() error
		expectError   bool
		expectedState TransactionState
	}{
		{
			name:          "Start Processing",
			action:        func() error { return transaction.StartProcessing() },
			expectError:   false,
			expectedState: TransactionStateProcessing,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset transaction state
			transaction.State = TransactionStateInitiated
			oldUpdatedAt := transaction.UpdatedAt

			// Wait a bit to ensure timestamp difference
			time.Sleep(1 * time.Millisecond)

			err := tt.action()

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for action")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid action: %v", err)
				}
				if transaction.State != tt.expectedState {
					t.Errorf("Expected state to be %s, got %s", tt.expectedState, transaction.State)
				}
				if !transaction.UpdatedAt.After(oldUpdatedAt) {
					t.Errorf("Expected UpdatedAt to be updated after state change")
				}
			}
		})
	}

	// Test complete lifecycle
	t.Run("Complete lifecycle", func(t *testing.T) {
		transaction.State = TransactionStateInitiated

		// Start processing
		err := transaction.StartProcessing()
		if err != nil {
			t.Errorf("Failed to start processing: %v", err)
		}
		if transaction.State != TransactionStateProcessing {
			t.Errorf("Expected state to be Processing, got %s", transaction.State)
		}

		// Complete
		err = transaction.Complete()
		if err != nil {
			t.Errorf("Failed to complete: %v", err)
		}
		if transaction.State != TransactionStateCompleted {
			t.Errorf("Expected state to be Completed, got %s", transaction.State)
		}

		// Try to change completed transaction (should fail)
		err = transaction.Fail("ERR001", "Test error")
		if err == nil {
			t.Errorf("Expected error when trying to change completed transaction")
		}
	})
}

func TestTransactionValidation(t *testing.T) {
	// Create valid transaction
	requestID, _ := valueobjects.NewRequestID("REQ123456789")
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")

	transaction, err := NewTransaction(
		"TXN123456789",
		requestID,
		TransactionTypeInquiry,
		companyCode,
		customerNumber,
		"BINA",
	)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	// Test validation
	err = transaction.Validate()
	if err != nil {
		t.Errorf("Expected valid transaction to pass validation, got error: %v", err)
	}
}

func TestTransactionFields(t *testing.T) {
	requestID, _ := valueobjects.NewRequestID("REQ123456789")
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")

	transaction, err := NewTransaction(
		"TXN123456789",
		requestID,
		TransactionTypePayment,
		companyCode,
		customerNumber,
		"BINA",
	)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	// Test field access
	if transaction.ID != "TXN123456789" {
		t.Errorf("Expected transaction ID 'TXN123456789', got '%s'", transaction.ID)
	}

	if transaction.RequestID.String() != "REQ123456789" {
		t.Errorf("Expected request ID 'REQ123456789', got '%s'", transaction.RequestID.String())
	}

	if transaction.Type != TransactionTypePayment {
		t.Errorf("Expected transaction type 'payment', got '%s'", transaction.Type)
	}

	if transaction.CompanyCode.String() != "12173" {
		t.Errorf("Expected company code '12173', got '%s'", transaction.CompanyCode.String())
	}

	if transaction.CustomerNumber.String() != "***********" {
		t.Errorf("Expected customer number '***********', got '%s'", transaction.CustomerNumber.String())
	}

	if transaction.State != TransactionStateInitiated {
		t.Errorf("Expected state 'initiated', got '%s'", transaction.State)
	}
}

func TestTransactionEquals(t *testing.T) {
	requestID, _ := valueobjects.NewRequestID("REQ123456789")
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")

	transaction1, _ := NewTransaction("TXN123456789", requestID, TransactionTypeInquiry, companyCode, customerNumber, "BINA")
	transaction2, _ := NewTransaction("TXN123456789", requestID, TransactionTypeInquiry, companyCode, customerNumber, "BINA")
	transaction3, _ := NewTransaction("TXN987654321", requestID, TransactionTypeInquiry, companyCode, customerNumber, "BINA")

	tests := []struct {
		name         string
		transaction1 *Transaction
		transaction2 *Transaction
		expected     bool
	}{
		{
			name:         "Same transactions",
			transaction1: transaction1,
			transaction2: transaction2,
			expected:     true,
		},
		{
			name:         "Different transaction IDs",
			transaction1: transaction1,
			transaction2: transaction3,
			expected:     false,
		},
		{
			name:         "Nil transaction",
			transaction1: transaction1,
			transaction2: nil,
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result bool
			if tt.transaction2 == nil {
				result = false
			} else {
				result = transaction1.ID == tt.transaction2.ID
			}
			if result != tt.expected {
				t.Errorf("Expected ID comparison to return %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestTransactionBusinessRules(t *testing.T) {
	t.Run("Transaction type validation", func(t *testing.T) {
		requestID, _ := valueobjects.NewRequestID("REQ123456789")
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")

		validTypes := []TransactionType{
			TransactionTypeInquiry,
			TransactionTypePayment,
		}

		for _, txType := range validTypes {
			transaction, err := NewTransaction(
				"TXN123456789",
				requestID,
				txType,
				companyCode,
				customerNumber,
				"BINA",
			)
			if err != nil {
				t.Errorf("Expected valid transaction type '%s' to be accepted, got error: %v", txType, err)
			}
			if transaction == nil {
				t.Errorf("Expected non-nil transaction for valid type '%s'", txType)
			}
		}
	})

	t.Run("Transaction ID uniqueness", func(t *testing.T) {
		// Test that transaction IDs should be unique
		requestID, _ := valueobjects.NewRequestID("REQ123456789")
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")

		transaction1, err := NewTransaction(
			"TXN123456789",
			requestID,
			TransactionTypeInquiry,
			companyCode,
			customerNumber,
			"BINA",
		)
		if err != nil {
			t.Fatalf("Failed to create first transaction: %v", err)
		}

		transaction2, err := NewTransaction(
			"TXN123456789",
			requestID,
			TransactionTypePayment,
			companyCode,
			customerNumber,
			"BINA",
		)
		if err != nil {
			t.Fatalf("Failed to create second transaction: %v", err)
		}

		// Both transactions should be equal based on ID
		if transaction1.ID != transaction2.ID {
			t.Errorf("Expected transactions with same ID to be equal")
		}
	})

	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test transaction creation with OttoPay example data
		requestID, _ := valueobjects.NewRequestID("201507131507262221400000001975")
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")

		transaction, err := NewTransaction(
			"TXN201507131507262221400000001975",
			requestID,
			TransactionTypeInquiry,
			companyCode,
			customerNumber,
			"BINA",
		)

		if err != nil {
			t.Errorf("Expected OttoPay example transaction to be valid, got error: %v", err)
		}
		if transaction == nil {
			t.Errorf("Expected non-nil transaction for OttoPay example")
		}
	})

	t.Run("State lifecycle", func(t *testing.T) {
		requestID, _ := valueobjects.NewRequestID("REQ123456789")
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")

		transaction, err := NewTransaction(
			"TXN123456789",
			requestID,
			TransactionTypeInquiry,
			companyCode,
			customerNumber,
			"BINA",
		)
		if err != nil {
			t.Fatalf("Failed to create transaction: %v", err)
		}

		// Test complete lifecycle: Initiated -> Processing -> Completed
		if transaction.State != TransactionStateInitiated {
			t.Errorf("Expected initial state to be Initiated, got %s", transaction.State)
		}

		err = transaction.StartProcessing()
		if err != nil {
			t.Errorf("Failed to start processing: %v", err)
		}

		if transaction.State != TransactionStateProcessing {
			t.Errorf("Expected state to be Processing, got %s", transaction.State)
		}

		err = transaction.Complete()
		if err != nil {
			t.Errorf("Failed to complete: %v", err)
		}

		if transaction.State != TransactionStateCompleted {
			t.Errorf("Expected state to be Completed, got %s", transaction.State)
		}

		// Test that final state cannot be changed
		err = transaction.Fail("ERR001", "Test error")
		if err == nil {
			t.Errorf("Expected error when trying to change from Completed to Failed")
		}
	})
}
