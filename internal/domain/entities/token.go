package entities

import (
	"time"
)

// TokenType represents the type of authentication token
type TokenType string

const (
	TokenTypeAccess  TokenType = "ACCESS"
	TokenTypeRefresh TokenType = "REFRESH"
)

// <PERSON><PERSON> represents an authentication token entity in the domain
type Token struct {
	// Core identification
	ID       string    `json:"id"`
	Type     TokenType `json:"type"`
	Value    string    `json:"value"`
	Username string    `json:"username"`
	
	// Token metadata
	Scope       string            `json:"scope,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	
	// Timestamps
	IssuedAt  time.Time `json:"issued_at"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// Status
	IsRevoked bool       `json:"is_revoked"`
	RevokedAt *time.Time `json:"revoked_at,omitempty"`
}

// NewToken creates a new token entity
func NewToken(
	id string,
	tokenType TokenType,
	value string,
	username string,
	expiresIn time.Duration,
) (*Token, error) {
	now := time.Now()
	
	token := &Token{
		ID:        id,
		Type:      tokenType,
		Value:     value,
		Username:  username,
		IssuedAt:  now,
		ExpiresAt: now.Add(expiresIn),
		CreatedAt: now,
		UpdatedAt: now,
		IsRevoked: false,
		Metadata:  make(map[string]string),
	}
	
	if err := token.Validate(); err != nil {
		return nil, err
	}
	
	return token, nil
}

// Validate performs domain validation on the token entity
func (t *Token) Validate() error {
	if t.ID == "" {
		return ErrInvalidToken
	}
	
	if t.Value == "" {
		return ErrInvalidToken
	}
	
	if t.Username == "" {
		return ErrInvalidToken
	}
	
	if t.Type != TokenTypeAccess && t.Type != TokenTypeRefresh {
		return ErrInvalidToken
	}
	
	if t.ExpiresAt.Before(t.IssuedAt) {
		return ErrInvalidToken
	}
	
	return nil
}

// IsExpired checks if the token has expired
func (t *Token) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}

// IsValid checks if the token is valid (not expired and not revoked)
func (t *Token) IsValid() bool {
	return !t.IsExpired() && !t.IsRevoked
}

// Revoke revokes the token
func (t *Token) Revoke() error {
	if t.IsRevoked {
		return ErrInvalidToken
	}
	
	t.IsRevoked = true
	now := time.Now()
	t.RevokedAt = &now
	t.UpdatedAt = now
	
	return nil
}

// Refresh creates a new token with extended expiration
func (t *Token) Refresh(newValue string, expiresIn time.Duration) error {
	if t.IsRevoked {
		return ErrInvalidToken
	}
	
	if t.Type != TokenTypeAccess {
		return ErrInvalidToken
	}
	
	now := time.Now()
	t.Value = newValue
	t.IssuedAt = now
	t.ExpiresAt = now.Add(expiresIn)
	t.UpdatedAt = now
	
	return nil
}

// SetScope sets the scope for the token
func (t *Token) SetScope(scope string) {
	t.Scope = scope
	t.UpdatedAt = time.Now()
}

// SetMetadata sets metadata for the token
func (t *Token) SetMetadata(key, value string) {
	if t.Metadata == nil {
		t.Metadata = make(map[string]string)
	}
	t.Metadata[key] = value
	t.UpdatedAt = time.Now()
}

// GetMetadata gets metadata from the token
func (t *Token) GetMetadata(key string) (string, bool) {
	if t.Metadata == nil {
		return "", false
	}
	value, exists := t.Metadata[key]
	return value, exists
}

// GetRemainingTime returns the remaining time until expiration
func (t *Token) GetRemainingTime() time.Duration {
	if t.IsExpired() {
		return 0
	}
	return time.Until(t.ExpiresAt)
}

// GetAge returns the age of the token
func (t *Token) GetAge() time.Duration {
	return time.Since(t.IssuedAt)
}

// ShouldRefresh checks if the token should be refreshed (expires within threshold)
func (t *Token) ShouldRefresh(threshold time.Duration) bool {
	return !t.IsRevoked && t.GetRemainingTime() <= threshold
}

// GetIdentifier returns a unique identifier for the token
func (t *Token) GetIdentifier() string {
	return t.Username + "-" + string(t.Type) + "-" + t.ID
}

// Clone creates a deep copy of the token entity
func (t *Token) Clone() *Token {
	clone := *t
	
	// Deep copy metadata
	if t.Metadata != nil {
		clone.Metadata = make(map[string]string)
		for k, v := range t.Metadata {
			clone.Metadata[k] = v
		}
	}
	
	// Deep copy revoked at
	if t.RevokedAt != nil {
		revokedAt := *t.RevokedAt
		clone.RevokedAt = &revokedAt
	}
	
	return &clone
}
