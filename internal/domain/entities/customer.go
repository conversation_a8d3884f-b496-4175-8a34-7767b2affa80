package entities

import (
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// Customer represents a customer entity in the domain
type Customer struct {
	// Core identification
	CompanyCode    valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber valueobjects.CustomerNumber `json:"customer_number"`
	
	// Customer details
	Name         string                    `json:"name"`
	CurrencyCode valueobjects.CurrencyCode `json:"currency_code"`
	
	// Financial information
	TotalAmount valueobjects.Amount `json:"total_amount"`
	SubCompany  string              `json:"sub_company"`
	
	// Additional data
	DetailBills    interface{} `json:"detail_bills,omitempty"`
	FreeTexts      interface{} `json:"free_texts,omitempty"`
	AdditionalData string      `json:"additional_data,omitempty"`
	
	// Metadata
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// NewCustomer creates a new customer entity with validation
func NewCustomer(
	companyCode valueobjects.CompanyCode,
	customerNumber valueobjects.CustomerNumber,
	name string,
	currencyCode valueobjects.CurrencyCode,
	totalAmount valueobjects.Amount,
) (*Customer, error) {
	now := time.Now()
	
	customer := &Customer{
		CompanyCode:    companyCode,
		CustomerNumber: customerNumber,
		Name:           name,
		CurrencyCode:   currencyCode,
		TotalAmount:    totalAmount,
		SubCompany:     "00000", // Default value
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	
	if err := customer.Validate(); err != nil {
		return nil, err
	}
	
	return customer, nil
}

// Validate performs domain validation on the customer entity
func (c *Customer) Validate() error {
	if err := c.CompanyCode.Validate(); err != nil {
		return err
	}
	
	if err := c.CustomerNumber.Validate(); err != nil {
		return err
	}
	
	if err := c.CurrencyCode.Validate(); err != nil {
		return err
	}
	
	if err := c.TotalAmount.Validate(); err != nil {
		return err
	}
	
	if c.Name == "" {
		return ErrInvalidCustomerName
	}
	
	if len(c.Name) > 30 {
		return ErrCustomerNameTooLong
	}
	
	return nil
}

// UpdateAmount updates the customer's total amount
func (c *Customer) UpdateAmount(amount valueobjects.Amount) error {
	if err := amount.Validate(); err != nil {
		return err
	}
	
	c.TotalAmount = amount
	c.UpdatedAt = time.Now()
	return nil
}

// SetAdditionalData sets additional data for the customer
func (c *Customer) SetAdditionalData(data string) error {
	if len(data) > 255 {
		return ErrAdditionalDataTooLong
	}
	
	c.AdditionalData = data
	c.UpdatedAt = time.Now()
	return nil
}

// SetDetailBills sets the detail bills for the customer
func (c *Customer) SetDetailBills(bills interface{}) {
	c.DetailBills = bills
	c.UpdatedAt = time.Now()
}

// SetFreeTexts sets the free texts for the customer
func (c *Customer) SetFreeTexts(texts interface{}) {
	c.FreeTexts = texts
	c.UpdatedAt = time.Now()
}

// IsActive checks if the customer is active (has a positive amount)
func (c *Customer) IsActive() bool {
	return c.TotalAmount.IsPositive()
}

// GetIdentifier returns a unique identifier for the customer
func (c *Customer) GetIdentifier() string {
	return string(c.CompanyCode) + "-" + string(c.CustomerNumber)
}

// Clone creates a deep copy of the customer entity
func (c *Customer) Clone() *Customer {
	clone := *c
	return &clone
}
