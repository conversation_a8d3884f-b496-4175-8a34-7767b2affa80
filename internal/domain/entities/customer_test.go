package entities

import (
	"testing"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

func TestNewCustomer(t *testing.T) {
	tests := []struct {
		name         string
		companyCode  string
		customerNum  string
		customerName string
		currencyCode string
		totalAmount  string
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "Valid customer",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			totalAmount:  "150000.00",
			expectError:  false,
		},
		{
			name:         "Valid customer with USD",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "<PERSON>",
			currencyCode: "USD",
			totalAmount:  "100.50",
			expectError:  false,
		},
		{
			name:         "Empty customer name",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "",
			currencyCode: "IDR",
			totalAmount:  "150000.00",
			expectError:  true,
			errorMsg:     "customer name cannot be empty",
		},
		{
			name:         "Customer name too long",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "This is a very long customer name that exceeds the maximum allowed length for customer names in the system which should be limited to a reasonable number of characters",
			currencyCode: "IDR",
			totalAmount:  "150000.00",
			expectError:  true,
			errorMsg:     "customer name cannot exceed 30 characters",
		},
		{
			name:         "Zero amount",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "<PERSON>",
			currencyCode: "IDR",
			totalAmount:  "0.00",
			expectError:  false,
		},
		{
			name:         "Negative amount",
			companyCode:  "12173",
			customerNum:  "***********",
			customerName: "John Doe",
			currencyCode: "IDR",
			totalAmount:  "-100.00",
			expectError:  true,
			errorMsg:     "amount cannot be negative",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode, err := valueobjects.NewCompanyCode(tt.companyCode)
			if err != nil {
				t.Fatalf("Failed to create company code: %v", err)
			}

			customerNumber, err := valueobjects.NewCustomerNumber(tt.customerNum)
			if err != nil {
				t.Fatalf("Failed to create customer number: %v", err)
			}

			currencyCode, err := valueobjects.NewCurrencyCode(tt.currencyCode)
			if err != nil {
				t.Fatalf("Failed to create currency code: %v", err)
			}

			totalAmount, err := valueobjects.NewAmountFromString(tt.totalAmount, currencyCode)
			if err != nil {
				if tt.expectError {
					// If we expect an error and got one during amount creation,
					// check if it's the expected error
					if tt.errorMsg != "" && err.Error() != tt.errorMsg {
						t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
					}
					return
				}
				t.Fatalf("Failed to create amount: %v", err)
			}

			customer, err := NewCustomer(
				companyCode,
				customerNumber,
				tt.customerName,
				currencyCode,
				totalAmount,
			)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for customer creation, but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if customer != nil {
					t.Errorf("Expected nil customer for invalid input, got %v", customer)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid customer: %v", err)
					return
				}
				if customer == nil {
					t.Errorf("Expected valid customer, got nil")
					return
				}

				// Verify customer properties
				if !customer.CompanyCode.Equals(companyCode) {
					t.Errorf("Expected company code %s, got %s", companyCode.String(), customer.CompanyCode.String())
				}
				if !customer.CustomerNumber.Equals(customerNumber) {
					t.Errorf("Expected customer number %s, got %s", customerNumber.String(), customer.CustomerNumber.String())
				}
				if customer.Name != tt.customerName {
					t.Errorf("Expected customer name '%s', got '%s'", tt.customerName, customer.Name)
				}
				if !customer.CurrencyCode.Equals(currencyCode) {
					t.Errorf("Expected currency code %s, got %s", currencyCode.String(), customer.CurrencyCode.String())
				}
				if !customer.TotalAmount.Equals(totalAmount) {
					t.Errorf("Expected total amount %s, got %s", totalAmount.String(), customer.TotalAmount.String())
				}
			}
		})
	}
}

func TestCustomerValidation(t *testing.T) {
	// Create valid customer
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	customer, err := NewCustomer(
		companyCode,
		customerNumber,
		"John Doe",
		currencyCode,
		totalAmount,
	)
	if err != nil {
		t.Fatalf("Failed to create valid customer: %v", err)
	}

	// Test validation
	err = customer.Validate()
	if err != nil {
		t.Errorf("Expected valid customer to pass validation, got error: %v", err)
	}
}

func TestCustomerGetters(t *testing.T) {
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	customer, err := NewCustomer(
		companyCode,
		customerNumber,
		"John Doe",
		currencyCode,
		totalAmount,
	)
	if err != nil {
		t.Fatalf("Failed to create customer: %v", err)
	}

	// Test field access
	if customer.CompanyCode.String() != "12173" {
		t.Errorf("Expected company code '12173', got '%s'", customer.CompanyCode.String())
	}

	if customer.CustomerNumber.String() != "***********" {
		t.Errorf("Expected customer number '***********', got '%s'", customer.CustomerNumber.String())
	}

	if customer.Name != "John Doe" {
		t.Errorf("Expected customer name 'John Doe', got '%s'", customer.Name)
	}

	if customer.CurrencyCode.String() != "IDR" {
		t.Errorf("Expected currency code 'IDR', got '%s'", customer.CurrencyCode.String())
	}

	if customer.TotalAmount.String() != "150000.00" {
		t.Errorf("Expected total amount '150000.00', got '%s'", customer.TotalAmount.String())
	}
}

func TestCustomerEquals(t *testing.T) {
	companyCode, _ := valueobjects.NewCompanyCode("12173")
	customerNumber, _ := valueobjects.NewCustomerNumber("***********")
	currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
	totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

	customer1, _ := NewCustomer(companyCode, customerNumber, "John Doe", currencyCode, totalAmount)
	customer2, _ := NewCustomer(companyCode, customerNumber, "John Doe", currencyCode, totalAmount)

	// Different customer number
	customerNumber2, _ := valueobjects.NewCustomerNumber("56751590098")
	customer3, _ := NewCustomer(companyCode, customerNumber2, "John Doe", currencyCode, totalAmount)

	tests := []struct {
		name      string
		customer1 *Customer
		customer2 *Customer
		expected  bool
	}{
		{
			name:      "Same customers",
			customer1: customer1,
			customer2: customer2,
			expected:  true,
		},
		{
			name:      "Different customer numbers",
			customer1: customer1,
			customer2: customer3,
			expected:  false,
		},
		{
			name:      "Nil customer",
			customer1: customer1,
			customer2: nil,
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result bool
			if tt.customer2 == nil {
				result = false
			} else {
				result = customer1.CompanyCode.Equals(tt.customer2.CompanyCode) &&
					customer1.CustomerNumber.Equals(tt.customer2.CustomerNumber)
			}
			if result != tt.expected {
				t.Errorf("Expected comparison to return %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestCustomerBusinessRules(t *testing.T) {
	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test customer creation with OttoPay example data
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

		customer, err := NewCustomer(
			companyCode,
			customerNumber,
			"Customer Virtual Account",
			currencyCode,
			totalAmount,
		)

		if err != nil {
			t.Errorf("Expected OttoPay example customer to be valid, got error: %v", err)
		}
		if customer == nil {
			t.Errorf("Expected non-nil customer for OttoPay example")
		}
	})

	t.Run("Currency consistency", func(t *testing.T) {
		// Test that customer currency matches amount currency
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

		customer, err := NewCustomer(
			companyCode,
			customerNumber,
			"John Doe",
			currencyCode,
			totalAmount,
		)

		if err != nil {
			t.Fatalf("Failed to create customer: %v", err)
		}

		if !customer.CurrencyCode.Equals(customer.TotalAmount.Currency()) {
			t.Errorf("Customer currency code should match amount currency")
		}
	})

	t.Run("Customer name validation", func(t *testing.T) {
		companyCode, _ := valueobjects.NewCompanyCode("12173")
		customerNumber, _ := valueobjects.NewCustomerNumber("***********")
		currencyCode, _ := valueobjects.NewCurrencyCode("IDR")
		totalAmount, _ := valueobjects.NewAmountFromString("150000.00", currencyCode)

		// Test various customer names
		validNames := []string{
			"John Doe",
			"PT. Contoh Perusahaan",
			"Customer 123",
			"A", // Single character
		}

		for _, name := range validNames {
			customer, err := NewCustomer(companyCode, customerNumber, name, currencyCode, totalAmount)
			if err != nil {
				t.Errorf("Expected valid customer name '%s' to be accepted, got error: %v", name, err)
			}
			if customer == nil {
				t.Errorf("Expected non-nil customer for valid name '%s'", name)
			}
		}
	})
}
