package entities

import (
	"testing"
	"time"
)

func TestNewToken(t *testing.T) {
	tests := []struct {
		name        string
		id          string
		tokenType   TokenType
		value       string
		username    string
		duration    time.Duration
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid access token",
			id:          "token-123",
			tokenType:   TokenTypeAccess,
			value:       "access-token-value",
			username:    "testuser",
			duration:    24 * time.Hour,
			expectError: false,
		},
		{
			name:        "Valid refresh token",
			id:          "refresh-123",
			tokenType:   TokenTypeRefresh,
			value:       "refresh-token-value",
			username:    "testuser",
			duration:    7 * 24 * time.Hour,
			expectError: false,
		},
		{
			name:        "Empty ID",
			id:          "",
			tokenType:   TokenTypeAccess,
			value:       "token-value",
			username:    "testuser",
			duration:    24 * time.Hour,
			expectError: true,
			errorMsg:    "invalid token",
		},
		{
			name:        "Empty value",
			id:          "token-123",
			tokenType:   TokenTypeAccess,
			value:       "",
			username:    "testuser",
			duration:    24 * time.Hour,
			expectError: true,
			errorMsg:    "invalid token",
		},
		{
			name:        "Empty username",
			id:          "token-123",
			tokenType:   TokenTypeAccess,
			value:       "token-value",
			username:    "",
			duration:    24 * time.Hour,
			expectError: true,
			errorMsg:    "invalid token",
		},
		{
			name:        "Invalid token type",
			id:          "token-123",
			tokenType:   "INVALID",
			value:       "token-value",
			username:    "testuser",
			duration:    24 * time.Hour,
			expectError: true,
			errorMsg:    "invalid token",
		},
		{
			name:        "Zero duration - should be allowed",
			id:          "token-123",
			tokenType:   TokenTypeAccess,
			value:       "token-value",
			username:    "testuser",
			duration:    0,
			expectError: false, // Zero duration might be allowed
		},
		{
			name:        "Negative duration",
			id:          "token-123",
			tokenType:   TokenTypeAccess,
			value:       "token-value",
			username:    "testuser",
			duration:    -1 * time.Hour,
			expectError: true,
			errorMsg:    "invalid token",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := NewToken(tt.id, tt.tokenType, tt.value, tt.username, tt.duration)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if token.ID != tt.id {
				t.Errorf("Expected ID %s, got %s", tt.id, token.ID)
			}

			if token.Type != tt.tokenType {
				t.Errorf("Expected type %s, got %s", tt.tokenType, token.Type)
			}

			if token.Value != tt.value {
				t.Errorf("Expected value %s, got %s", tt.value, token.Value)
			}

			if token.Username != tt.username {
				t.Errorf("Expected username %s, got %s", tt.username, token.Username)
			}

			if token.IsRevoked {
				t.Errorf("Expected token to not be revoked initially")
			}

			// Check that expiration time is set correctly
			expectedExpiry := time.Now().Add(tt.duration)
			tolerance := 5 * time.Second // Allow 5 seconds tolerance
			if token.ExpiresAt.Before(expectedExpiry.Add(-tolerance)) || token.ExpiresAt.After(expectedExpiry.Add(tolerance)) {
				t.Errorf("Expected expiry around %v, got %v", expectedExpiry, token.ExpiresAt)
			}
		})
	}
}

func TestTokenValidation(t *testing.T) {
	// Create a valid token
	token, err := NewToken("test-id", TokenTypeAccess, "test-value", "testuser", 24*time.Hour)
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	t.Run("Valid token", func(t *testing.T) {
		if !token.IsValid() {
			t.Errorf("Expected token to be valid")
		}
	})

	t.Run("Expired token", func(t *testing.T) {
		// Create an expired token by creating a valid one and modifying its expiry
		expiredToken, err := NewToken("expired-id", TokenTypeAccess, "expired-value", "testuser", 1*time.Hour)
		if err != nil {
			t.Fatalf("Failed to create token: %v", err)
		}

		// Manually set expiry to past
		expiredToken.ExpiresAt = time.Now().Add(-1 * time.Hour)

		if expiredToken.IsValid() {
			t.Errorf("Expected expired token to be invalid")
		}

		if !expiredToken.IsExpired() {
			t.Errorf("Expected token to be expired")
		}
	})

	t.Run("Revoked token", func(t *testing.T) {
		// Clone the token and revoke it
		revokedToken := token.Clone()
		err := revokedToken.Revoke()
		if err != nil {
			t.Errorf("Unexpected error revoking token: %v", err)
		}

		if revokedToken.IsValid() {
			t.Errorf("Expected revoked token to be invalid")
		}

		if !revokedToken.IsRevoked {
			t.Errorf("Expected token to be marked as revoked")
		}
	})
}

func TestTokenRefresh(t *testing.T) {
	token, err := NewToken("test-id", TokenTypeAccess, "old-value", "testuser", 24*time.Hour)
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	oldValue := token.Value
	oldExpiry := token.ExpiresAt
	newDuration := 48 * time.Hour

	t.Run("Successful refresh", func(t *testing.T) {
		err := token.Refresh("new-value", newDuration)
		if err != nil {
			t.Errorf("Unexpected error refreshing token: %v", err)
		}

		if token.Value == oldValue {
			t.Errorf("Expected token value to change after refresh")
		}

		if token.Value != "new-value" {
			t.Errorf("Expected new value 'new-value', got '%s'", token.Value)
		}

		if !token.ExpiresAt.After(oldExpiry) {
			t.Errorf("Expected expiry to be extended after refresh")
		}

		if token.IsRevoked {
			t.Errorf("Expected token to not be revoked after refresh")
		}
	})

	t.Run("Refresh revoked token", func(t *testing.T) {
		revokedToken := token.Clone()
		revokedToken.Revoke()

		err := revokedToken.Refresh("newer-value", 24*time.Hour)
		if err == nil {
			t.Errorf("Expected error when refreshing revoked token")
		}
	})

	t.Run("Refresh with empty value", func(t *testing.T) {
		err := token.Refresh("", 24*time.Hour)
		if err != nil {
			t.Errorf("Unexpected error when refreshing with empty value: %v", err)
		}
		// The implementation allows empty values
	})

	t.Run("Refresh with zero duration", func(t *testing.T) {
		err := token.Refresh("new-value", 0)
		if err != nil {
			t.Errorf("Unexpected error when refreshing with zero duration: %v", err)
		}
		// The implementation allows zero duration
	})
}

func TestTokenShouldRefresh(t *testing.T) {
	tests := []struct {
		name           string
		tokenDuration  time.Duration
		threshold      time.Duration
		expectedResult bool
	}{
		{
			name:           "Token expires soon",
			tokenDuration:  2 * time.Minute,
			threshold:      5 * time.Minute,
			expectedResult: true,
		},
		{
			name:           "Token expires later",
			tokenDuration:  10 * time.Minute,
			threshold:      5 * time.Minute,
			expectedResult: false,
		},
		{
			name:           "Token expires exactly at threshold",
			tokenDuration:  5 * time.Minute,
			threshold:      5 * time.Minute,
			expectedResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := NewToken("test-id", TokenTypeAccess, "test-value", "testuser", tt.tokenDuration)
			if err != nil {
				t.Fatalf("Failed to create test token: %v", err)
			}

			result := token.ShouldRefresh(tt.threshold)
			if result != tt.expectedResult {
				t.Errorf("Expected ShouldRefresh to return %v, got %v", tt.expectedResult, result)
			}
		})
	}
}

func TestTokenGetRemainingTime(t *testing.T) {
	duration := 1 * time.Hour
	token, err := NewToken("test-id", TokenTypeAccess, "test-value", "testuser", duration)
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	remaining := token.GetRemainingTime()

	// Should be close to the original duration (within a few seconds)
	tolerance := 5 * time.Second
	if remaining < duration-tolerance || remaining > duration+tolerance {
		t.Errorf("Expected remaining time around %v, got %v", duration, remaining)
	}

	// Test expired token
	expiredToken, err := NewToken("expired-id", TokenTypeAccess, "expired-value", "testuser", 1*time.Hour)
	if err != nil {
		t.Fatalf("Failed to create expired token: %v", err)
	}
	// Manually set expiry to past
	expiredToken.ExpiresAt = time.Now().Add(-1 * time.Hour)

	expiredRemaining := expiredToken.GetRemainingTime()
	if expiredRemaining > 0 {
		t.Errorf("Expected expired token to have non-positive remaining time, got %v", expiredRemaining)
	}
}

func TestTokenMetadata(t *testing.T) {
	token, err := NewToken("test-id", TokenTypeAccess, "test-value", "testuser", 24*time.Hour)
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	t.Run("Set and get metadata", func(t *testing.T) {
		token.SetMetadata("key1", "value1")
		token.SetMetadata("key2", "value2")

		value1, exists1 := token.GetMetadata("key1")
		if !exists1 || value1 != "value1" {
			t.Errorf("Expected metadata key1=value1, got %s (exists: %v)", value1, exists1)
		}

		value2, exists2 := token.GetMetadata("key2")
		if !exists2 || value2 != "value2" {
			t.Errorf("Expected metadata key2=value2, got %s (exists: %v)", value2, exists2)
		}
	})

	t.Run("Get non-existent metadata", func(t *testing.T) {
		value, exists := token.GetMetadata("nonexistent")
		if exists {
			t.Errorf("Expected metadata key to not exist, but got value: %s", value)
		}
	})

	t.Run("Overwrite metadata", func(t *testing.T) {
		token.SetMetadata("overwrite", "original")
		token.SetMetadata("overwrite", "modified")

		value, exists := token.GetMetadata("overwrite")
		if !exists || value != "modified" {
			t.Errorf("Expected metadata to be overwritten, got %s (exists: %v)", value, exists)
		}
	})
}

func TestTokenClone(t *testing.T) {
	original, err := NewToken("test-id", TokenTypeAccess, "test-value", "testuser", 24*time.Hour)
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	original.SetMetadata("test", "value")

	clone := original.Clone()

	// Check that all fields are copied
	if clone.ID != original.ID {
		t.Errorf("Expected cloned ID to match original")
	}

	if clone.Type != original.Type {
		t.Errorf("Expected cloned type to match original")
	}

	if clone.Value != original.Value {
		t.Errorf("Expected cloned value to match original")
	}

	if clone.Username != original.Username {
		t.Errorf("Expected cloned username to match original")
	}

	if !clone.ExpiresAt.Equal(original.ExpiresAt) {
		t.Errorf("Expected cloned expiry to match original")
	}

	if clone.IsRevoked != original.IsRevoked {
		t.Errorf("Expected cloned revoked status to match original")
	}

	// Check metadata is copied
	value, exists := clone.GetMetadata("test")
	if !exists || value != "value" {
		t.Errorf("Expected cloned metadata to match original")
	}

	// Check that modifying clone doesn't affect original
	clone.SetMetadata("test", "modified")
	originalValue, _ := original.GetMetadata("test")
	if originalValue != "value" {
		t.Errorf("Expected original metadata to remain unchanged after modifying clone")
	}
}
