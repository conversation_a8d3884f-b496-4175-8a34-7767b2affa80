package entities

import (
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// PaymentStatus represents the status of a payment
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "PENDING"
	PaymentStatusSuccess   PaymentStatus = "SUCCESS"
	PaymentStatusFailed    PaymentStatus = "FAILED"
	PaymentStatusTimeout   PaymentStatus = "TIMEOUT"
	PaymentStatusCancelled PaymentStatus = "CANCELLED"
)

// PaymentFlagReason represents the payment flag reason in multiple languages
type PaymentFlagReason struct {
	Indonesian string `json:"indonesian"`
	English    string `json:"english"`
}

// Payment represents a payment entity in the domain
type Payment struct {
	// Core identification
	ID             string                      `json:"id"`
	CompanyCode    valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber valueobjects.CustomerNumber `json:"customer_number"`
	RequestID      valueobjects.RequestID      `json:"request_id"`
	
	// Payment details
	CustomerName string                    `json:"customer_name"`
	CurrencyCode valueobjects.CurrencyCode `json:"currency_code"`
	PaidAmount   valueobjects.Amount       `json:"paid_amount"`
	TotalAmount  valueobjects.Amount       `json:"total_amount"`
	Reference    string                    `json:"reference"`
	
	// Channel and processing info
	ChannelType string `json:"channel_type"`
	SubCompany  string `json:"sub_company"`
	
	// Status and reason
	Status PaymentStatus     `json:"status"`
	Reason PaymentFlagReason `json:"reason"`
	
	// Additional data
	DetailBills    interface{} `json:"detail_bills,omitempty"`
	FreeTexts      interface{} `json:"free_texts,omitempty"`
	AdditionalData string      `json:"additional_data,omitempty"`
	
	// Timestamps
	TransactionDate time.Time `json:"transaction_date"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	ProcessedAt     *time.Time `json:"processed_at,omitempty"`
}

// NewPayment creates a new payment entity with validation
func NewPayment(
	id string,
	companyCode valueobjects.CompanyCode,
	customerNumber valueobjects.CustomerNumber,
	requestID valueobjects.RequestID,
	customerName string,
	currencyCode valueobjects.CurrencyCode,
	paidAmount valueobjects.Amount,
	totalAmount valueobjects.Amount,
	reference string,
	channelType string,
) (*Payment, error) {
	now := time.Now()
	
	payment := &Payment{
		ID:              id,
		CompanyCode:     companyCode,
		CustomerNumber:  customerNumber,
		RequestID:       requestID,
		CustomerName:    customerName,
		CurrencyCode:    currencyCode,
		PaidAmount:      paidAmount,
		TotalAmount:     totalAmount,
		Reference:       reference,
		ChannelType:     channelType,
		SubCompany:      "00000", // Default value
		Status:          PaymentStatusPending,
		TransactionDate: now,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
	
	if err := payment.Validate(); err != nil {
		return nil, err
	}
	
	return payment, nil
}

// Validate performs domain validation on the payment entity
func (p *Payment) Validate() error {
	if err := p.CompanyCode.Validate(); err != nil {
		return err
	}
	
	if err := p.CustomerNumber.Validate(); err != nil {
		return err
	}
	
	if err := p.RequestID.Validate(); err != nil {
		return err
	}
	
	if err := p.CurrencyCode.Validate(); err != nil {
		return err
	}
	
	if err := p.PaidAmount.Validate(); err != nil {
		return err
	}
	
	if err := p.TotalAmount.Validate(); err != nil {
		return err
	}
	
	if p.CustomerName == "" {
		return ErrInvalidCustomerName
	}
	
	if len(p.CustomerName) > 30 {
		return ErrCustomerNameTooLong
	}
	
	if p.Reference == "" {
		return ErrInvalidReference
	}
	
	if len(p.Reference) > 255 {
		return ErrReferenceTooLong
	}
	
	if !p.PaidAmount.IsPositive() {
		return ErrInvalidPaymentAmount
	}
	
	return nil
}

// MarkAsSuccess marks the payment as successful
func (p *Payment) MarkAsSuccess(reason PaymentFlagReason) error {
	if p.IsProcessed() {
		return ErrPaymentAlreadyProcessed
	}
	
	p.Status = PaymentStatusSuccess
	p.Reason = reason
	now := time.Now()
	p.ProcessedAt = &now
	p.UpdatedAt = now
	
	return nil
}

// MarkAsFailed marks the payment as failed
func (p *Payment) MarkAsFailed(reason PaymentFlagReason) error {
	if p.IsProcessed() {
		return ErrPaymentAlreadyProcessed
	}
	
	p.Status = PaymentStatusFailed
	p.Reason = reason
	now := time.Now()
	p.ProcessedAt = &now
	p.UpdatedAt = now
	
	return nil
}

// MarkAsTimeout marks the payment as timed out
func (p *Payment) MarkAsTimeout(reason PaymentFlagReason) error {
	if p.IsProcessed() {
		return ErrPaymentAlreadyProcessed
	}
	
	p.Status = PaymentStatusTimeout
	p.Reason = reason
	now := time.Now()
	p.ProcessedAt = &now
	p.UpdatedAt = now
	
	return nil
}

// Cancel cancels the payment
func (p *Payment) Cancel(reason PaymentFlagReason) error {
	if p.IsProcessed() {
		return ErrPaymentAlreadyProcessed
	}
	
	p.Status = PaymentStatusCancelled
	p.Reason = reason
	now := time.Now()
	p.ProcessedAt = &now
	p.UpdatedAt = now
	
	return nil
}

// IsProcessed checks if the payment has been processed
func (p *Payment) IsProcessed() bool {
	return p.Status != PaymentStatusPending
}

// IsSuccess checks if the payment was successful
func (p *Payment) IsSuccess() bool {
	return p.Status == PaymentStatusSuccess
}

// IsFailed checks if the payment failed
func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

// IsTimeout checks if the payment timed out
func (p *Payment) IsTimeout() bool {
	return p.Status == PaymentStatusTimeout
}

// IsCancelled checks if the payment was cancelled
func (p *Payment) IsCancelled() bool {
	return p.Status == PaymentStatusCancelled
}

// SetAdditionalData sets additional data for the payment
func (p *Payment) SetAdditionalData(data string) error {
	if len(data) > 255 {
		return ErrAdditionalDataTooLong
	}
	
	p.AdditionalData = data
	p.UpdatedAt = time.Now()
	return nil
}

// SetDetailBills sets the detail bills for the payment
func (p *Payment) SetDetailBills(bills interface{}) {
	p.DetailBills = bills
	p.UpdatedAt = time.Now()
}

// SetFreeTexts sets the free texts for the payment
func (p *Payment) SetFreeTexts(texts interface{}) {
	p.FreeTexts = texts
	p.UpdatedAt = time.Now()
}

// GetIdentifier returns a unique identifier for the payment
func (p *Payment) GetIdentifier() string {
	return string(p.CompanyCode) + "-" + string(p.CustomerNumber) + "-" + string(p.RequestID)
}

// Clone creates a deep copy of the payment entity
func (p *Payment) Clone() *Payment {
	clone := *p
	if p.ProcessedAt != nil {
		processedAt := *p.ProcessedAt
		clone.ProcessedAt = &processedAt
	}
	return &clone
}
