package services

import (
	"context"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// OttoPayService defines the core business operations for OttoPay integration
type OttoPayService interface {
	// Authentication operations
	AuthenticateUser(ctx context.Context, username, password string) (*AuthenticationResult, error)
	RefreshToken(ctx context.Context, token string) (*AuthenticationResult, error)
	ValidateToken(ctx context.Context, token string) (*TokenValidationResult, error)
	RevokeToken(ctx context.Context, token string) error

	// Customer inquiry operations
	InquireCustomer(ctx context.Context, req *CustomerInquiryRequest) (*CustomerInquiryResult, error)
	ValidateInquiryRequest(ctx context.Context, req *CustomerInquiryRequest) error

	// Payment operations
	ProcessPayment(ctx context.Context, req *PaymentProcessingRequest) (*PaymentProcessingResult, error)
	ValidatePaymentRequest(ctx context.Context, req *PaymentProcessingRequest) error
	GetPaymentStatus(ctx context.Context, requestID valueobjects.RequestID) (*PaymentStatusResult, error)
	CancelPayment(ctx context.Context, requestID valueobjects.RequestID, reason string) error

	// Transaction management
	CreateTransaction(ctx context.Context, req *TransactionCreationRequest) (*entities.Transaction, error)
	UpdateTransactionStatus(ctx context.Context, transactionID string, status entities.TransactionState) error
	GetTransactionHistory(ctx context.Context, filter *TransactionFilter) ([]*entities.Transaction, error)

	// Health and monitoring
	CheckServiceHealth(ctx context.Context) (*ServiceHealthResult, error)
	GetServiceMetrics(ctx context.Context) (*ServiceMetrics, error)
}

// AuthenticationResult represents the result of authentication
type AuthenticationResult struct {
	Token     *entities.Token `json:"token"`
	ExpiresIn time.Duration   `json:"expires_in"`
	Success   bool            `json:"success"`
	Message   string          `json:"message"`
}

// TokenValidationResult represents the result of token validation
type TokenValidationResult struct {
	Valid     bool            `json:"valid"`
	Token     *entities.Token `json:"token,omitempty"`
	ExpiresIn time.Duration   `json:"expires_in"`
	Reason    string          `json:"reason,omitempty"`
}

// CustomerInquiryRequest represents a customer inquiry request
type CustomerInquiryRequest struct {
	CompanyCode     valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber  valueobjects.CustomerNumber `json:"customer_number"`
	RequestID       valueobjects.RequestID      `json:"request_id"`
	ChannelType     string                      `json:"channel_type"`
	TransactionDate *time.Time                  `json:"transaction_date,omitempty"`
	AdditionalData  string                      `json:"additional_data,omitempty"`
}

// CustomerInquiryResult represents the result of customer inquiry
type CustomerInquiryResult struct {
	Success       bool                       `json:"success"`
	Customer      *entities.Customer         `json:"customer,omitempty"`
	InquiryStatus string                     `json:"inquiry_status"`
	InquiryReason entities.PaymentFlagReason `json:"inquiry_reason"`
	Message       string                     `json:"message"`
	ResponseTime  time.Duration              `json:"response_time"`
}

// PaymentProcessingRequest represents a payment processing request
type PaymentProcessingRequest struct {
	CompanyCode     valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber  valueobjects.CustomerNumber `json:"customer_number"`
	RequestID       valueobjects.RequestID      `json:"request_id"`
	ChannelType     string                      `json:"channel_type"`
	CustomerName    string                      `json:"customer_name"`
	CurrencyCode    valueobjects.CurrencyCode   `json:"currency_code"`
	PaidAmount      valueobjects.Amount         `json:"paid_amount"`
	TotalAmount     valueobjects.Amount         `json:"total_amount"`
	Reference       string                      `json:"reference"`
	SubCompany      string                      `json:"sub_company,omitempty"`
	TransactionDate *time.Time                  `json:"transaction_date,omitempty"`
	DetailBills     interface{}                 `json:"detail_bills,omitempty"`
	AdditionalData  string                      `json:"additional_data,omitempty"`
}

// PaymentProcessingResult represents the result of payment processing
type PaymentProcessingResult struct {
	Success       bool                       `json:"success"`
	Payment       *entities.Payment          `json:"payment,omitempty"`
	Status        entities.PaymentStatus     `json:"status"`
	Reason        entities.PaymentFlagReason `json:"reason"`
	TransactionID string                     `json:"transaction_id"`
	Message       string                     `json:"message"`
	ResponseTime  time.Duration              `json:"response_time"`
}

// PaymentStatusResult represents the result of payment status inquiry
type PaymentStatusResult struct {
	Payment     *entities.Payment      `json:"payment"`
	Status      entities.PaymentStatus `json:"status"`
	LastUpdated time.Time              `json:"last_updated"`
	Message     string                 `json:"message"`
}

// TransactionCreationRequest represents a transaction creation request
type TransactionCreationRequest struct {
	RequestID      valueobjects.RequestID      `json:"request_id"`
	Type           entities.TransactionType    `json:"type"`
	CompanyCode    valueobjects.CompanyCode    `json:"company_code"`
	CustomerNumber valueobjects.CustomerNumber `json:"customer_number"`
	ChannelType    string                      `json:"channel_type"`
	Metadata       map[string]interface{}      `json:"metadata,omitempty"`
}

// TransactionFilter represents filters for transaction queries
type TransactionFilter struct {
	CompanyCode    *valueobjects.CompanyCode    `json:"company_code,omitempty"`
	CustomerNumber *valueobjects.CustomerNumber `json:"customer_number,omitempty"`
	Type           *entities.TransactionType    `json:"type,omitempty"`
	State          *entities.TransactionState   `json:"state,omitempty"`
	ChannelType    *string                      `json:"channel_type,omitempty"`
	StartDate      *time.Time                   `json:"start_date,omitempty"`
	EndDate        *time.Time                   `json:"end_date,omitempty"`
	Limit          int                          `json:"limit,omitempty"`
	Offset         int                          `json:"offset,omitempty"`
}

// ServiceHealthResult represents the result of service health check
type ServiceHealthResult struct {
	Healthy         bool                        `json:"healthy"`
	Status          string                      `json:"status"`
	Version         string                      `json:"version"`
	Uptime          time.Duration               `json:"uptime"`
	Dependencies    map[string]DependencyHealth `json:"dependencies"`
	LastHealthCheck time.Time                   `json:"last_health_check"`
	Message         string                      `json:"message,omitempty"`
}

// DependencyHealth represents the health status of a dependency
type DependencyHealth struct {
	Name      string        `json:"name"`
	Healthy   bool          `json:"healthy"`
	Status    string        `json:"status"`
	Latency   time.Duration `json:"latency"`
	Message   string        `json:"message,omitempty"`
	LastCheck time.Time     `json:"last_check"`
}

// ServiceMetrics represents service performance metrics
type ServiceMetrics struct {
	RequestCount        int64                       `json:"request_count"`
	SuccessCount        int64                       `json:"success_count"`
	ErrorCount          int64                       `json:"error_count"`
	AverageResponseTime time.Duration               `json:"average_response_time"`
	P95ResponseTime     time.Duration               `json:"p95_response_time"`
	P99ResponseTime     time.Duration               `json:"p99_response_time"`
	ThroughputPerSecond float64                     `json:"throughput_per_second"`
	ErrorRate           float64                     `json:"error_rate"`
	SuccessRate         float64                     `json:"success_rate"`
	MetricsByOperation  map[string]OperationMetrics `json:"metrics_by_operation"`
	CollectedAt         time.Time                   `json:"collected_at"`
}

// OperationMetrics represents metrics for a specific operation
type OperationMetrics struct {
	Operation           string        `json:"operation"`
	RequestCount        int64         `json:"request_count"`
	SuccessCount        int64         `json:"success_count"`
	ErrorCount          int64         `json:"error_count"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	ErrorRate           float64       `json:"error_rate"`
	LastRequest         time.Time     `json:"last_request"`
}

// OttoPayServiceError represents errors from OttoPay service operations
type OttoPayServiceError struct {
	Operation string
	Code      string
	Message   string
	Err       error
}

func (e *OttoPayServiceError) Error() string {
	if e.Err != nil {
		return "ottopay service " + e.Operation + " [" + e.Code + "]: " + e.Message + " - " + e.Err.Error()
	}
	return "ottopay service " + e.Operation + " [" + e.Code + "]: " + e.Message
}

func (e *OttoPayServiceError) Unwrap() error {
	return e.Err
}

// NewOttoPayServiceError creates a new OttoPay service error
func NewOttoPayServiceError(operation, code, message string, err error) *OttoPayServiceError {
	return &OttoPayServiceError{
		Operation: operation,
		Code:      code,
		Message:   message,
		Err:       err,
	}
}

// Service error codes
const (
	ErrorCodeInvalidRequest       = "INVALID_REQUEST"
	ErrorCodeAuthenticationFailed = "AUTHENTICATION_FAILED"
	ErrorCodeTokenExpired         = "TOKEN_EXPIRED"
	ErrorCodeInvalidToken         = "INVALID_TOKEN"
	ErrorCodeCustomerNotFound     = "CUSTOMER_NOT_FOUND"
	ErrorCodeInvalidBill          = "INVALID_BILL"
	ErrorCodePaymentFailed        = "PAYMENT_FAILED"
	ErrorCodePaymentTimeout       = "PAYMENT_TIMEOUT"
	ErrorCodeDuplicateRequest     = "DUPLICATE_REQUEST"
	ErrorCodeInvalidAmount        = "INVALID_AMOUNT"
	ErrorCodePaidBill             = "PAID_BILL"
	ErrorCodeTransactionExpired   = "TRANSACTION_EXPIRED"
	ErrorCodeServiceUnavailable   = "SERVICE_UNAVAILABLE"
	ErrorCodeInternalError        = "INTERNAL_ERROR"
)
