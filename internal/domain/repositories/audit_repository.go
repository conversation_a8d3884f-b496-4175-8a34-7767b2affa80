package repositories

import (
	"context"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// AuditEventType represents the type of audit event
type AuditEventType string

const (
	AuditEventTypeTokenRequest    AuditEventType = "TOKEN_REQUEST"
	AuditEventTypeTokenGenerated  AuditEventType = "TOKEN_GENERATED"
	AuditEventTypeTokenExpired    AuditEventType = "TOKEN_EXPIRED"
	AuditEventTypeTokenRevoked    AuditEventType = "TOKEN_REVOKED"
	AuditEventTypeInquiryRequest  AuditEventType = "INQUIRY_REQUEST"
	AuditEventTypeInquiryResponse AuditEventType = "INQUIRY_RESPONSE"
	AuditEventTypePaymentRequest  AuditEventType = "PAYMENT_REQUEST"
	AuditEventTypePaymentResponse AuditEventType = "PAYMENT_RESPONSE"
	AuditEventTypePaymentSuccess  AuditEventType = "PAYMENT_SUCCESS"
	AuditEventTypePaymentFailed   AuditEventType = "PAYMENT_FAILED"
	AuditEventTypePaymentTimeout  AuditEventType = "PAYMENT_TIMEOUT"
	AuditEventTypeAPIError        AuditEventType = "API_ERROR"
	AuditEventTypeSystemError     AuditEventType = "SYSTEM_ERROR"
)

// AuditLevel represents the severity level of an audit event
type AuditLevel string

const (
	AuditLevelInfo    AuditLevel = "INFO"
	AuditLevelWarning AuditLevel = "WARNING"
	AuditLevelError   AuditLevel = "ERROR"
	AuditLevelCritical AuditLevel = "CRITICAL"
)

// AuditEvent represents an audit event
type AuditEvent struct {
	ID          string                      `json:"id"`
	Type        AuditEventType              `json:"type"`
	Level       AuditLevel                  `json:"level"`
	Message     string                      `json:"message"`
	
	// Context information
	CompanyCode    *valueobjects.CompanyCode    `json:"company_code,omitempty"`
	CustomerNumber *valueobjects.CustomerNumber `json:"customer_number,omitempty"`
	RequestID      *valueobjects.RequestID      `json:"request_id,omitempty"`
	Username       *string                      `json:"username,omitempty"`
	
	// Request/Response data
	RequestData  map[string]interface{} `json:"request_data,omitempty"`
	ResponseData map[string]interface{} `json:"response_data,omitempty"`
	
	// Error information
	ErrorCode    *string `json:"error_code,omitempty"`
	ErrorMessage *string `json:"error_message,omitempty"`
	StackTrace   *string `json:"stack_trace,omitempty"`
	
	// Metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	
	// Timing information
	Duration  *time.Duration `json:"duration,omitempty"`
	Timestamp time.Time      `json:"timestamp"`
	
	// Source information
	SourceIP    *string `json:"source_ip,omitempty"`
	UserAgent   *string `json:"user_agent,omitempty"`
	ChannelType *string `json:"channel_type,omitempty"`
}

// AuditRepository defines the interface for audit logging operations
type AuditRepository interface {
	// LogEvent logs an audit event
	LogEvent(ctx context.Context, event *AuditEvent) error
	
	// LogEvents logs multiple audit events in batch
	LogEvents(ctx context.Context, events []*AuditEvent) error
	
	// GetEventByID retrieves an audit event by its ID
	GetEventByID(ctx context.Context, id string) (*AuditEvent, error)
	
	// GetEvents retrieves audit events with filtering and pagination
	GetEvents(ctx context.Context, filter *AuditFilter, limit, offset int) ([]*AuditEvent, error)
	
	// CountEvents counts audit events based on filter
	CountEvents(ctx context.Context, filter *AuditFilter) (int, error)
	
	// GetEventsByRequestID retrieves all events for a specific request ID
	GetEventsByRequestID(ctx context.Context, requestID valueobjects.RequestID) ([]*AuditEvent, error)
	
	// GetEventsByCustomer retrieves events for a specific customer
	GetEventsByCustomer(ctx context.Context, companyCode valueobjects.CompanyCode, customerNumber valueobjects.CustomerNumber) ([]*AuditEvent, error)
	
	// GetEventsByType retrieves events by type
	GetEventsByType(ctx context.Context, eventType AuditEventType) ([]*AuditEvent, error)
	
	// GetEventsByLevel retrieves events by level
	GetEventsByLevel(ctx context.Context, level AuditLevel) ([]*AuditEvent, error)
	
	// GetEventsByDateRange retrieves events within a date range
	GetEventsByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*AuditEvent, error)
	
	// DeleteOldEvents deletes events older than the specified duration
	DeleteOldEvents(ctx context.Context, olderThan time.Duration) (int, error)
	
	// GetAuditStats returns audit statistics
	GetAuditStats(ctx context.Context) (*AuditStats, error)
	
	// GetErrorSummary returns error summary for a time period
	GetErrorSummary(ctx context.Context, startDate, endDate time.Time) (*ErrorSummary, error)
}

// AuditFilter represents filters for audit event queries
type AuditFilter struct {
	Types          []AuditEventType             `json:"types,omitempty"`
	Levels         []AuditLevel                 `json:"levels,omitempty"`
	CompanyCode    *valueobjects.CompanyCode    `json:"company_code,omitempty"`
	CustomerNumber *valueobjects.CustomerNumber `json:"customer_number,omitempty"`
	RequestID      *valueobjects.RequestID      `json:"request_id,omitempty"`
	Username       *string                      `json:"username,omitempty"`
	ChannelType    *string                      `json:"channel_type,omitempty"`
	SourceIP       *string                      `json:"source_ip,omitempty"`
	
	// Date filters
	TimestampAfter  *time.Time `json:"timestamp_after,omitempty"`
	TimestampBefore *time.Time `json:"timestamp_before,omitempty"`
	
	// Error filters
	HasError       *bool   `json:"has_error,omitempty"`
	ErrorCode      *string `json:"error_code,omitempty"`
	
	// Duration filters
	MinDuration *time.Duration `json:"min_duration,omitempty"`
	MaxDuration *time.Duration `json:"max_duration,omitempty"`
	
	// Text search
	MessageContains *string `json:"message_contains,omitempty"`
}

// AuditStats represents audit statistics
type AuditStats struct {
	TotalEvents       int                        `json:"total_events"`
	EventsByType      map[AuditEventType]int     `json:"events_by_type"`
	EventsByLevel     map[AuditLevel]int         `json:"events_by_level"`
	EventsByChannel   map[string]int             `json:"events_by_channel"`
	ErrorCount        int                        `json:"error_count"`
	WarningCount      int                        `json:"warning_count"`
	AverageDuration   time.Duration              `json:"average_duration"`
	UniqueCustomers   int                        `json:"unique_customers"`
	UniqueRequests    int                        `json:"unique_requests"`
	EventsPerHour     map[int]int                `json:"events_per_hour"`
	TopErrorCodes     map[string]int             `json:"top_error_codes"`
}

// ErrorSummary represents error summary for a time period
type ErrorSummary struct {
	TotalErrors       int                    `json:"total_errors"`
	ErrorsByType      map[AuditEventType]int `json:"errors_by_type"`
	ErrorsByCode      map[string]int         `json:"errors_by_code"`
	ErrorsByChannel   map[string]int         `json:"errors_by_channel"`
	ErrorsByCompany   map[string]int         `json:"errors_by_company"`
	ErrorRate         float64                `json:"error_rate"`
	MostCommonErrors  []string               `json:"most_common_errors"`
	ErrorTrend        []ErrorTrendPoint      `json:"error_trend"`
}

// ErrorTrendPoint represents a point in error trend analysis
type ErrorTrendPoint struct {
	Timestamp   time.Time `json:"timestamp"`
	ErrorCount  int       `json:"error_count"`
	TotalEvents int       `json:"total_events"`
	ErrorRate   float64   `json:"error_rate"`
}

// AuditRepositoryWithAnalytics extends AuditRepository with analytics capabilities
type AuditRepositoryWithAnalytics interface {
	AuditRepository
	
	// GetEventTrends returns event trends over time
	GetEventTrends(ctx context.Context, startDate, endDate time.Time, interval string) ([]*EventTrend, error)
	
	// GetPerformanceMetrics returns performance metrics for a time period
	GetPerformanceMetrics(ctx context.Context, startDate, endDate time.Time) (*PerformanceMetrics, error)
	
	// GetUsagePatterns returns usage patterns analysis
	GetUsagePatterns(ctx context.Context, startDate, endDate time.Time) (*UsagePatterns, error)
}

// EventTrend represents event trends over time
type EventTrend struct {
	Period      time.Time              `json:"period"`
	TotalEvents int                    `json:"total_events"`
	EventsByType map[AuditEventType]int `json:"events_by_type"`
	ErrorCount  int                    `json:"error_count"`
	ErrorRate   float64                `json:"error_rate"`
}

// PerformanceMetrics represents performance metrics
type PerformanceMetrics struct {
	AverageResponseTime time.Duration              `json:"average_response_time"`
	MedianResponseTime  time.Duration              `json:"median_response_time"`
	P95ResponseTime     time.Duration              `json:"p95_response_time"`
	P99ResponseTime     time.Duration              `json:"p99_response_time"`
	ThroughputPerSecond float64                    `json:"throughput_per_second"`
	ResponseTimeByType  map[AuditEventType]time.Duration `json:"response_time_by_type"`
	SlowestOperations   []SlowOperation            `json:"slowest_operations"`
}

// SlowOperation represents a slow operation
type SlowOperation struct {
	Type      AuditEventType `json:"type"`
	RequestID string         `json:"request_id"`
	Duration  time.Duration  `json:"duration"`
	Timestamp time.Time      `json:"timestamp"`
}

// UsagePatterns represents usage patterns analysis
type UsagePatterns struct {
	PeakHours         []int                  `json:"peak_hours"`
	BusiestDays       []time.Weekday         `json:"busiest_days"`
	TopChannels       map[string]int         `json:"top_channels"`
	TopCompanies      map[string]int         `json:"top_companies"`
	RequestDistribution map[string]float64   `json:"request_distribution"`
	GeographicDistribution map[string]int    `json:"geographic_distribution"`
}

// AuditRepositoryError represents errors from audit repository operations
type AuditRepositoryError struct {
	Operation string
	Err       error
}

func (e *AuditRepositoryError) Error() string {
	return "audit repository " + e.Operation + ": " + e.Err.Error()
}

func (e *AuditRepositoryError) Unwrap() error {
	return e.Err
}

// NewAuditRepositoryError creates a new audit repository error
func NewAuditRepositoryError(operation string, err error) *AuditRepositoryError {
	return &AuditRepositoryError{
		Operation: operation,
		Err:       err,
	}
}
