package repositories

import (
	"context"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
)

// TokenRepository defines the interface for token storage operations
type TokenRepository interface {
	// Store stores a token in the repository
	Store(ctx context.Context, token *entities.Token) error

	// GetByID retrieves a token by its ID
	GetByID(ctx context.Context, id string) (*entities.Token, error)

	// GetByUsername retrieves the latest valid token for a username
	GetByUsername(ctx context.Context, username string) (*entities.Token, error)

	// GetByValue retrieves a token by its value
	GetByValue(ctx context.Context, value string) (*entities.Token, error)

	// Update updates an existing token
	Update(ctx context.Context, token *entities.Token) error

	// Delete removes a token from the repository
	Delete(ctx context.Context, id string) error

	// DeleteByUsername removes all tokens for a username
	DeleteByUsername(ctx context.Context, username string) error

	// Revoke revokes a token (marks it as revoked)
	Revoke(ctx context.Context, id string) error

	// RevokeByUsername revokes all tokens for a username
	RevokeByUsername(ctx context.Context, username string) error

	// GetExpiredTokens retrieves all expired tokens
	GetExpiredTokens(ctx context.Context) ([]*entities.Token, error)

	// GetTokensExpiringBefore retrieves tokens expiring before the specified time
	GetTokensExpiringBefore(ctx context.Context, before time.Time) ([]*entities.Token, error)

	// CleanupExpiredTokens removes expired tokens from the repository
	CleanupExpiredTokens(ctx context.Context) (int, error)

	// IsTokenValid checks if a token is valid (exists, not expired, not revoked)
	IsTokenValid(ctx context.Context, value string) (bool, error)

	// GetTokenStats returns statistics about tokens in the repository
	GetTokenStats(ctx context.Context) (*TokenStats, error)

	// ListTokensByUsername lists all tokens for a username with pagination
	ListTokensByUsername(ctx context.Context, username string, limit, offset int) ([]*entities.Token, error)

	// CountTokensByUsername counts tokens for a username
	CountTokensByUsername(ctx context.Context, username string) (int, error)

	// GetActiveTokensCount returns the count of active (valid) tokens
	GetActiveTokensCount(ctx context.Context) (int, error)
}

// TokenStats represents statistics about tokens in the repository
type TokenStats struct {
	TotalTokens   int `json:"total_tokens"`
	ActiveTokens  int `json:"active_tokens"`
	ExpiredTokens int `json:"expired_tokens"`
	RevokedTokens int `json:"revoked_tokens"`
	UniqueUsers   int `json:"unique_users"`
}

// TokenFilter represents filters for token queries
type TokenFilter struct {
	Username      string             `json:"username,omitempty"`
	TokenType     entities.TokenType `json:"token_type,omitempty"`
	IsRevoked     *bool              `json:"is_revoked,omitempty"`
	IsExpired     *bool              `json:"is_expired,omitempty"`
	CreatedAfter  *time.Time         `json:"created_after,omitempty"`
	CreatedBefore *time.Time         `json:"created_before,omitempty"`
	ExpiresAfter  *time.Time         `json:"expires_after,omitempty"`
	ExpiresBefore *time.Time         `json:"expires_before,omitempty"`
}

// TokenRepositoryWithFiltering extends TokenRepository with advanced filtering capabilities
type TokenRepositoryWithFiltering interface {
	TokenRepository

	// FindTokens finds tokens based on the provided filter
	FindTokens(ctx context.Context, filter *TokenFilter, limit, offset int) ([]*entities.Token, error)

	// CountTokens counts tokens based on the provided filter
	CountTokens(ctx context.Context, filter *TokenFilter) (int, error)
}

// TokenCacheRepository defines the interface for token caching operations
type TokenCacheRepository interface {
	// Set stores a token in the cache with TTL
	Set(ctx context.Context, key string, token *entities.Token, ttl time.Duration) error

	// Get retrieves a token from the cache
	Get(ctx context.Context, key string) (*entities.Token, error)

	// Delete removes a token from the cache
	Delete(ctx context.Context, key string) error

	// Exists checks if a token exists in the cache
	Exists(ctx context.Context, key string) (bool, error)

	// SetTTL updates the TTL for a cached token
	SetTTL(ctx context.Context, key string, ttl time.Duration) error

	// GetTTL gets the remaining TTL for a cached token
	GetTTL(ctx context.Context, key string) (time.Duration, error)

	// Clear removes all tokens from the cache
	Clear(ctx context.Context) error

	// GetCacheStats returns cache statistics
	GetCacheStats(ctx context.Context) (*CacheStats, error)
}

// CacheStats represents cache statistics
type CacheStats struct {
	TotalKeys   int           `json:"total_keys"`
	HitCount    int64         `json:"hit_count"`
	MissCount   int64         `json:"miss_count"`
	HitRate     float64       `json:"hit_rate"`
	MemoryUsage int64         `json:"memory_usage"`
	AverageTTL  time.Duration `json:"average_ttl"`
}

// TokenRepositoryError represents errors from token repository operations
type TokenRepositoryError struct {
	Operation string
	Err       error
}

func (e *TokenRepositoryError) Error() string {
	return "token repository " + e.Operation + ": " + e.Err.Error()
}

func (e *TokenRepositoryError) Unwrap() error {
	return e.Err
}

// NewTokenRepositoryError creates a new token repository error
func NewTokenRepositoryError(operation string, err error) *TokenRepositoryError {
	return &TokenRepositoryError{
		Operation: operation,
		Err:       err,
	}
}
