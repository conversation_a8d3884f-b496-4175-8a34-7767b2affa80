package repositories

import (
	"context"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/entities"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
)

// PaymentRepository defines the interface for payment storage operations
type PaymentRepository interface {
	// Store stores a payment in the repository
	Store(ctx context.Context, payment *entities.Payment) error
	
	// GetByID retrieves a payment by its ID
	GetByID(ctx context.Context, id string) (*entities.Payment, error)
	
	// GetByRequestID retrieves a payment by its request ID
	GetByRequestID(ctx context.Context, requestID valueobjects.RequestID) (*entities.Payment, error)
	
	// GetByCustomer retrieves payments for a specific customer
	GetByCustomer(ctx context.Context, companyCode valueobjects.CompanyCode, customerNumber valueobjects.CustomerNumber) ([]*entities.Payment, error)
	
	// GetByReference retrieves a payment by its reference
	GetByReference(ctx context.Context, reference string) (*entities.Payment, error)
	
	// Update updates an existing payment
	Update(ctx context.Context, payment *entities.Payment) error
	
	// Delete removes a payment from the repository
	Delete(ctx context.Context, id string) error
	
	// GetPaymentsByStatus retrieves payments by status
	GetPaymentsByStatus(ctx context.Context, status entities.PaymentStatus) ([]*entities.Payment, error)
	
	// GetPaymentsByDateRange retrieves payments within a date range
	GetPaymentsByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*entities.Payment, error)
	
	// GetPendingPayments retrieves all pending payments
	GetPendingPayments(ctx context.Context) ([]*entities.Payment, error)
	
	// GetExpiredPayments retrieves payments that have been pending for too long
	GetExpiredPayments(ctx context.Context, maxAge time.Duration) ([]*entities.Payment, error)
	
	// GetPaymentStats returns payment statistics
	GetPaymentStats(ctx context.Context) (*PaymentStats, error)
	
	// GetPaymentsByCompany retrieves payments for a specific company
	GetPaymentsByCompany(ctx context.Context, companyCode valueobjects.CompanyCode) ([]*entities.Payment, error)
	
	// ListPayments lists payments with pagination and filtering
	ListPayments(ctx context.Context, filter *PaymentFilter, limit, offset int) ([]*entities.Payment, error)
	
	// CountPayments counts payments based on filter
	CountPayments(ctx context.Context, filter *PaymentFilter) (int, error)
	
	// GetDailyPaymentSummary returns daily payment summary for a date range
	GetDailyPaymentSummary(ctx context.Context, startDate, endDate time.Time) ([]*DailyPaymentSummary, error)
	
	// GetPaymentsByChannel retrieves payments by channel type
	GetPaymentsByChannel(ctx context.Context, channelType string) ([]*entities.Payment, error)
}

// PaymentStats represents payment statistics
type PaymentStats struct {
	TotalPayments     int                                    `json:"total_payments"`
	SuccessfulPayments int                                   `json:"successful_payments"`
	FailedPayments    int                                    `json:"failed_payments"`
	PendingPayments   int                                    `json:"pending_payments"`
	TimeoutPayments   int                                    `json:"timeout_payments"`
	TotalAmount       map[valueobjects.CurrencyCode]float64  `json:"total_amount"`
	SuccessAmount     map[valueobjects.CurrencyCode]float64  `json:"success_amount"`
	AverageAmount     map[valueobjects.CurrencyCode]float64  `json:"average_amount"`
	PaymentsByStatus  map[entities.PaymentStatus]int         `json:"payments_by_status"`
	PaymentsByChannel map[string]int                         `json:"payments_by_channel"`
	PaymentsByCompany map[valueobjects.CompanyCode]int       `json:"payments_by_company"`
}

// PaymentFilter represents filters for payment queries
type PaymentFilter struct {
	CompanyCode    *valueobjects.CompanyCode    `json:"company_code,omitempty"`
	CustomerNumber *valueobjects.CustomerNumber `json:"customer_number,omitempty"`
	Status         *entities.PaymentStatus      `json:"status,omitempty"`
	CurrencyCode   *valueobjects.CurrencyCode   `json:"currency_code,omitempty"`
	ChannelType    *string                      `json:"channel_type,omitempty"`
	Reference      *string                      `json:"reference,omitempty"`
	CustomerName   *string                      `json:"customer_name,omitempty"`
	
	// Amount filters
	MinAmount *valueobjects.Amount `json:"min_amount,omitempty"`
	MaxAmount *valueobjects.Amount `json:"max_amount,omitempty"`
	
	// Date filters
	CreatedAfter     *time.Time `json:"created_after,omitempty"`
	CreatedBefore    *time.Time `json:"created_before,omitempty"`
	ProcessedAfter   *time.Time `json:"processed_after,omitempty"`
	ProcessedBefore  *time.Time `json:"processed_before,omitempty"`
	TransactionAfter *time.Time `json:"transaction_after,omitempty"`
	TransactionBefore *time.Time `json:"transaction_before,omitempty"`
}

// DailyPaymentSummary represents daily payment summary
type DailyPaymentSummary struct {
	Date              time.Time                              `json:"date"`
	TotalPayments     int                                    `json:"total_payments"`
	SuccessfulPayments int                                   `json:"successful_payments"`
	FailedPayments    int                                    `json:"failed_payments"`
	TotalAmount       map[valueobjects.CurrencyCode]float64  `json:"total_amount"`
	SuccessAmount     map[valueobjects.CurrencyCode]float64  `json:"success_amount"`
	AverageAmount     map[valueobjects.CurrencyCode]float64  `json:"average_amount"`
	UniqueCustomers   int                                    `json:"unique_customers"`
	PaymentsByChannel map[string]int                         `json:"payments_by_channel"`
}

// PaymentRepositoryWithAnalytics extends PaymentRepository with analytics capabilities
type PaymentRepositoryWithAnalytics interface {
	PaymentRepository
	
	// GetPaymentTrends returns payment trends over time
	GetPaymentTrends(ctx context.Context, startDate, endDate time.Time, interval string) ([]*PaymentTrend, error)
	
	// GetTopCustomers returns top customers by payment volume
	GetTopCustomers(ctx context.Context, limit int, startDate, endDate time.Time) ([]*CustomerPaymentSummary, error)
	
	// GetPaymentDistribution returns payment distribution by various dimensions
	GetPaymentDistribution(ctx context.Context, dimension string, startDate, endDate time.Time) (map[string]int, error)
	
	// GetFailureAnalysis returns analysis of payment failures
	GetFailureAnalysis(ctx context.Context, startDate, endDate time.Time) (*FailureAnalysis, error)
}

// PaymentTrend represents payment trends over time
type PaymentTrend struct {
	Period            time.Time                              `json:"period"`
	TotalPayments     int                                    `json:"total_payments"`
	SuccessfulPayments int                                   `json:"successful_payments"`
	FailedPayments    int                                    `json:"failed_payments"`
	TotalAmount       map[valueobjects.CurrencyCode]float64  `json:"total_amount"`
	SuccessRate       float64                                `json:"success_rate"`
}

// CustomerPaymentSummary represents customer payment summary
type CustomerPaymentSummary struct {
	CompanyCode       valueobjects.CompanyCode               `json:"company_code"`
	CustomerNumber    valueobjects.CustomerNumber            `json:"customer_number"`
	CustomerName      string                                 `json:"customer_name"`
	TotalPayments     int                                    `json:"total_payments"`
	SuccessfulPayments int                                   `json:"successful_payments"`
	TotalAmount       map[valueobjects.CurrencyCode]float64  `json:"total_amount"`
	LastPaymentDate   time.Time                              `json:"last_payment_date"`
}

// FailureAnalysis represents analysis of payment failures
type FailureAnalysis struct {
	TotalFailures     int                    `json:"total_failures"`
	FailuresByReason  map[string]int         `json:"failures_by_reason"`
	FailuresByChannel map[string]int         `json:"failures_by_channel"`
	FailuresByCompany map[string]int         `json:"failures_by_company"`
	FailureRate       float64                `json:"failure_rate"`
	CommonReasons     []string               `json:"common_reasons"`
}

// PaymentRepositoryError represents errors from payment repository operations
type PaymentRepositoryError struct {
	Operation string
	Err       error
}

func (e *PaymentRepositoryError) Error() string {
	return "payment repository " + e.Operation + ": " + e.Err.Error()
}

func (e *PaymentRepositoryError) Unwrap() error {
	return e.Err
}

// NewPaymentRepositoryError creates a new payment repository error
func NewPaymentRepositoryError(operation string, err error) *PaymentRepositoryError {
	return &PaymentRepositoryError{
		Operation: operation,
		Err:       err,
	}
}
