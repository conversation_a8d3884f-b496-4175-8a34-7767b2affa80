package valueobjects

import (
	"testing"
)

func TestNewCustomerNumber(t *testing.T) {
	tests := []struct {
		name        string
		number      string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid 11-digit customer number",
			number:      "12********1",
			expectError: false,
		},
		{
			name:        "Valid 10-digit customer number",
			number:      "12********",
			expectError: false,
		},
		{
			name:        "Valid 5-digit customer number",
			number:      "12345",
			expectError: false,
		},
		{
			name:        "Valid 1-digit customer number",
			number:      "1",
			expectError: false,
		},
		{
			name:        "Empty customer number",
			number:      "",
			expectError: true,
			errorMsg:    "customer number cannot be empty",
		},
		{
			name:        "Customer number too long",
			number:      "12********12",
			expectError: true,
			errorMsg:    "customer number cannot exceed 11 characters",
		},
		{
			name:        "Non-numeric customer number",
			number:      "12345A67890",
			expectError: true,
			errorMsg:    "customer number must contain only numeric characters",
		},
		{
			name:        "Customer number with spaces",
			number:      "12345 67890",
			expectError: true,
			errorMsg:    "customer number must contain only numeric characters",
		},
		{
			name:        "Customer number with special characters",
			number:      "12345-67890",
			expectError: true,
			errorMsg:    "customer number must contain only numeric characters",
		},
		{
			name:        "Leading zeros allowed",
			number:      "***********",
			expectError: false,
		},
		{
			name:        "All zeros",
			number:      "00000000000",
			expectError: false,
		},
		{
			name:        "Customer number with prefix",
			number:      "***********",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			customerNumber, err := NewCustomerNumber(tt.number)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for customer number '%s', but got none", tt.number)
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if customerNumber != "" {
					t.Errorf("Expected empty customer number for invalid input, got %v", customerNumber)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid customer number '%s': %v", tt.number, err)
					return
				}
				if customerNumber == "" {
					t.Errorf("Expected valid customer number, got empty")
					return
				}
				if customerNumber.String() != tt.number {
					t.Errorf("Expected customer number string '%s', got '%s'", tt.number, customerNumber.String())
				}
			}
		})
	}
}

func TestCustomerNumberString(t *testing.T) {
	tests := []struct {
		name     string
		number   string
		expected string
	}{
		{
			name:     "11-digit number",
			number:   "12********1",
			expected: "12********1",
		},
		{
			name:     "1-digit number",
			number:   "1",
			expected: "1",
		},
		{
			name:     "Number with leading zeros",
			number:   "***********",
			expected: "***********",
		},
		{
			name:     "OttoPay example number",
			number:   "***********",
			expected: "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			customerNumber, err := NewCustomerNumber(tt.number)
			if err != nil {
				t.Fatalf("Unexpected error creating customer number: %v", err)
			}

			result := customerNumber.String()
			if result != tt.expected {
				t.Errorf("Expected String() to return '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestCustomerNumberEquals(t *testing.T) {
	tests := []struct {
		name     string
		number1  string
		number2  string
		expected bool
	}{
		{
			name:     "Same numbers",
			number1:  "12********1",
			number2:  "12********1",
			expected: true,
		},
		{
			name:     "Different numbers",
			number1:  "12********1",
			number2:  "10987654321",
			expected: false,
		},
		{
			name:     "Different length numbers",
			number1:  "123",
			number2:  "12345",
			expected: false,
		},
		{
			name:     "Leading zeros matter",
			number1:  "123",
			number2:  "00123",
			expected: false,
		},
		{
			name:     "Same with leading zeros",
			number1:  "***********",
			number2:  "***********",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			customerNumber1, err := NewCustomerNumber(tt.number1)
			if err != nil {
				t.Fatalf("Unexpected error creating first customer number: %v", err)
			}

			customerNumber2, err := NewCustomerNumber(tt.number2)
			if err != nil {
				t.Fatalf("Unexpected error creating second customer number: %v", err)
			}

			result := customerNumber1.Equals(customerNumber2)
			if result != tt.expected {
				t.Errorf("Expected Equals() to return %v for '%s' and '%s', got %v",
					tt.expected, tt.number1, tt.number2, result)
			}

			// Test symmetry
			reverseResult := customerNumber2.Equals(customerNumber1)
			if reverseResult != tt.expected {
				t.Errorf("Equals() is not symmetric: '%s'.Equals('%s') = %v, but '%s'.Equals('%s') = %v",
					tt.number1, tt.number2, result, tt.number2, tt.number1, reverseResult)
			}
		})
	}
}

func TestCustomerNumberValidation(t *testing.T) {
	tests := []struct {
		name     string
		number   string
		expected bool
	}{
		{
			name:     "Valid 11-digit number",
			number:   "12********1",
			expected: true,
		},
		{
			name:     "Valid 1-digit number",
			number:   "1",
			expected: true,
		},
		{
			name:     "Valid number with leading zeros",
			number:   "***********",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			customerNumber, err := NewCustomerNumber(tt.number)
			if err != nil {
				t.Fatalf("Unexpected error creating customer number: %v", err)
			}

			result := customerNumber.Validate() == nil
			if result != tt.expected {
				t.Errorf("Expected validation to return %v for '%s', got %v",
					tt.expected, tt.number, result)
			}
		})
	}
}

func TestCustomerNumberLength(t *testing.T) {
	tests := []struct {
		name     string
		number   string
		expected int
	}{
		{
			name:     "11-digit number",
			number:   "12********1",
			expected: 11,
		},
		{
			name:     "1-digit number",
			number:   "1",
			expected: 1,
		},
		{
			name:     "5-digit number",
			number:   "12345",
			expected: 5,
		},
		{
			name:     "Number with leading zeros",
			number:   "***********",
			expected: 11,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			customerNumber, err := NewCustomerNumber(tt.number)
			if err != nil {
				t.Fatalf("Unexpected error creating customer number: %v", err)
			}

			result := customerNumber.Length()
			if result != tt.expected {
				t.Errorf("Expected Length() to return %d for '%s', got %d",
					tt.expected, tt.number, result)
			}
		})
	}
}

func TestCustomerNumberBusinessRules(t *testing.T) {
	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test that customer numbers follow OttoPay specification
		// Customer numbers must be numeric and at most 11 characters
		// Can include optional SubPrefix + CustomerNumber/BillID

		validNumbers := []string{
			"***********", // OttoPay example
			"1",           // Minimum length
			"12********1", // Maximum length
			"***********", // With leading zeros
		}

		for _, number := range validNumbers {
			customerNumber, err := NewCustomerNumber(number)
			if err != nil {
				t.Errorf("Expected valid customer number '%s' to be accepted, got error: %v", number, err)
			}
			if customerNumber == "" {
				t.Errorf("Expected non-empty customer number for '%s'", number)
			}
		}

		invalidNumbers := []string{
			"",             // Empty
			"12********12", // Too long
			"12A********",  // Non-numeric
			"12-********",  // Special characters
			"12 ********",  // Spaces
		}

		for _, number := range invalidNumbers {
			customerNumber, err := NewCustomerNumber(number)
			if err == nil {
				t.Errorf("Expected invalid customer number '%s' to be rejected", number)
			}
			if customerNumber != "" {
				t.Errorf("Expected empty customer number for invalid input '%s'", number)
			}
		}
	})

	t.Run("Leading zeros preservation", func(t *testing.T) {
		// Leading zeros should be preserved as they might be significant for routing
		number := "***********"
		customerNumber, err := NewCustomerNumber(number)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if customerNumber.String() != "***********" {
			t.Errorf("Expected leading zeros to be preserved, got '%s'", customerNumber.String())
		}
	})

	t.Run("SubPrefix handling", func(t *testing.T) {
		// Customer numbers can include SubPrefix (optional) + CustomerNumber/BillID
		// This is implementation-specific but should be supported
		numbersWithPossiblePrefix := []string{
			"***********", // Could be SubPrefix + CustomerNumber
			"12********",  // Could be just CustomerNumber
			"123",         // Short number
		}

		for _, number := range numbersWithPossiblePrefix {
			customerNumber, err := NewCustomerNumber(number)
			if err != nil {
				t.Errorf("Expected customer number with possible prefix '%s' to be accepted, got error: %v", number, err)
			}
			if customerNumber.String() != number {
				t.Errorf("Expected customer number to preserve original format '%s', got '%s'", number, customerNumber.String())
			}
		}
	})
}
