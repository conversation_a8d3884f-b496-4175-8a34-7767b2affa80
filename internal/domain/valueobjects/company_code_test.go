package valueobjects

import (
	"testing"
)

func TestNewCompanyCode(t *testing.T) {
	tests := []struct {
		name        string
		code        string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid 5-digit company code",
			code:        "12345",
			expectError: false,
		},
		{
			name:        "Valid 4-digit company code",
			code:        "1234",
			expectError: false,
		},
		{
			name:        "Valid 3-digit company code",
			code:        "123",
			expectError: false,
		},
		{
			name:        "Valid 2-digit company code",
			code:        "12",
			expectError: false,
		},
		{
			name:        "Valid 1-digit company code",
			code:        "1",
			expectError: false,
		},
		{
			name:        "Empty company code",
			code:        "",
			expectError: true,
			errorMsg:    "company code cannot be empty",
		},
		{
			name:        "Company code too long",
			code:        "123456",
			expectError: true,
			errorMsg:    "company code cannot exceed 5 characters",
		},
		{
			name:        "Non-numeric company code",
			code:        "12A45",
			expectError: true,
			errorMsg:    "company code must contain only numeric characters",
		},
		{
			name:        "Company code with spaces",
			code:        "12 45",
			expectError: true,
			errorMsg:    "company code must contain only numeric characters",
		},
		{
			name:        "Company code with special characters",
			code:        "12-45",
			expectError: true,
			errorMsg:    "company code must contain only numeric characters",
		},
		{
			name:        "Leading zeros allowed",
			code:        "00123",
			expectError: false,
		},
		{
			name:        "All zeros",
			code:        "00000",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode, err := NewCompanyCode(tt.code)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for company code '%s', but got none", tt.code)
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if companyCode != "" {
					t.Errorf("Expected empty company code for invalid input, got %v", companyCode)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid company code '%s': %v", tt.code, err)
					return
				}
				if companyCode == "" {
					t.Errorf("Expected valid company code, got empty")
					return
				}
				if companyCode.String() != tt.code {
					t.Errorf("Expected company code string '%s', got '%s'", tt.code, companyCode.String())
				}
			}
		})
	}
}

func TestCompanyCodeString(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected string
	}{
		{
			name:     "5-digit code",
			code:     "12345",
			expected: "12345",
		},
		{
			name:     "1-digit code",
			code:     "1",
			expected: "1",
		},
		{
			name:     "Code with leading zeros",
			code:     "00123",
			expected: "00123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode, err := NewCompanyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating company code: %v", err)
			}

			result := companyCode.String()
			if result != tt.expected {
				t.Errorf("Expected String() to return '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestCompanyCodeEquals(t *testing.T) {
	tests := []struct {
		name     string
		code1    string
		code2    string
		expected bool
	}{
		{
			name:     "Same codes",
			code1:    "12345",
			code2:    "12345",
			expected: true,
		},
		{
			name:     "Different codes",
			code1:    "12345",
			code2:    "54321",
			expected: false,
		},
		{
			name:     "Different length codes",
			code1:    "123",
			code2:    "12345",
			expected: false,
		},
		{
			name:     "Leading zeros matter",
			code1:    "123",
			code2:    "00123",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode1, err := NewCompanyCode(tt.code1)
			if err != nil {
				t.Fatalf("Unexpected error creating first company code: %v", err)
			}

			companyCode2, err := NewCompanyCode(tt.code2)
			if err != nil {
				t.Fatalf("Unexpected error creating second company code: %v", err)
			}

			result := companyCode1.Equals(companyCode2)
			if result != tt.expected {
				t.Errorf("Expected Equals() to return %v for '%s' and '%s', got %v",
					tt.expected, tt.code1, tt.code2, result)
			}

			// Test symmetry
			reverseResult := companyCode2.Equals(companyCode1)
			if reverseResult != tt.expected {
				t.Errorf("Equals() is not symmetric: '%s'.Equals('%s') = %v, but '%s'.Equals('%s') = %v",
					tt.code1, tt.code2, result, tt.code2, tt.code1, reverseResult)
			}
		})
	}
}

func TestCompanyCodeIsValidFormat(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected bool
	}{
		{
			name:     "Valid 5-digit code",
			code:     "12345",
			expected: true,
		},
		{
			name:     "Valid 1-digit code",
			code:     "1",
			expected: true,
		},
		{
			name:     "Valid code with leading zeros",
			code:     "00123",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			companyCode, err := NewCompanyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating company code: %v", err)
			}

			result := companyCode.IsValidFormat()
			if result != tt.expected {
				t.Errorf("Expected IsValidFormat() to return %v for '%s', got %v",
					tt.expected, tt.code, result)
			}
		})
	}
}

func TestCompanyCodeBusinessRules(t *testing.T) {
	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test that company codes follow OttoPay specification
		// Company codes must be numeric and at most 5 characters

		validCodes := []string{"12173", "1", "12", "123", "1234", "12345"}
		for _, code := range validCodes {
			companyCode, err := NewCompanyCode(code)
			if err != nil {
				t.Errorf("Expected valid company code '%s' to be accepted, got error: %v", code, err)
			}
			if companyCode == "" {
				t.Errorf("Expected non-empty company code for '%s'", code)
			}
		}

		invalidCodes := []string{"", "123456", "12A34", "12-34", "12 34"}
		for _, code := range invalidCodes {
			companyCode, err := NewCompanyCode(code)
			if err == nil {
				t.Errorf("Expected invalid company code '%s' to be rejected", code)
			}
			if companyCode != "" {
				t.Errorf("Expected empty company code for invalid input '%s'", code)
			}
		}
	})

	t.Run("Leading zeros preservation", func(t *testing.T) {
		// Leading zeros should be preserved as they might be significant
		code := "00123"
		companyCode, err := NewCompanyCode(code)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if companyCode.String() != "00123" {
			t.Errorf("Expected leading zeros to be preserved, got '%s'", companyCode.String())
		}
	})
}
