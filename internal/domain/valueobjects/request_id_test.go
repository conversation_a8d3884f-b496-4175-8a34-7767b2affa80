package valueobjects

import (
	"strings"
	"testing"
	"time"
)

func TestNewRequestID(t *testing.T) {
	tests := []struct {
		name        string
		id          string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid request ID",
			id:          "201507131507262221400000001975",
			expectError: false,
		},
		{
			name:        "Valid short request ID",
			id:          "REQ123456789",
			expectError: false,
		},
		{
			name:        "Valid UUID-like request ID",
			id:          "550e8400-e29b-41d4-a716-************",
			expectError: false,
		},
		{
			name:        "Empty request ID",
			id:          "",
			expectError: true,
			errorMsg:    "request ID cannot be empty",
		},
		{
			name:        "Request ID too long",
			id:          strings.Repeat("a", 256),
			expectError: true,
			errorMsg:    "request ID cannot exceed 255 characters",
		},
		{
			name:        "Valid alphanumeric request ID",
			id:          "REQ123ABC456DEF",
			expectError: false,
		},
		{
			name:        "Request ID with hyphens",
			id:          "REQ-123-456-789",
			expectError: false,
		},
		{
			name:        "Request ID with underscores",
			id:          "REQ_123_456_789",
			expectError: false,
		},
		{
			name:        "Request ID with dots",
			id:          "REQ.123.456.789",
			expectError: false,
		},
		{
			name:        "Maximum length request ID",
			id:          strings.Repeat("a", 255),
			expectError: false,
		},
		{
			name:        "Minimum length request ID",
			id:          "1",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestID, err := NewRequestID(tt.id)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for request ID '%s', but got none", tt.id)
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if requestID != "" {
					t.Errorf("Expected empty request ID for invalid input, got %v", requestID)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid request ID '%s': %v", tt.id, err)
					return
				}
				if requestID == "" {
					t.Errorf("Expected valid request ID, got empty")
					return
				}
				if requestID.String() != tt.id {
					t.Errorf("Expected request ID string '%s', got '%s'", tt.id, requestID.String())
				}
			}
		})
	}
}

func TestGenerateInquiryRequestID(t *testing.T) {
	t.Run("Generate unique inquiry request IDs", func(t *testing.T) {
		ids := make(map[string]bool)

		// Generate multiple IDs and ensure they're unique
		for i := 0; i < 100; i++ {
			requestID, err := GenerateInquiryRequestID()
			if err != nil {
				t.Fatalf("Unexpected error generating inquiry request ID: %v", err)
			}

			idStr := requestID.String()
			if ids[idStr] {
				t.Errorf("Generated duplicate inquiry request ID: %s", idStr)
			}
			ids[idStr] = true

			// Check prefix
			if !strings.HasPrefix(idStr, "INQ") {
				t.Errorf("Expected inquiry request ID to start with 'INQ', got: %s", idStr)
			}

			// Check length is reasonable
			if len(idStr) < 10 || len(idStr) > 50 {
				t.Errorf("Expected inquiry request ID length between 10-50 chars, got %d: %s", len(idStr), idStr)
			}
		}
	})
}

func TestGeneratePaymentRequestID(t *testing.T) {
	t.Run("Generate unique payment request IDs", func(t *testing.T) {
		ids := make(map[string]bool)

		// Generate multiple IDs and ensure they're unique
		for i := 0; i < 100; i++ {
			requestID, err := GeneratePaymentRequestID()
			if err != nil {
				t.Fatalf("Unexpected error generating payment request ID: %v", err)
			}

			idStr := requestID.String()
			if ids[idStr] {
				t.Errorf("Generated duplicate payment request ID: %s", idStr)
			}
			ids[idStr] = true

			// Check prefix
			if !strings.HasPrefix(idStr, "PAY") {
				t.Errorf("Expected payment request ID to start with 'PAY', got: %s", idStr)
			}

			// Check length is reasonable
			if len(idStr) < 10 || len(idStr) > 50 {
				t.Errorf("Expected payment request ID length between 10-50 chars, got %d: %s", len(idStr), idStr)
			}
		}
	})
}

func TestGenerateTokenRequestID(t *testing.T) {
	t.Run("Generate unique token request IDs", func(t *testing.T) {
		ids := make(map[string]bool)

		// Generate multiple IDs and ensure they're unique
		for i := 0; i < 100; i++ {
			requestID, err := GenerateTokenRequestID()
			if err != nil {
				t.Fatalf("Unexpected error generating token request ID: %v", err)
			}

			idStr := requestID.String()
			if ids[idStr] {
				t.Errorf("Generated duplicate token request ID: %s", idStr)
			}
			ids[idStr] = true

			// Check prefix
			if !strings.HasPrefix(idStr, "TOK") {
				t.Errorf("Expected token request ID to start with 'TOK', got: %s", idStr)
			}

			// Check length is reasonable
			if len(idStr) < 10 || len(idStr) > 50 {
				t.Errorf("Expected token request ID length between 10-50 chars, got %d: %s", len(idStr), idStr)
			}
		}
	})
}

func TestRequestIDString(t *testing.T) {
	tests := []struct {
		name     string
		id       string
		expected string
	}{
		{
			name:     "OttoPay example ID",
			id:       "201507131507262221400000001975",
			expected: "201507131507262221400000001975",
		},
		{
			name:     "Simple ID",
			id:       "REQ123",
			expected: "REQ123",
		},
		{
			name:     "UUID-like ID",
			id:       "550e8400-e29b-41d4-a716-************",
			expected: "550e8400-e29b-41d4-a716-************",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestID, err := NewRequestID(tt.id)
			if err != nil {
				t.Fatalf("Unexpected error creating request ID: %v", err)
			}

			result := requestID.String()
			if result != tt.expected {
				t.Errorf("Expected String() to return '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestRequestIDEquals(t *testing.T) {
	tests := []struct {
		name     string
		id1      string
		id2      string
		expected bool
	}{
		{
			name:     "Same IDs",
			id1:      "201507131507262221400000001975",
			id2:      "201507131507262221400000001975",
			expected: true,
		},
		{
			name:     "Different IDs",
			id1:      "201507131507262221400000001975",
			id2:      "201507131507262221400000001976",
			expected: false,
		},
		{
			name:     "Case sensitive",
			id1:      "REQ123",
			id2:      "req123",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestID1, err := NewRequestID(tt.id1)
			if err != nil {
				t.Fatalf("Unexpected error creating first request ID: %v", err)
			}

			requestID2, err := NewRequestID(tt.id2)
			if err != nil {
				t.Fatalf("Unexpected error creating second request ID: %v", err)
			}

			result := requestID1.Equals(requestID2)
			if result != tt.expected {
				t.Errorf("Expected Equals() to return %v for '%s' and '%s', got %v",
					tt.expected, tt.id1, tt.id2, result)
			}

			// Test symmetry
			reverseResult := requestID2.Equals(requestID1)
			if reverseResult != tt.expected {
				t.Errorf("Equals() is not symmetric: '%s'.Equals('%s') = %v, but '%s'.Equals('%s') = %v",
					tt.id1, tt.id2, result, tt.id2, tt.id1, reverseResult)
			}
		})
	}
}

func TestRequestIDValidation(t *testing.T) {
	tests := []struct {
		name     string
		id       string
		expected bool
	}{
		{
			name:     "Valid ID",
			id:       "201507131507262221400000001975",
			expected: true,
		},
		{
			name:     "Valid short ID",
			id:       "REQ123",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			requestID, err := NewRequestID(tt.id)
			if err != nil {
				t.Fatalf("Unexpected error creating request ID: %v", err)
			}

			result := requestID.Validate() == nil
			if result != tt.expected {
				t.Errorf("Expected validation to return %v for '%s', got %v",
					tt.expected, tt.id, result)
			}
		})
	}
}

func TestRequestIDBusinessRules(t *testing.T) {
	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test that request IDs follow OttoPay specification
		// Request IDs must be at most 255 characters and generated by bank

		validIDs := []string{
			"201507131507262221400000001975",       // OttoPay example
			"REQ123456789",                         // Simple format
			"550e8400-e29b-41d4-a716-************", // UUID format
		}

		for _, id := range validIDs {
			requestID, err := NewRequestID(id)
			if err != nil {
				t.Errorf("Expected valid request ID '%s' to be accepted, got error: %v", id, err)
			}
			if requestID == "" {
				t.Errorf("Expected non-empty request ID for '%s'", id)
			}
		}

		invalidIDs := []string{
			"",                       // Empty
			strings.Repeat("a", 256), // Too long
		}

		for _, id := range invalidIDs {
			requestID, err := NewRequestID(id)
			if err == nil {
				t.Errorf("Expected invalid request ID '%s' to be rejected", id)
			}
			if requestID != "" {
				t.Errorf("Expected empty request ID for invalid input '%s'", id)
			}
		}
	})

	t.Run("Uniqueness over time", func(t *testing.T) {
		// Test that generated IDs are unique even when generated quickly
		ids := make(map[string]bool)

		for i := 0; i < 10; i++ {
			inquiryID, err := GenerateInquiryRequestID()
			if err != nil {
				t.Fatalf("Error generating inquiry ID: %v", err)
			}

			paymentID, err := GeneratePaymentRequestID()
			if err != nil {
				t.Fatalf("Error generating payment ID: %v", err)
			}

			tokenID, err := GenerateTokenRequestID()
			if err != nil {
				t.Fatalf("Error generating token ID: %v", err)
			}

			allIDs := []RequestID{inquiryID, paymentID, tokenID}
			for _, id := range allIDs {
				idStr := id.String()
				if ids[idStr] {
					t.Errorf("Generated duplicate ID: %s", idStr)
				}
				ids[idStr] = true
			}

			// Small delay to ensure timestamp-based uniqueness
			time.Sleep(1 * time.Millisecond)
		}
	})

	t.Run("Prefix consistency", func(t *testing.T) {
		// Test that generated IDs have consistent prefixes
		inquiryID, err := GenerateInquiryRequestID()
		if err != nil {
			t.Fatalf("Error generating inquiry ID: %v", err)
		}
		if !strings.HasPrefix(inquiryID.String(), "INQ") {
			t.Errorf("Inquiry ID should start with 'INQ', got: %s", inquiryID.String())
		}

		paymentID, err := GeneratePaymentRequestID()
		if err != nil {
			t.Fatalf("Error generating payment ID: %v", err)
		}
		if !strings.HasPrefix(paymentID.String(), "PAY") {
			t.Errorf("Payment ID should start with 'PAY', got: %s", paymentID.String())
		}

		tokenID, err := GenerateTokenRequestID()
		if err != nil {
			t.Fatalf("Error generating token ID: %v", err)
		}
		if !strings.HasPrefix(tokenID.String(), "TOK") {
			t.Errorf("Token ID should start with 'TOK', got: %s", tokenID.String())
		}
	})
}
