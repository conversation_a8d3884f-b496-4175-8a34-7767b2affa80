package valueobjects

import (
	"testing"
)

func TestNewAmount(t *testing.T) {
	tests := []struct {
		name        string
		value       float64
		currency    CurrencyCode
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid IDR amount",
			value:       100000.50,
			currency:    CurrencyIDR,
			expectError: false,
		},
		{
			name:        "Valid USD amount",
			value:       1000.99,
			currency:    CurrencyUSD,
			expectError: false,
		},
		{
			name:        "Zero amount",
			value:       0.0,
			currency:    CurrencyIDR,
			expectError: false,
		},
		{
			name:        "Negative amount",
			value:       -100.0,
			currency:    CurrencyIDR,
			expectError: true,
			errorMsg:    "amount cannot be negative",
		},
		{
			name:        "Amount too large",
			value:       10000000000000.0,
			currency:    CurrencyIDR,
			expectError: true,
			errorMsg:    "amount is too large",
		},
		{
			name:        "Invalid currency",
			value:       100.0,
			currency:    "INVALID",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			amount, err := NewAmount(tt.value, tt.currency)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if amount.Value() != tt.value {
				t.Errorf("Expected value %f, got %f", tt.value, amount.Value())
			}

			if amount.Currency() != tt.currency {
				t.Errorf("Expected currency %s, got %s", tt.currency, amount.Currency())
			}
		})
	}
}

func TestNewAmountFromString(t *testing.T) {
	currency := CurrencyIDR

	tests := []struct {
		name        string
		value       string
		expectError bool
		expectedVal float64
	}{
		{
			name:        "Valid string amount",
			value:       "100000.50",
			expectError: false,
			expectedVal: 100000.50,
		},
		{
			name:        "Integer string",
			value:       "1000",
			expectError: false,
			expectedVal: 1000.0,
		},
		{
			name:        "Zero string",
			value:       "0",
			expectError: false,
			expectedVal: 0.0,
		},
		{
			name:        "Empty string",
			value:       "",
			expectError: true,
		},
		{
			name:        "Invalid string",
			value:       "abc",
			expectError: true,
		},
		{
			name:        "Negative string",
			value:       "-100.0",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			amount, err := NewAmountFromString(tt.value, currency)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if amount.Value() != tt.expectedVal {
				t.Errorf("Expected value %f, got %f", tt.expectedVal, amount.Value())
			}
		})
	}
}

func TestAmountOperations(t *testing.T) {
	currency := CurrencyIDR
	amount1, _ := NewAmount(100.0, currency)
	amount2, _ := NewAmount(50.0, currency)
	differentCurrency, _ := NewAmount(100.0, CurrencyUSD)

	t.Run("Add same currency", func(t *testing.T) {
		result, err := amount1.Add(amount2)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if result.Value() != 150.0 {
			t.Errorf("Expected 150.0, got %f", result.Value())
		}
	})

	t.Run("Add different currency", func(t *testing.T) {
		_, err := amount1.Add(differentCurrency)
		if err == nil {
			t.Errorf("Expected error for different currencies")
		}
	})

	t.Run("Subtract same currency", func(t *testing.T) {
		result, err := amount1.Subtract(amount2)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if result.Value() != 50.0 {
			t.Errorf("Expected 50.0, got %f", result.Value())
		}
	})

	t.Run("Multiply", func(t *testing.T) {
		result, err := amount1.Multiply(2.0)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if result.Value() != 200.0 {
			t.Errorf("Expected 200.0, got %f", result.Value())
		}
	})

	t.Run("Divide", func(t *testing.T) {
		result, err := amount1.Divide(2.0)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		if result.Value() != 50.0 {
			t.Errorf("Expected 50.0, got %f", result.Value())
		}
	})

	t.Run("Divide by zero", func(t *testing.T) {
		_, err := amount1.Divide(0.0)
		if err == nil {
			t.Errorf("Expected error for division by zero")
		}
	})
}

func TestAmountComparisons(t *testing.T) {
	currency := CurrencyIDR
	amount1, _ := NewAmount(100.0, currency)
	amount2, _ := NewAmount(50.0, currency)
	amount3, _ := NewAmount(100.0, currency)
	differentCurrency, _ := NewAmount(100.0, CurrencyUSD)

	t.Run("Equals same currency and value", func(t *testing.T) {
		if !amount1.Equals(amount3) {
			t.Errorf("Expected amounts to be equal")
		}
	})

	t.Run("Equals different currency", func(t *testing.T) {
		if amount1.Equals(differentCurrency) {
			t.Errorf("Expected amounts with different currencies to not be equal")
		}
	})

	t.Run("GreaterThan", func(t *testing.T) {
		if !amount1.GreaterThan(amount2) {
			t.Errorf("Expected amount1 to be greater than amount2")
		}
		if amount2.GreaterThan(amount1) {
			t.Errorf("Expected amount2 to not be greater than amount1")
		}
	})

	t.Run("LessThan", func(t *testing.T) {
		if !amount2.LessThan(amount1) {
			t.Errorf("Expected amount2 to be less than amount1")
		}
		if amount1.LessThan(amount2) {
			t.Errorf("Expected amount1 to not be less than amount2")
		}
	})

	t.Run("GreaterThanOrEqual", func(t *testing.T) {
		if !amount1.GreaterThanOrEqual(amount3) {
			t.Errorf("Expected amount1 to be greater than or equal to amount3")
		}
		if !amount1.GreaterThanOrEqual(amount2) {
			t.Errorf("Expected amount1 to be greater than or equal to amount2")
		}
	})

	t.Run("LessThanOrEqual", func(t *testing.T) {
		if !amount1.LessThanOrEqual(amount3) {
			t.Errorf("Expected amount1 to be less than or equal to amount3")
		}
		if !amount2.LessThanOrEqual(amount1) {
			t.Errorf("Expected amount2 to be less than or equal to amount1")
		}
	})
}

func TestAmountProperties(t *testing.T) {
	currency := CurrencyIDR
	zeroAmount, _ := NewAmount(0.0, currency)
	positiveAmount, _ := NewAmount(100.0, currency)

	t.Run("IsZero", func(t *testing.T) {
		if !zeroAmount.IsZero() {
			t.Errorf("Expected zero amount to be zero")
		}
		if positiveAmount.IsZero() {
			t.Errorf("Expected positive amount to not be zero")
		}
	})

	t.Run("IsPositive", func(t *testing.T) {
		if zeroAmount.IsPositive() {
			t.Errorf("Expected zero amount to not be positive")
		}
		if !positiveAmount.IsPositive() {
			t.Errorf("Expected positive amount to be positive")
		}
	})

	t.Run("IsNegative", func(t *testing.T) {
		if zeroAmount.IsNegative() {
			t.Errorf("Expected zero amount to not be negative")
		}
		if positiveAmount.IsNegative() {
			t.Errorf("Expected positive amount to not be negative")
		}
	})
}

func TestAmountString(t *testing.T) {
	tests := []struct {
		name                 string
		value                float64
		currency             CurrencyCode
		expectedString       string
		expectedWithCurrency string
		expectedWithSymbol   string
	}{
		{
			name:                 "IDR amount",
			value:                100000.50,
			currency:             CurrencyIDR,
			expectedString:       "100000.50",
			expectedWithCurrency: "100000.50 IDR",
			expectedWithSymbol:   "Rp100000.50",
		},
		{
			name:                 "USD amount",
			value:                1000.99,
			currency:             CurrencyUSD,
			expectedString:       "1000.99",
			expectedWithCurrency: "1000.99 USD",
			expectedWithSymbol:   "$1000.99",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			amount, err := NewAmount(tt.value, tt.currency)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if amount.String() != tt.expectedString {
				t.Errorf("Expected string '%s', got '%s'", tt.expectedString, amount.String())
			}

			if amount.StringWithCurrency() != tt.expectedWithCurrency {
				t.Errorf("Expected string with currency '%s', got '%s'", tt.expectedWithCurrency, amount.StringWithCurrency())
			}

			if amount.StringWithSymbol() != tt.expectedWithSymbol {
				t.Errorf("Expected string with symbol '%s', got '%s'", tt.expectedWithSymbol, amount.StringWithSymbol())
			}
		})
	}
}

func TestAmountMinorUnits(t *testing.T) {
	currency := CurrencyIDR
	amount, _ := NewAmount(100.50, currency)

	t.Run("ToMinorUnits", func(t *testing.T) {
		minorUnits := amount.ToMinorUnits()
		expected := int64(10050) // 100.50 * 100
		if minorUnits != expected {
			t.Errorf("Expected %d minor units, got %d", expected, minorUnits)
		}
	})

	t.Run("FromMinorUnits", func(t *testing.T) {
		minorUnits := int64(10050)
		amount, err := FromMinorUnits(minorUnits, currency)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		expected := 100.50
		if amount.Value() != expected {
			t.Errorf("Expected value %f, got %f", expected, amount.Value())
		}
	})
}

func TestAmountRound(t *testing.T) {
	currency := CurrencyIDR
	// Use a valid amount with exact decimal places for IDR
	amount, _ := NewAmount(100.56, currency)

	rounded := amount.Round()
	expected := 100.56 // Should remain the same since it's already properly rounded

	// Use a small tolerance for floating point comparison
	tolerance := 0.001
	if abs(rounded.Value()-expected) > tolerance {
		t.Errorf("Expected rounded value %f, got %f", expected, rounded.Value())
	}
}

// Helper function for absolute value
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
