package valueobjects

import (
	"errors"
	"strings"
)

// CurrencyCode represents a currency code value object
type CurrencyCode string

// Supported currency codes
const (
	CurrencyIDR CurrencyCode = "IDR"
	CurrencyUSD CurrencyCode = "USD"
)

var (
	ErrInvalidCurrencyCode = errors.New("invalid currency code")
	ErrUnsupportedCurrency = errors.New("unsupported currency")
)

// supportedCurrencies contains all supported currency codes
var supportedCurrencies = map[CurrencyCode]bool{
	CurrencyIDR: true,
	CurrencyUSD: true,
}

// NewCurrencyCode creates a new currency code value object
func NewCurrencyCode(code string) (CurrencyCode, error) {
	currency := CurrencyCode(strings.ToUpper(strings.TrimSpace(code)))
	
	if err := currency.Validate(); err != nil {
		return "", err
	}
	
	return currency, nil
}

// Validate validates the currency code
func (c CurrencyCode) Validate() error {
	if c == "" {
		return ErrInvalidCurrencyCode
	}
	
	if !supportedCurrencies[c] {
		return ErrUnsupportedCurrency
	}
	
	return nil
}

// String returns the string representation of the currency code
func (c CurrencyCode) String() string {
	return string(c)
}

// IsIDR checks if the currency is Indonesian Rupiah
func (c CurrencyCode) IsIDR() bool {
	return c == CurrencyIDR
}

// IsUSD checks if the currency is US Dollar
func (c CurrencyCode) IsUSD() bool {
	return c == CurrencyUSD
}

// GetSymbol returns the currency symbol
func (c CurrencyCode) GetSymbol() string {
	switch c {
	case CurrencyIDR:
		return "Rp"
	case CurrencyUSD:
		return "$"
	default:
		return string(c)
	}
}

// GetName returns the full name of the currency
func (c CurrencyCode) GetName() string {
	switch c {
	case CurrencyIDR:
		return "Indonesian Rupiah"
	case CurrencyUSD:
		return "US Dollar"
	default:
		return string(c)
	}
}

// GetDecimalPlaces returns the number of decimal places for the currency
func (c CurrencyCode) GetDecimalPlaces() int {
	switch c {
	case CurrencyIDR:
		return 2 // IDR typically uses 2 decimal places in financial systems
	case CurrencyUSD:
		return 2
	default:
		return 2
	}
}

// Equals checks if two currency codes are equal
func (c CurrencyCode) Equals(other CurrencyCode) bool {
	return c == other
}

// GetSupportedCurrencies returns all supported currency codes
func GetSupportedCurrencies() []CurrencyCode {
	currencies := make([]CurrencyCode, 0, len(supportedCurrencies))
	for currency := range supportedCurrencies {
		currencies = append(currencies, currency)
	}
	return currencies
}

// IsSupportedCurrency checks if a currency code is supported
func IsSupportedCurrency(code string) bool {
	currency := CurrencyCode(strings.ToUpper(strings.TrimSpace(code)))
	return supportedCurrencies[currency]
}
