package valueobjects

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"
)

// RequestID represents a request ID value object
type RequestID string

var (
	ErrInvalidRequestID   = errors.New("invalid request ID")
	ErrRequestIDTooLong   = errors.New("request ID cannot exceed 255 characters")
	ErrRequestIDEmpty     = errors.New("request ID cannot be empty")
	ErrRequestIDDuplicate = errors.New("request ID already exists")
)

// NewRequestID creates a new request ID value object
func NewRequestID(id string) (RequestID, error) {
	requestID := RequestID(strings.TrimSpace(id))
	
	if err := requestID.Validate(); err != nil {
		return "", err
	}
	
	return requestID, nil
}

// GenerateRequestID generates a new unique request ID with a prefix
func GenerateRequestID(prefix string) (RequestID, error) {
	timestamp := time.Now().Format("20060102150405")
	
	// Generate random bytes
	randomBytes := make([]byte, 4)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	
	randomHex := hex.EncodeToString(randomBytes)
	
	var id string
	if prefix != "" {
		id = fmt.Sprintf("%s-%s-%s", prefix, timestamp, randomHex)
	} else {
		id = fmt.Sprintf("%s-%s", timestamp, randomHex)
	}
	
	return NewRequestID(id)
}

// GenerateInquiryRequestID generates a request ID specifically for inquiry operations
func GenerateInquiryRequestID() (RequestID, error) {
	return GenerateRequestID("INQ")
}

// GeneratePaymentRequestID generates a request ID specifically for payment operations
func GeneratePaymentRequestID() (RequestID, error) {
	return GenerateRequestID("PAY")
}

// GenerateTokenRequestID generates a request ID specifically for token operations
func GenerateTokenRequestID() (RequestID, error) {
	return GenerateRequestID("TOK")
}

// Validate validates the request ID
func (r RequestID) Validate() error {
	if r == "" {
		return ErrRequestIDEmpty
	}
	
	if len(string(r)) > 255 {
		return ErrRequestIDTooLong
	}
	
	return nil
}

// String returns the string representation of the request ID
func (r RequestID) String() string {
	return string(r)
}

// Length returns the length of the request ID
func (r RequestID) Length() int {
	return len(string(r))
}

// IsEmpty checks if the request ID is empty
func (r RequestID) IsEmpty() bool {
	return r == ""
}

// Equals checks if two request IDs are equal
func (r RequestID) Equals(other RequestID) bool {
	return r == other
}

// HasPrefix checks if the request ID has a specific prefix
func (r RequestID) HasPrefix(prefix string) bool {
	return strings.HasPrefix(string(r), prefix)
}

// HasSuffix checks if the request ID has a specific suffix
func (r RequestID) HasSuffix(suffix string) bool {
	return strings.HasSuffix(string(r), suffix)
}

// GetPrefix returns the prefix of the request ID (everything before the first dash)
func (r RequestID) GetPrefix() string {
	str := string(r)
	parts := strings.Split(str, "-")
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

// GetTimestamp extracts the timestamp from the request ID if it follows the standard format
func (r RequestID) GetTimestamp() (time.Time, error) {
	str := string(r)
	parts := strings.Split(str, "-")
	
	if len(parts) < 2 {
		return time.Time{}, errors.New("invalid request ID format for timestamp extraction")
	}
	
	timestampStr := parts[1]
	if len(timestampStr) != 14 {
		return time.Time{}, errors.New("invalid timestamp format in request ID")
	}
	
	timestamp, err := time.Parse("20060102150405", timestampStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse timestamp: %w", err)
	}
	
	return timestamp, nil
}

// IsInquiryRequest checks if this is an inquiry request ID
func (r RequestID) IsInquiryRequest() bool {
	return r.HasPrefix("INQ")
}

// IsPaymentRequest checks if this is a payment request ID
func (r RequestID) IsPaymentRequest() bool {
	return r.HasPrefix("PAY")
}

// IsTokenRequest checks if this is a token request ID
func (r RequestID) IsTokenRequest() bool {
	return r.HasPrefix("TOK")
}

// GetAge returns the age of the request ID based on its timestamp
func (r RequestID) GetAge() (time.Duration, error) {
	timestamp, err := r.GetTimestamp()
	if err != nil {
		return 0, err
	}
	
	return time.Since(timestamp), nil
}

// IsExpired checks if the request ID is older than the specified duration
func (r RequestID) IsExpired(maxAge time.Duration) bool {
	age, err := r.GetAge()
	if err != nil {
		return false // If we can't determine age, assume not expired
	}
	
	return age > maxAge
}

// ToCorrelationID converts the request ID to a correlation ID format
func (r RequestID) ToCorrelationID() string {
	return fmt.Sprintf("CORR-%s", string(r))
}

// Mask masks the request ID for logging purposes (shows only prefix and suffix)
func (r RequestID) Mask() string {
	str := string(r)
	if len(str) <= 8 {
		return strings.Repeat("*", len(str))
	}
	
	prefix := str[:4]
	suffix := str[len(str)-4:]
	middle := strings.Repeat("*", len(str)-8)
	
	return prefix + middle + suffix
}

// IsValidFormat checks if the request ID has a valid format
func (r RequestID) IsValidFormat() bool {
	return r.Validate() == nil
}

// WithSuffix appends a suffix to the request ID
func (r RequestID) WithSuffix(suffix string) (RequestID, error) {
	newID := string(r) + "-" + suffix
	return NewRequestID(newID)
}

// Clone creates a copy of the request ID
func (r RequestID) Clone() RequestID {
	return RequestID(string(r))
}
