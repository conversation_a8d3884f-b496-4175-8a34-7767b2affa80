package valueobjects

import (
	"strings"
	"testing"
)

func TestNewCurrencyCode(t *testing.T) {
	tests := []struct {
		name        string
		code        string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid IDR currency",
			code:        "IDR",
			expectError: false,
		},
		{
			name:        "Valid USD currency",
			code:        "USD",
			expectError: false,
		},
		{
			name:        "Empty currency code",
			code:        "",
			expectError: true,
			errorMsg:    "invalid currency code",
		},
		{
			name:        "Invalid currency code",
			code:        "XYZ",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
		{
			name:        "Lowercase currency code (should work - auto-converted)",
			code:        "idr",
			expectError: false,
		},
		{
			name:        "Mixed case currency code (should work - auto-converted)",
			code:        "Idr",
			expectError: false,
		},
		{
			name:        "Currency code too short",
			code:        "ID",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
		{
			name:        "Currency code too long",
			code:        "IDRU",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
		{
			name:        "Currency code with numbers",
			code:        "ID1",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
		{
			name:        "Currency code with special characters",
			code:        "ID-",
			expectError: true,
			errorMsg:    "unsupported currency",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode, err := NewCurrencyCode(tt.code)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for currency code '%s', but got none", tt.code)
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("Expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
				if currencyCode != "" {
					t.Errorf("Expected empty currency code for invalid input, got %v", currencyCode)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for valid currency code '%s': %v", tt.code, err)
					return
				}
				if currencyCode == "" {
					t.Errorf("Expected valid currency code, got empty")
					return
				}
				// For case conversion, check the uppercase version
				expected := strings.ToUpper(tt.code)
				if currencyCode.String() != expected {
					t.Errorf("Expected currency code string '%s', got '%s'", expected, currencyCode.String())
				}
			}
		})
	}
}

func TestCurrencyCodeString(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected string
	}{
		{
			name:     "IDR currency",
			code:     "IDR",
			expected: "IDR",
		},
		{
			name:     "USD currency",
			code:     "USD",
			expected: "USD",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode, err := NewCurrencyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating currency code: %v", err)
			}

			result := currencyCode.String()
			if result != tt.expected {
				t.Errorf("Expected String() to return '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestCurrencyCodeEquals(t *testing.T) {
	tests := []struct {
		name     string
		code1    string
		code2    string
		expected bool
	}{
		{
			name:     "Same currencies",
			code1:    "IDR",
			code2:    "IDR",
			expected: true,
		},
		{
			name:     "Different currencies",
			code1:    "IDR",
			code2:    "USD",
			expected: false,
		},
		{
			name:     "USD equals USD",
			code1:    "USD",
			code2:    "USD",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode1, err := NewCurrencyCode(tt.code1)
			if err != nil {
				t.Fatalf("Unexpected error creating first currency code: %v", err)
			}

			currencyCode2, err := NewCurrencyCode(tt.code2)
			if err != nil {
				t.Fatalf("Unexpected error creating second currency code: %v", err)
			}

			result := currencyCode1.Equals(currencyCode2)
			if result != tt.expected {
				t.Errorf("Expected Equals() to return %v for '%s' and '%s', got %v",
					tt.expected, tt.code1, tt.code2, result)
			}

			// Test symmetry
			reverseResult := currencyCode2.Equals(currencyCode1)
			if reverseResult != tt.expected {
				t.Errorf("Equals() is not symmetric: '%s'.Equals('%s') = %v, but '%s'.Equals('%s') = %v",
					tt.code1, tt.code2, result, tt.code2, tt.code1, reverseResult)
			}
		})
	}
}

func TestCurrencyCodeValidation(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected bool
	}{
		{
			name:     "Valid IDR",
			code:     "IDR",
			expected: true,
		},
		{
			name:     "Valid USD",
			code:     "USD",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode, err := NewCurrencyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating currency code: %v", err)
			}

			result := currencyCode.Validate() == nil
			if result != tt.expected {
				t.Errorf("Expected validation to return %v for '%s', got %v",
					tt.expected, tt.code, result)
			}
		})
	}
}

func TestCurrencyCodeGetDecimalPlaces(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected int
	}{
		{
			name:     "IDR has 2 decimal places",
			code:     "IDR",
			expected: 2,
		},
		{
			name:     "USD has 2 decimal places",
			code:     "USD",
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode, err := NewCurrencyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating currency code: %v", err)
			}

			result := currencyCode.GetDecimalPlaces()
			if result != tt.expected {
				t.Errorf("Expected GetDecimalPlaces() to return %d for '%s', got %d",
					tt.expected, tt.code, result)
			}
		})
	}
}

func TestCurrencyCodeGetSymbol(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected string
	}{
		{
			name:     "IDR symbol",
			code:     "IDR",
			expected: "Rp",
		},
		{
			name:     "USD symbol",
			code:     "USD",
			expected: "$",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			currencyCode, err := NewCurrencyCode(tt.code)
			if err != nil {
				t.Fatalf("Unexpected error creating currency code: %v", err)
			}

			result := currencyCode.GetSymbol()
			if result != tt.expected {
				t.Errorf("Expected GetSymbol() to return '%s' for '%s', got '%s'",
					tt.expected, tt.code, result)
			}
		})
	}
}

func TestCurrencyCodeBusinessRules(t *testing.T) {
	t.Run("OttoPay specification compliance", func(t *testing.T) {
		// Test that only IDR and USD are supported as per OttoPay specification
		supportedCurrencies := []string{"IDR", "USD"}
		for _, code := range supportedCurrencies {
			currencyCode, err := NewCurrencyCode(code)
			if err != nil {
				t.Errorf("Expected supported currency '%s' to be accepted, got error: %v", code, err)
			}
			if currencyCode == "" {
				t.Errorf("Expected non-empty currency code for '%s'", code)
			}
		}

		unsupportedCurrencies := []string{"EUR", "GBP", "JPY", "CNY", "SGD"}
		for _, code := range unsupportedCurrencies {
			currencyCode, err := NewCurrencyCode(code)
			if err == nil {
				t.Errorf("Expected unsupported currency '%s' to be rejected", code)
			}
			if currencyCode != "" {
				t.Errorf("Expected empty currency code for unsupported currency '%s'", code)
			}
		}
	})

	t.Run("Case insensitivity (auto-conversion)", func(t *testing.T) {
		// Currency codes are auto-converted to uppercase
		testCases := []struct {
			input    string
			expected string
		}{
			{"IDR", "IDR"},
			{"idr", "IDR"},
			{"Idr", "IDR"},
			{"iDr", "IDR"},
			{"idR", "IDR"},
			{"USD", "USD"},
			{"usd", "USD"},
		}

		for _, tc := range testCases {
			currencyCode, err := NewCurrencyCode(tc.input)
			if err != nil {
				t.Errorf("Expected currency code '%s' to be accepted, got error: %v", tc.input, err)
				continue
			}
			if currencyCode.String() != tc.expected {
				t.Errorf("Expected currency code '%s' to be converted to '%s', got '%s'",
					tc.input, tc.expected, currencyCode.String())
			}
		}
	})

	t.Run("Decimal places consistency", func(t *testing.T) {
		// Both supported currencies should have 2 decimal places for OttoPay
		currencies := []string{"IDR", "USD"}
		for _, code := range currencies {
			currencyCode, err := NewCurrencyCode(code)
			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			decimalPlaces := currencyCode.GetDecimalPlaces()
			if decimalPlaces != 2 {
				t.Errorf("Expected 2 decimal places for '%s', got %d", code, decimalPlaces)
			}
		}
	})
}
