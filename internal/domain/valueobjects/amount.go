package valueobjects

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
)

// Amount represents a monetary amount value object
type Amount struct {
	value    float64
	currency CurrencyCode
}

var (
	ErrInvalidAmount     = errors.New("invalid amount")
	ErrNegativeAmount    = errors.New("amount cannot be negative")
	ErrAmountTooLarge    = errors.New("amount is too large")
	ErrAmountPrecision   = errors.New("amount has too many decimal places")
	ErrInvalidAmountFormat = errors.New("invalid amount format")
)

const (
	// Maximum amount value (15 characters including decimal point)
	MaxAmountValue = 9999999999999.99
	// Minimum amount value
	MinAmountValue = 0.0
)

// NewAmount creates a new amount value object from a float64
func NewAmount(value float64, currency CurrencyCode) (Amount, error) {
	amount := Amount{
		value:    value,
		currency: currency,
	}
	
	if err := amount.Validate(); err != nil {
		return Amount{}, err
	}
	
	return amount, nil
}

// NewAmountFromString creates a new amount value object from a string
func NewAmountFromString(value string, currency CurrencyCode) (Amount, error) {
	// Clean the string
	cleanValue := strings.TrimSpace(value)
	if cleanValue == "" {
		return Amount{}, ErrInvalidAmountFormat
	}
	
	// Parse the float value
	floatValue, err := strconv.ParseFloat(cleanValue, 64)
	if err != nil {
		return Amount{}, ErrInvalidAmountFormat
	}
	
	return NewAmount(floatValue, currency)
}

// Validate validates the amount
func (a Amount) Validate() error {
	if err := a.currency.Validate(); err != nil {
		return err
	}
	
	if math.IsNaN(a.value) || math.IsInf(a.value, 0) {
		return ErrInvalidAmount
	}
	
	if a.value < MinAmountValue {
		return ErrNegativeAmount
	}
	
	if a.value > MaxAmountValue {
		return ErrAmountTooLarge
	}
	
	// Check decimal places
	decimalPlaces := a.currency.GetDecimalPlaces()
	multiplier := math.Pow(10, float64(decimalPlaces))
	if math.Mod(a.value*multiplier, 1) != 0 {
		return ErrAmountPrecision
	}
	
	return nil
}

// Value returns the numeric value of the amount
func (a Amount) Value() float64 {
	return a.value
}

// Currency returns the currency of the amount
func (a Amount) Currency() CurrencyCode {
	return a.currency
}

// String returns the string representation of the amount
func (a Amount) String() string {
	decimalPlaces := a.currency.GetDecimalPlaces()
	format := fmt.Sprintf("%%.%df", decimalPlaces)
	return fmt.Sprintf(format, a.value)
}

// StringWithCurrency returns the string representation with currency
func (a Amount) StringWithCurrency() string {
	return fmt.Sprintf("%s %s", a.String(), a.currency.String())
}

// StringWithSymbol returns the string representation with currency symbol
func (a Amount) StringWithSymbol() string {
	return fmt.Sprintf("%s%s", a.currency.GetSymbol(), a.String())
}

// IsZero checks if the amount is zero
func (a Amount) IsZero() bool {
	return a.value == 0.0
}

// IsPositive checks if the amount is positive
func (a Amount) IsPositive() bool {
	return a.value > 0.0
}

// IsNegative checks if the amount is negative
func (a Amount) IsNegative() bool {
	return a.value < 0.0
}

// Add adds another amount to this amount
func (a Amount) Add(other Amount) (Amount, error) {
	if !a.currency.Equals(other.currency) {
		return Amount{}, errors.New("cannot add amounts with different currencies")
	}
	
	return NewAmount(a.value+other.value, a.currency)
}

// Subtract subtracts another amount from this amount
func (a Amount) Subtract(other Amount) (Amount, error) {
	if !a.currency.Equals(other.currency) {
		return Amount{}, errors.New("cannot subtract amounts with different currencies")
	}
	
	return NewAmount(a.value-other.value, a.currency)
}

// Multiply multiplies the amount by a factor
func (a Amount) Multiply(factor float64) (Amount, error) {
	return NewAmount(a.value*factor, a.currency)
}

// Divide divides the amount by a divisor
func (a Amount) Divide(divisor float64) (Amount, error) {
	if divisor == 0 {
		return Amount{}, errors.New("cannot divide by zero")
	}
	
	return NewAmount(a.value/divisor, a.currency)
}

// Equals checks if two amounts are equal
func (a Amount) Equals(other Amount) bool {
	return a.currency.Equals(other.currency) && a.value == other.value
}

// GreaterThan checks if this amount is greater than another
func (a Amount) GreaterThan(other Amount) bool {
	if !a.currency.Equals(other.currency) {
		return false
	}
	return a.value > other.value
}

// LessThan checks if this amount is less than another
func (a Amount) LessThan(other Amount) bool {
	if !a.currency.Equals(other.currency) {
		return false
	}
	return a.value < other.value
}

// GreaterThanOrEqual checks if this amount is greater than or equal to another
func (a Amount) GreaterThanOrEqual(other Amount) bool {
	if !a.currency.Equals(other.currency) {
		return false
	}
	return a.value >= other.value
}

// LessThanOrEqual checks if this amount is less than or equal to another
func (a Amount) LessThanOrEqual(other Amount) bool {
	if !a.currency.Equals(other.currency) {
		return false
	}
	return a.value <= other.value
}

// Round rounds the amount to the currency's decimal places
func (a Amount) Round() Amount {
	decimalPlaces := a.currency.GetDecimalPlaces()
	multiplier := math.Pow(10, float64(decimalPlaces))
	rounded := math.Round(a.value*multiplier) / multiplier
	
	// This should not fail since we're rounding to valid precision
	result, _ := NewAmount(rounded, a.currency)
	return result
}

// ToMinorUnits converts the amount to minor units (e.g., cents for USD)
func (a Amount) ToMinorUnits() int64 {
	decimalPlaces := a.currency.GetDecimalPlaces()
	multiplier := math.Pow(10, float64(decimalPlaces))
	return int64(math.Round(a.value * multiplier))
}

// FromMinorUnits creates an amount from minor units
func FromMinorUnits(minorUnits int64, currency CurrencyCode) (Amount, error) {
	decimalPlaces := currency.GetDecimalPlaces()
	divisor := math.Pow(10, float64(decimalPlaces))
	value := float64(minorUnits) / divisor
	
	return NewAmount(value, currency)
}
