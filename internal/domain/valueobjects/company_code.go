package valueobjects

import (
	"errors"
	"regexp"
	"strings"
)

// CompanyCode represents a company code value object
type CompanyCode string

var (
	ErrInvalidCompanyCode = errors.New("invalid company code")
	ErrCompanyCodeTooLong = errors.New("company code cannot exceed 5 characters")
	ErrCompanyCodeEmpty   = errors.New("company code cannot be empty")
	ErrCompanyCodeFormat  = errors.New("company code must contain only numeric characters")
)

// companyCodeRegex validates that company code contains only digits
var companyCodeRegex = regexp.MustCompile(`^[0-9]+$`)

// NewCompanyCode creates a new company code value object
func NewCompanyCode(code string) (CompanyCode, error) {
	companyCode := CompanyCode(strings.TrimSpace(code))
	
	if err := companyCode.Validate(); err != nil {
		return "", err
	}
	
	return companyCode, nil
}

// Validate validates the company code
func (c CompanyCode) Validate() error {
	if c == "" {
		return ErrCompanyCodeEmpty
	}
	
	if len(string(c)) > 5 {
		return ErrCompanyCodeTooLong
	}
	
	if !companyCodeRegex.MatchString(string(c)) {
		return ErrCompanyCodeFormat
	}
	
	return nil
}

// String returns the string representation of the company code
func (c CompanyCode) String() string {
	return string(c)
}

// Length returns the length of the company code
func (c CompanyCode) Length() int {
	return len(string(c))
}

// IsEmpty checks if the company code is empty
func (c CompanyCode) IsEmpty() bool {
	return c == ""
}

// Equals checks if two company codes are equal
func (c CompanyCode) Equals(other CompanyCode) bool {
	return c == other
}

// PadLeft pads the company code with zeros on the left to reach the specified length
func (c CompanyCode) PadLeft(length int) CompanyCode {
	current := string(c)
	if len(current) >= length {
		return c
	}
	
	padding := strings.Repeat("0", length-len(current))
	return CompanyCode(padding + current)
}

// PadRight pads the company code with zeros on the right to reach the specified length
func (c CompanyCode) PadRight(length int) CompanyCode {
	current := string(c)
	if len(current) >= length {
		return c
	}
	
	padding := strings.Repeat("0", length-len(current))
	return CompanyCode(current + padding)
}

// ToStandardFormat converts the company code to a standard 5-digit format
func (c CompanyCode) ToStandardFormat() CompanyCode {
	return c.PadLeft(5)
}

// IsValidFormat checks if the company code has a valid format
func (c CompanyCode) IsValidFormat() bool {
	return c.Validate() == nil
}

// ToInt converts the company code to an integer
func (c CompanyCode) ToInt() (int, error) {
	str := string(c)
	if str == "" {
		return 0, ErrCompanyCodeEmpty
	}
	
	result := 0
	for _, char := range str {
		if char < '0' || char > '9' {
			return 0, ErrCompanyCodeFormat
		}
		result = result*10 + int(char-'0')
	}
	
	return result, nil
}

// FromInt creates a company code from an integer
func CompanyCodeFromInt(code int) (CompanyCode, error) {
	if code < 0 {
		return "", ErrInvalidCompanyCode
	}
	
	if code > 99999 { // Maximum 5 digits
		return "", ErrCompanyCodeTooLong
	}
	
	str := ""
	if code == 0 {
		str = "0"
	} else {
		for code > 0 {
			str = string(rune('0'+code%10)) + str
			code /= 10
		}
	}
	
	return NewCompanyCode(str)
}

// GetRegion returns the region code based on company code patterns
// This is a business-specific method that can be customized
func (c CompanyCode) GetRegion() string {
	code := string(c)
	if len(code) >= 2 {
		prefix := code[:2]
		switch prefix {
		case "12":
			return "JAKARTA"
		case "13":
			return "BANDUNG"
		case "14":
			return "SURABAYA"
		case "15":
			return "MEDAN"
		default:
			return "UNKNOWN"
		}
	}
	return "UNKNOWN"
}

// IsTestCode checks if this is a test company code
func (c CompanyCode) IsTestCode() bool {
	// Test codes typically start with 99
	return strings.HasPrefix(string(c), "99")
}

// IsProductionCode checks if this is a production company code
func (c CompanyCode) IsProductionCode() bool {
	return !c.IsTestCode()
}
