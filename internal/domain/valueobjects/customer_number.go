package valueobjects

import (
	"errors"
	"regexp"
	"strings"
)

// CustomerNumber represents a customer number value object
type CustomerNumber string

var (
	ErrInvalidCustomerNumber = errors.New("invalid customer number")
	ErrCustomerNumberTooLong = errors.New("customer number cannot exceed 11 characters")
	ErrCustomerNumberEmpty   = errors.New("customer number cannot be empty")
	ErrCustomerNumberFormat  = errors.New("customer number must contain only numeric characters")
)

// customerNumberRegex validates that customer number contains only digits
var customerNumberRegex = regexp.MustCompile(`^[0-9]+$`)

// NewCustomerNumber creates a new customer number value object
func NewCustomerNumber(number string) (CustomerNumber, error) {
	customerNumber := CustomerNumber(strings.TrimSpace(number))
	
	if err := customerNumber.Validate(); err != nil {
		return "", err
	}
	
	return customerNumber, nil
}

// Validate validates the customer number
func (c CustomerNumber) Validate() error {
	if c == "" {
		return ErrCustomerNumberEmpty
	}
	
	if len(string(c)) > 11 {
		return ErrCustomerNumberTooLong
	}
	
	if !customerNumberRegex.MatchString(string(c)) {
		return ErrCustomerNumberFormat
	}
	
	return nil
}

// String returns the string representation of the customer number
func (c CustomerNumber) String() string {
	return string(c)
}

// Length returns the length of the customer number
func (c CustomerNumber) Length() int {
	return len(string(c))
}

// IsEmpty checks if the customer number is empty
func (c CustomerNumber) IsEmpty() bool {
	return c == ""
}

// Equals checks if two customer numbers are equal
func (c CustomerNumber) Equals(other CustomerNumber) bool {
	return c == other
}

// HasPrefix checks if the customer number has a specific prefix
func (c CustomerNumber) HasPrefix(prefix string) bool {
	return strings.HasPrefix(string(c), prefix)
}

// HasSuffix checks if the customer number has a specific suffix
func (c CustomerNumber) HasSuffix(suffix string) bool {
	return strings.HasSuffix(string(c), suffix)
}

// PadLeft pads the customer number with zeros on the left to reach the specified length
func (c CustomerNumber) PadLeft(length int) CustomerNumber {
	current := string(c)
	if len(current) >= length {
		return c
	}
	
	padding := strings.Repeat("0", length-len(current))
	return CustomerNumber(padding + current)
}

// PadRight pads the customer number with zeros on the right to reach the specified length
func (c CustomerNumber) PadRight(length int) CustomerNumber {
	current := string(c)
	if len(current) >= length {
		return c
	}
	
	padding := strings.Repeat("0", length-len(current))
	return CustomerNumber(current + padding)
}

// Mask masks the customer number for display purposes (shows only last 4 digits)
func (c CustomerNumber) Mask() string {
	str := string(c)
	if len(str) <= 4 {
		return strings.Repeat("*", len(str))
	}
	
	masked := strings.Repeat("*", len(str)-4)
	return masked + str[len(str)-4:]
}

// GetCheckDigit calculates a simple check digit for the customer number
func (c CustomerNumber) GetCheckDigit() int {
	str := string(c)
	sum := 0
	
	for i, char := range str {
		digit := int(char - '0')
		if i%2 == 0 {
			digit *= 2
			if digit > 9 {
				digit = digit/10 + digit%10
			}
		}
		sum += digit
	}
	
	return (10 - (sum % 10)) % 10
}

// WithCheckDigit appends a check digit to the customer number
func (c CustomerNumber) WithCheckDigit() (CustomerNumber, error) {
	checkDigit := c.GetCheckDigit()
	newNumber := string(c) + string(rune('0'+checkDigit))
	
	return NewCustomerNumber(newNumber)
}

// ValidateCheckDigit validates the check digit of the customer number
func (c CustomerNumber) ValidateCheckDigit() bool {
	str := string(c)
	if len(str) < 2 {
		return false
	}
	
	// Extract the number without the last digit
	numberPart := str[:len(str)-1]
	checkDigitPart := str[len(str)-1:]
	
	// Parse the check digit
	expectedCheckDigit := int(checkDigitPart[0] - '0')
	
	// Calculate the check digit for the number part
	numberCustomer := CustomerNumber(numberPart)
	calculatedCheckDigit := numberCustomer.GetCheckDigit()
	
	return expectedCheckDigit == calculatedCheckDigit
}

// IsValidFormat checks if the customer number has a valid format
func (c CustomerNumber) IsValidFormat() bool {
	return c.Validate() == nil
}

// ToInt64 converts the customer number to int64 (if possible)
func (c CustomerNumber) ToInt64() (int64, error) {
	str := string(c)
	if str == "" {
		return 0, ErrCustomerNumberEmpty
	}
	
	var result int64
	for _, char := range str {
		if char < '0' || char > '9' {
			return 0, ErrCustomerNumberFormat
		}
		result = result*10 + int64(char-'0')
	}
	
	return result, nil
}

// FromInt64 creates a customer number from int64
func CustomerNumberFromInt64(number int64) (CustomerNumber, error) {
	if number < 0 {
		return "", ErrInvalidCustomerNumber
	}
	
	str := ""
	if number == 0 {
		str = "0"
	} else {
		for number > 0 {
			str = string(rune('0'+number%10)) + str
			number /= 10
		}
	}
	
	return NewCustomerNumber(str)
}
