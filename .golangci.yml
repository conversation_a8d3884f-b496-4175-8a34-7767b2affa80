# golangci-lint configuration for OttoPay project

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - build
    - dist
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_mock\\.go$"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  uniq-by-line: true
  sort-results: true

linters-settings:
  # Cyclomatic complexity
  cyclop:
    max-complexity: 15
    package-average: 10.0
    skip-tests: true

  # Duplicate code detection
  dupl:
    threshold: 100

  # Error handling
  errcheck:
    check-type-assertions: true
    check-blank: true
    exclude-functions:
      - io/ioutil.ReadFile
      - io.Copy(*bytes.Buffer)
      - io.Copy(os.Stdout)

  # Function length
  funlen:
    lines: 100
    statements: 50

  # Cognitive complexity
  gocognit:
    min-complexity: 15

  # Constants detection
  goconst:
    min-len: 2
    min-occurrences: 3
    ignore-tests: true

  # Criticality detection
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint

  # Formatting
  gofmt:
    simplify: true

  # Import formatting
  goimports:
    local-prefixes: repo.nusatek.id/sugeng/ottopay

  # Line length
  lll:
    line-length: 120

  # Misspelling
  misspell:
    locale: US

  # Naming conventions
  revive:
    rules:
      - name: exported
        arguments: [checkPrivateReceivers, sayRepetitiveInsteadOfStutters]
      - name: unreachable-code
      - name: unused-parameter
      - name: unused-receiver
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: if-return
      - name: increment-decrement
      - name: var-naming
      - name: var-declaration
      - name: package-comments
      - name: range
      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: empty-block
      - name: superfluous-else
      - name: unused-parameter
      - name: unreachable-code
      - name: redefines-builtin-id

  # Unused code
  unused:
    check-exported: false

  # Whitespace
  wsl:
    strict-append: true
    allow-assign-and-call: true
    allow-multiline-assign: true
    allow-cuddle-declarations: false
    allow-trailing-comment: false
    force-case-trailing-whitespace: 0
    force-err-cuddling: false
    allow-separated-leading-comment: false

linters:
  disable-all: true
  enable:
    # Enabled by default
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused

    # Additional linters
    - asciicheck
    - bodyclose
    - cyclop
    - dupl
    - durationcheck
    - errorlint
    - exhaustive
    - exportloopref
    - forbidigo
    - funlen
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gofmt
    - goimports
    - gomnd
    - goprintffuncname
    - gosec
    - lll
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    - nlreturn
    - noctx
    - nolintlint
    - prealloc
    - predeclared
    - revive
    - rowserrcheck
    - sqlclosecheck
    - thelper
    - tparallel
    - unconvert
    - unparam
    - wastedassign
    - whitespace
    - wsl

issues:
  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - gomnd
        - funlen
        - gocognit
        - cyclop
        - lll
        - dupl

    # Exclude some linters from running on mock files
    - path: mock.*\.go
      linters:
        - gomnd
        - funlen
        - gocognit
        - cyclop
        - lll
        - dupl
        - unused

    # Exclude some linters from running on generated files
    - path: .*\.pb\.go
      linters:
        - all

    # Exclude magic number detection in configuration files
    - path: config.*\.go
      linters:
        - gomnd

    # Exclude line length in long string literals
    - linters:
        - lll
      source: "^//go:embed"

  exclude:
    # Exclude common false positives
    - "Error return value of .((os\\.)?std(out|err)\\..*|.*Close|.*Flush|os\\.Remove(All)?|.*print(f|ln)?|os\\.(Un)?Setenv). is not checked"
    - "exported (type|method|function) .* should have comment or be unexported"
    - "ST1000: at least one file in a package should have a package comment"

  max-issues-per-linter: 0
  max-same-issues: 0
  new: false

severity:
  default-severity: error
  case-sensitive: false
  rules:
    - linters:
        - dupl
      severity: info
    - linters:
        - gomnd
      severity: info
