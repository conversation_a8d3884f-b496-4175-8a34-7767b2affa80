# OttoPay Virtual Account Non Billing Integration
# Environment Configuration Example

# Application Configuration
APP_NAME=ottopay
APP_VERSION=1.0.0
APP_ENV=development
APP_DEBUG=true

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=60s

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILE_PATH=/app/logs/ottopay.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true

# Database Configuration
DATABASE_DRIVER=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=ottopay
DATABASE_USER=ottopay
DATABASE_PASSWORD=password
DATABASE_SSL_MODE=disable
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=5m

# Alternative: Database URL
# DATABASE_URL=postgres://ottopay:password@localhost:5432/ottopay?sslmode=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5

# Alternative: Redis URL
# REDIS_URL=redis://localhost:6379

# OttoPay API Configuration
OTTOPAY_API_URL=https://api.ottopay.example.com
OTTOPAY_USERNAME=your_username
OTTOPAY_PASSWORD=your_password
OTTOPAY_API_KEY=your_api_key
OTTOPAY_TIMEOUT=30s
OTTOPAY_MAX_RETRIES=3
OTTOPAY_RETRY_DELAY=1s

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=24h
JWT_REFRESH_EXPIRATION=168h
TOKEN_CLEANUP_INTERVAL=1h

# Encryption Configuration
ENCRYPTION_KEY=your-32-character-encryption-key
ENCRYPTION_ALGORITHM=AES-256-GCM

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# CORS Configuration
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=86400

# Security Configuration
SECURITY_BCRYPT_COST=12
SECURITY_SESSION_TIMEOUT=30m
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=15m

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PATH=/metrics
METRICS_PORT=9090

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_INTERVAL=30s

# Tracing Configuration
TRACING_ENABLED=false
TRACING_JAEGER_ENDPOINT=http://localhost:14268/api/traces
TRACING_SERVICE_NAME=ottopay
TRACING_SAMPLE_RATE=0.1

# Audit Configuration
AUDIT_ENABLED=true
AUDIT_LOG_LEVEL=info
AUDIT_RETENTION_DAYS=90

# Cache Configuration
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=1h
CACHE_TOKEN_TTL=24h
CACHE_CUSTOMER_TTL=5m

# Validation Configuration
VALIDATION_STRICT_MODE=true
VALIDATION_MAX_REQUEST_SIZE=1MB

# Development Configuration
DEV_MODE=true
DEV_AUTO_MIGRATE=true
DEV_SEED_DATA=false

# Testing Configuration
TEST_DATABASE_URL=postgres://ottopay:password@localhost:5432/ottopay_test?sslmode=disable
TEST_REDIS_URL=redis://localhost:6379/1

# Production Configuration (uncomment for production)
# APP_ENV=production
# APP_DEBUG=false
# LOG_LEVEL=warn
# DATABASE_SSL_MODE=require
# RATE_LIMIT_REQUESTS_PER_MINUTE=30
# SECURITY_BCRYPT_COST=14
# TRACING_ENABLED=true
# DEV_MODE=false

# SSL/TLS Configuration (for production)
# TLS_ENABLED=true
# TLS_CERT_FILE=/etc/ssl/certs/ottopay.crt
# TLS_KEY_FILE=/etc/ssl/private/ottopay.key
# TLS_MIN_VERSION=1.2

# Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_S3_BUCKET=ottopay-backups
# BACKUP_S3_REGION=us-east-1

# Notification Configuration
# NOTIFICATION_ENABLED=false
# NOTIFICATION_EMAIL_SMTP_HOST=smtp.example.com
# NOTIFICATION_EMAIL_SMTP_PORT=587
# NOTIFICATION_EMAIL_USERNAME=<EMAIL>
# NOTIFICATION_EMAIL_PASSWORD=password
# NOTIFICATION_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# External Service Configuration
# EXTERNAL_SERVICE_TIMEOUT=10s
# EXTERNAL_SERVICE_MAX_RETRIES=3
# EXTERNAL_SERVICE_CIRCUIT_BREAKER_ENABLED=true

# Feature Flags
# FEATURE_NEW_PAYMENT_FLOW=false
# FEATURE_ENHANCED_LOGGING=true
# FEATURE_ASYNC_PROCESSING=false
