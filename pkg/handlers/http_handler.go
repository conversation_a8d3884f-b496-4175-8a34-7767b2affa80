package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"repo.nusatek.id/sugeng/ottopay/pkg/ottopay"
)

// HTTPHandler provides HTTP handlers for OttoPay operations
type HTTPHandler struct {
	client ottopay.Client
	logger ottopay.Logger
}

// NewHTTPHandler creates a new HTTP handler
func NewHTTPHandler(client ottopay.Client, logger ottopay.Logger) *HTTPHandler {
	if logger == nil {
		logger = &ottopay.NoOpLogger{}
	}

	return &HTTPHandler{
		client: client,
		logger: logger,
	}
}

// TokenHandler handles token acquisition requests
func (h *HTTPHandler) TokenHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	var req ottopay.GetTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeError(w, http.StatusBadRequest, "Invalid request body", "")
		return
	}

	// Add request metadata
	req.SourceIP = h.getClientIP(r)
	req.UserAgent = r.UserAgent()
	req.ChannelType = ottopay.ChannelTypeAPI

	response, err := h.client.GetToken(r.Context(), &req)
	if err != nil {
		h.logger.Errorf("Token request failed: %v", err)
		h.writeError(w, http.StatusInternalServerError, "Token request failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// InquiryHandler handles customer inquiry requests
func (h *HTTPHandler) InquiryHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	var req ottopay.InquiryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeError(w, http.StatusBadRequest, "Invalid request body", "")
		return
	}

	// Add request metadata
	req.SourceIP = h.getClientIP(r)
	req.UserAgent = r.UserAgent()

	// Get token from header if not in body
	if req.Token == "" {
		req.Token = h.getTokenFromHeader(r)
	}

	if req.Token == "" {
		h.writeError(w, http.StatusUnauthorized, "Authentication token required", "")
		return
	}

	response, err := h.client.Inquiry(r.Context(), &req)
	if err != nil {
		h.logger.Errorf("Inquiry request failed: %v", err)
		h.writeError(w, http.StatusInternalServerError, "Inquiry request failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// PaymentHandler handles payment processing requests
func (h *HTTPHandler) PaymentHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	var req ottopay.PaymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeError(w, http.StatusBadRequest, "Invalid request body", "")
		return
	}

	// Add request metadata
	req.SourceIP = h.getClientIP(r)
	req.UserAgent = r.UserAgent()

	// Get token from header if not in body
	if req.Token == "" {
		req.Token = h.getTokenFromHeader(r)
	}

	if req.Token == "" {
		h.writeError(w, http.StatusUnauthorized, "Authentication token required", "")
		return
	}

	response, err := h.client.Payment(r.Context(), &req)
	if err != nil {
		h.logger.Errorf("Payment request failed: %v", err)
		h.writeError(w, http.StatusInternalServerError, "Payment request failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// HealthHandler handles health check requests
func (h *HTTPHandler) HealthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	response, err := h.client.HealthCheck(r.Context())
	if err != nil {
		h.logger.Errorf("Health check failed: %v", err)
		h.writeError(w, http.StatusServiceUnavailable, "Health check failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// ValidateTokenHandler handles token validation requests
func (h *HTTPHandler) ValidateTokenHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	var req ottopay.ValidateTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeError(w, http.StatusBadRequest, "Invalid request body", "")
		return
	}

	// Add request metadata
	req.SourceIP = h.getClientIP(r)
	req.UserAgent = r.UserAgent()
	req.ChannelType = ottopay.ChannelTypeAPI
	req.Operation = "validate"

	response, err := h.client.ValidateToken(r.Context(), &req)
	if err != nil {
		h.logger.Errorf("Token validation failed: %v", err)
		h.writeError(w, http.StatusInternalServerError, "Token validation failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// RefreshTokenHandler handles token refresh requests
func (h *HTTPHandler) RefreshTokenHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeError(w, http.StatusMethodNotAllowed, "Method not allowed", "")
		return
	}

	var req ottopay.RefreshTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeError(w, http.StatusBadRequest, "Invalid request body", "")
		return
	}

	// Add request metadata
	req.SourceIP = h.getClientIP(r)
	req.UserAgent = r.UserAgent()
	req.ChannelType = ottopay.ChannelTypeAPI

	response, err := h.client.RefreshToken(r.Context(), &req)
	if err != nil {
		h.logger.Errorf("Token refresh failed: %v", err)
		h.writeError(w, http.StatusInternalServerError, "Token refresh failed", "")
		return
	}

	h.writeJSON(w, http.StatusOK, response)
}

// Helper methods

// writeJSON writes a JSON response
func (h *HTTPHandler) writeJSON(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.Errorf("Failed to encode JSON response: %v", err)
	}
}

// writeError writes an error response
func (h *HTTPHandler) writeError(w http.ResponseWriter, statusCode int, message, requestID string) {
	errorResponse := ottopay.ErrorResponse{
		Error:     http.StatusText(statusCode),
		Message:   message,
		RequestID: requestID,
		Timestamp: time.Now(),
	}

	h.writeJSON(w, statusCode, errorResponse)
}

// getClientIP extracts the client IP address from the request
func (h *HTTPHandler) getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fall back to RemoteAddr
	return r.RemoteAddr
}

// getTokenFromHeader extracts the authentication token from the request header
func (h *HTTPHandler) getTokenFromHeader(r *http.Request) string {
	// Check Authorization header
	if auth := r.Header.Get("Authorization"); auth != "" {
		// Support both "Bearer <token>" and "<token>" formats
		if len(auth) > 7 && auth[:7] == "Bearer " {
			return auth[7:]
		}
		return auth
	}

	// Check X-Auth-Token header
	return r.Header.Get("X-Auth-Token")
}

// SetupRoutes sets up HTTP routes for the handlers
func (h *HTTPHandler) SetupRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/token", h.TokenHandler)
	mux.HandleFunc("/token/validate", h.ValidateTokenHandler)
	mux.HandleFunc("/token/refresh", h.RefreshTokenHandler)
	mux.HandleFunc("/inquiry", h.InquiryHandler)
	mux.HandleFunc("/payment", h.PaymentHandler)
	mux.HandleFunc("/health", h.HealthHandler)
}

// Middleware

// LoggingMiddleware logs HTTP requests
func (h *HTTPHandler) LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Create a response writer wrapper to capture status code
		wrapper := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		next.ServeHTTP(wrapper, r)

		duration := time.Since(start)
		h.logger.Infof("%s %s %d %v", r.Method, r.URL.Path, wrapper.statusCode, duration)
	})
}

// CORSMiddleware adds CORS headers
func (h *HTTPHandler) CORSMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Auth-Token")

		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}
