package ottopay

import (
	"errors"
	"fmt"
	"time"
)

// Error codes
const (
	// Authentication errors
	ErrorCodeInvalidCredentials   = "INVALID_CREDENTIALS"
	ErrorCodeTokenExpired         = "TOKEN_EXPIRED"
	ErrorCodeTokenInvalid         = "TOKEN_INVALID"
	ErrorCodeTokenNotFound        = "TOKEN_NOT_FOUND"
	ErrorCodeAuthenticationFailed = "AUTHENTICATION_FAILED"

	// Validation errors
	ErrorCodeInvalidRequest   = "INVALID_REQUEST"
	ErrorCodeMissingField     = "MISSING_FIELD"
	ErrorCodeInvalidFormat    = "INVALID_FORMAT"
	ErrorCodeInvalidValue     = "INVALID_VALUE"
	ErrorCodeDuplicateRequest = "DUPLICATE_REQUEST"

	// Business logic errors
	ErrorCodeCustomerNotFound   = "CUSTOMER_NOT_FOUND"
	ErrorCodeInvalidBill        = "INVALID_BILL"
	ErrorCodePaidBill           = "PAID_BILL"
	ErrorCodeInvalidAmount      = "INVALID_AMOUNT"
	ErrorCodeInsufficientFunds  = "INSUFFICIENT_FUNDS"
	ErrorCodePaymentFailed      = "PAYMENT_FAILED"
	ErrorCodePaymentTimeout     = "PAYMENT_TIMEOUT"
	ErrorCodeTransactionExpired = "TRANSACTION_EXPIRED"

	// System errors
	ErrorCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	ErrorCodeInternalError      = "INTERNAL_ERROR"
	ErrorCodeNetworkError       = "NETWORK_ERROR"
	ErrorCodeTimeoutError       = "TIMEOUT_ERROR"
	ErrorCodeRateLimited        = "RATE_LIMITED"

	// Configuration errors
	ErrorCodeInvalidConfiguration = "INVALID_CONFIGURATION"
	ErrorCodeMissingConfiguration = "MISSING_CONFIGURATION"
)

// OttoPayError represents an error from the OttoPay client
type OttoPayError struct {
	Code      string            `json:"code"`
	Message   string            `json:"message"`
	Details   map[string]string `json:"details,omitempty"`
	RequestID string            `json:"request_id,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
	Cause     error             `json:"-"`
}

// Error implements the error interface
func (e *OttoPayError) Error() string {
	if e.RequestID != "" {
		return fmt.Sprintf("[%s] %s: %s (RequestID: %s)", e.Code, e.Message, e.getDetailsString(), e.RequestID)
	}
	return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.getDetailsString())
}

// Unwrap returns the underlying error
func (e *OttoPayError) Unwrap() error {
	return e.Cause
}

// getDetailsString returns a string representation of the error details
func (e *OttoPayError) getDetailsString() string {
	if len(e.Details) == 0 {
		return ""
	}

	var details string
	for key, value := range e.Details {
		if details != "" {
			details += ", "
		}
		details += fmt.Sprintf("%s=%s", key, value)
	}
	return details
}

// NewOttoPayError creates a new OttoPay error
func NewOttoPayError(code, message string) *OttoPayError {
	return &OttoPayError{
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		Details:   make(map[string]string),
	}
}

// NewOttoPayErrorWithCause creates a new OttoPay error with an underlying cause
func NewOttoPayErrorWithCause(code, message string, cause error) *OttoPayError {
	return &OttoPayError{
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		Details:   make(map[string]string),
		Cause:     cause,
	}
}

// WithDetail adds a detail to the error
func (e *OttoPayError) WithDetail(key, value string) *OttoPayError {
	if e.Details == nil {
		e.Details = make(map[string]string)
	}
	e.Details[key] = value
	return e
}

// WithRequestID sets the request ID for the error
func (e *OttoPayError) WithRequestID(requestID string) *OttoPayError {
	e.RequestID = requestID
	return e
}

// IsCode checks if the error has a specific code
func (e *OttoPayError) IsCode(code string) bool {
	return e.Code == code
}

// IsAuthenticationError checks if the error is an authentication error
func (e *OttoPayError) IsAuthenticationError() bool {
	authCodes := []string{
		ErrorCodeInvalidCredentials,
		ErrorCodeTokenExpired,
		ErrorCodeTokenInvalid,
		ErrorCodeTokenNotFound,
		ErrorCodeAuthenticationFailed,
	}

	for _, code := range authCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// IsValidationError checks if the error is a validation error
func (e *OttoPayError) IsValidationError() bool {
	validationCodes := []string{
		ErrorCodeInvalidRequest,
		ErrorCodeMissingField,
		ErrorCodeInvalidFormat,
		ErrorCodeInvalidValue,
		ErrorCodeDuplicateRequest,
	}

	for _, code := range validationCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// IsBusinessError checks if the error is a business logic error
func (e *OttoPayError) IsBusinessError() bool {
	businessCodes := []string{
		ErrorCodeCustomerNotFound,
		ErrorCodeInvalidBill,
		ErrorCodePaidBill,
		ErrorCodeInvalidAmount,
		ErrorCodeInsufficientFunds,
		ErrorCodePaymentFailed,
		ErrorCodePaymentTimeout,
		ErrorCodeTransactionExpired,
	}

	for _, code := range businessCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// IsSystemError checks if the error is a system error
func (e *OttoPayError) IsSystemError() bool {
	systemCodes := []string{
		ErrorCodeServiceUnavailable,
		ErrorCodeInternalError,
		ErrorCodeNetworkError,
		ErrorCodeTimeoutError,
		ErrorCodeRateLimited,
	}

	for _, code := range systemCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// IsRetryable checks if the error indicates a retryable condition
func (e *OttoPayError) IsRetryable() bool {
	retryableCodes := []string{
		ErrorCodeServiceUnavailable,
		ErrorCodeNetworkError,
		ErrorCodeTimeoutError,
		ErrorCodeInternalError,
	}

	for _, code := range retryableCodes {
		if e.Code == code {
			return true
		}
	}
	return false
}

// Predefined errors
var (
	ErrInvalidCredentials = NewOttoPayError(ErrorCodeInvalidCredentials, "Invalid username or password")
	ErrTokenExpired       = NewOttoPayError(ErrorCodeTokenExpired, "Authentication token has expired")
	ErrTokenInvalid       = NewOttoPayError(ErrorCodeTokenInvalid, "Authentication token is invalid")
	ErrTokenNotFound      = NewOttoPayError(ErrorCodeTokenNotFound, "Authentication token not found")
	ErrInvalidRequest     = NewOttoPayError(ErrorCodeInvalidRequest, "Invalid request")
	ErrCustomerNotFound   = NewOttoPayError(ErrorCodeCustomerNotFound, "Customer not found")
	ErrInvalidBill        = NewOttoPayError(ErrorCodeInvalidBill, "Invalid bill")
	ErrPaidBill           = NewOttoPayError(ErrorCodePaidBill, "Bill has already been paid")
	ErrPaymentFailed      = NewOttoPayError(ErrorCodePaymentFailed, "Payment processing failed")
	ErrServiceUnavailable = NewOttoPayError(ErrorCodeServiceUnavailable, "Service is currently unavailable")
	ErrInternalError      = NewOttoPayError(ErrorCodeInternalError, "Internal server error")
)

// Error helper functions

// IsOttoPayError checks if an error is an OttoPayError
func IsOttoPayError(err error) bool {
	var ottoPayErr *OttoPayError
	return errors.As(err, &ottoPayErr)
}

// GetOttoPayError extracts an OttoPayError from an error
func GetOttoPayError(err error) *OttoPayError {
	var ottoPayErr *OttoPayError
	if errors.As(err, &ottoPayErr) {
		return ottoPayErr
	}
	return nil
}

// WrapError wraps a generic error as an OttoPayError
func WrapError(err error, code, message string) *OttoPayError {
	if err == nil {
		return nil
	}

	// If it's already an OttoPayError, return it
	if ottoPayErr := GetOttoPayError(err); ottoPayErr != nil {
		return ottoPayErr
	}

	return NewOttoPayErrorWithCause(code, message, err)
}

// ErrorFromResponse creates an error from an HTTP response
func ErrorFromResponse(statusCode int, body []byte, requestID string) *OttoPayError {
	var code, message string

	switch statusCode {
	case 400:
		code = ErrorCodeInvalidRequest
		message = "Bad request"
	case 401:
		code = ErrorCodeAuthenticationFailed
		message = "Authentication failed"
	case 403:
		code = ErrorCodeTokenInvalid
		message = "Access forbidden"
	case 404:
		code = ErrorCodeCustomerNotFound
		message = "Resource not found"
	case 408:
		code = ErrorCodeTimeoutError
		message = "Request timeout"
	case 429:
		code = ErrorCodeRateLimited
		message = "Rate limit exceeded"
	case 500:
		code = ErrorCodeInternalError
		message = "Internal server error"
	case 502, 503:
		code = ErrorCodeServiceUnavailable
		message = "Service unavailable"
	case 504:
		code = ErrorCodeTimeoutError
		message = "Gateway timeout"
	default:
		code = ErrorCodeInternalError
		message = fmt.Sprintf("HTTP %d error", statusCode)
	}

	err := NewOttoPayError(code, message)
	if requestID != "" {
		err.WithRequestID(requestID)
	}

	if len(body) > 0 {
		err.WithDetail("response_body", string(body))
	}

	return err
}

// ValidationError creates a validation error
func ValidationError(field, message string) *OttoPayError {
	return NewOttoPayError(ErrorCodeInvalidValue, message).
		WithDetail("field", field)
}

// BusinessError creates a business logic error
func BusinessError(code, message string) *OttoPayError {
	return NewOttoPayError(code, message)
}

// SystemError creates a system error
func SystemError(message string, cause error) *OttoPayError {
	return NewOttoPayErrorWithCause(ErrorCodeInternalError, message, cause)
}

// NetworkError creates a network error
func NetworkError(message string, cause error) *OttoPayError {
	return NewOttoPayErrorWithCause(ErrorCodeNetworkError, message, cause)
}

// TimeoutError creates a timeout error
func TimeoutError(message string) *OttoPayError {
	return NewOttoPayError(ErrorCodeTimeoutError, message)
}
