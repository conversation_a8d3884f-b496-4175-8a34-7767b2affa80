package ottopay

import (
	"context"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/domain/services"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/external"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/http"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/auth"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/inquiry"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/payment"
)

// Client represents the main OttoPay client interface
type Client interface {
	// Authentication operations
	GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error)
	RefreshToken(ctx context.Context, req *RefreshTokenRequest) (*RefreshTokenResponse, error)
	ValidateToken(ctx context.Context, req *ValidateTokenRequest) (*ValidateTokenResponse, error)

	// Customer inquiry operations
	Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error)

	// Payment operations
	Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)

	// Health check
	HealthCheck(ctx context.Context) (*HealthResponse, error)
}

// client is the concrete implementation of the Client interface
type client struct {
	// Use cases
	getTokenUseCase        *auth.GetTokenUseCase
	refreshTokenUseCase    *auth.RefreshTokenUseCase
	validateTokenUseCase   *auth.ValidateTokenUseCase
	customerInquiryUseCase *inquiry.CustomerInquiryUseCase
	processPaymentUseCase  *payment.ProcessPaymentUseCase

	// Configuration
	config *Config
	logger Logger
}

// Config holds configuration for the OttoPay client
type Config struct {
	BaseURL    string
	APIKey     string
	Username   string
	Password   string
	Timeout    time.Duration
	HTTPClient http.HTTPClient
	Logger     Logger
}

// Option defines a functional option for configuring the client
type Option func(*Config)

// WithBaseURL sets the base URL for the API
func WithBaseURL(baseURL string) Option {
	return func(c *Config) {
		c.BaseURL = baseURL
	}
}

// WithTimeout sets the HTTP client timeout
func WithTimeout(timeout time.Duration) Option {
	return func(c *Config) {
		c.Timeout = timeout
	}
}

// WithHTTPClient sets a custom HTTP client
func WithHTTPClient(httpClient http.HTTPClient) Option {
	return func(c *Config) {
		c.HTTPClient = httpClient
	}
}

// WithLogger sets a custom logger
func WithLogger(logger Logger) Option {
	return func(c *Config) {
		c.Logger = logger
	}
}

// NewClient creates a new OttoPay client with the provided options
func NewClient(apiKey, username, password string, opts ...Option) Client {
	config := &Config{
		APIKey:   apiKey,
		Username: username,
		Password: password,
		Timeout:  30 * time.Second,
		Logger:   &NoOpLogger{},
	}

	for _, opt := range opts {
		opt(config)
	}

	// Set up dependencies
	logger := config.Logger

	// Create repositories
	tokenRepo := newMemoryTokenRepository()
	var tokenCacheRepo repositories.TokenCacheRepository // Could be Redis in production
	var auditRepo repositories.AuditRepository           // Could be database in production
	var paymentRepo repositories.PaymentRepository       // Could be database in production

	// Create services
	validationService := services.NewValidationService()
	var encryptionService services.EncryptionService // Would be implemented

	// Create OttoPay API adapter
	apiConfig := &external.OttoPayAPIConfig{
		BaseURL:  config.BaseURL,
		APIKey:   config.APIKey,
		Username: config.Username,
		Password: config.Password,
		Timeout:  config.Timeout,
		Logger:   logger,
	}
	ottoPayService := external.NewOttoPayAPIAdapter(apiConfig)

	// Create use cases
	getTokenUseCase := auth.NewGetTokenUseCase(
		tokenRepo,
		tokenCacheRepo,
		ottoPayService,
		validationService,
		encryptionService,
		auditRepo,
	)

	refreshTokenUseCase := auth.NewRefreshTokenUseCase(
		tokenRepo,
		tokenCacheRepo,
		ottoPayService,
		validationService,
		encryptionService,
		auditRepo,
	)

	validateTokenUseCase := auth.NewValidateTokenUseCase(
		tokenRepo,
		tokenCacheRepo,
		validationService,
		auditRepo,
	)

	customerInquiryUseCase := inquiry.NewCustomerInquiryUseCase(
		ottoPayService,
		validationService,
		auditRepo,
		tokenRepo,
	)

	processPaymentUseCase := payment.NewProcessPaymentUseCase(
		paymentRepo,
		ottoPayService,
		validationService,
		auditRepo,
		tokenRepo,
	)

	return &client{
		getTokenUseCase:        getTokenUseCase,
		refreshTokenUseCase:    refreshTokenUseCase,
		validateTokenUseCase:   validateTokenUseCase,
		customerInquiryUseCase: customerInquiryUseCase,
		processPaymentUseCase:  processPaymentUseCase,
		config:                 config,
		logger:                 logger,
	}
}

// GetToken implements the Client interface
func (c *client) GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error) {
	if req == nil {
		req = &GetTokenRequest{
			Username: c.config.Username,
			Password: c.config.Password,
		}
	}

	useCaseReq := &auth.GetTokenRequest{
		Username:    req.Username,
		Password:    req.Password,
		SourceIP:    req.SourceIP,
		UserAgent:   req.UserAgent,
		ChannelType: req.ChannelType,
	}

	result, err := c.getTokenUseCase.Execute(ctx, useCaseReq)
	if err != nil {
		return nil, err
	}

	return &GetTokenResponse{
		Token:     result.Token,
		ExpiresIn: result.ExpiresIn,
		Success:   result.Success,
		Message:   result.Message,
		RequestID: result.RequestID,
	}, nil
}

// RefreshToken implements the Client interface
func (c *client) RefreshToken(ctx context.Context, req *RefreshTokenRequest) (*RefreshTokenResponse, error) {
	useCaseReq := &auth.RefreshTokenRequest{
		Token:       req.Token,
		SourceIP:    req.SourceIP,
		UserAgent:   req.UserAgent,
		ChannelType: req.ChannelType,
	}

	result, err := c.refreshTokenUseCase.Execute(ctx, useCaseReq)
	if err != nil {
		return nil, err
	}

	return &RefreshTokenResponse{
		Token:     result.Token,
		ExpiresIn: result.ExpiresIn,
		Success:   result.Success,
		Message:   result.Message,
		RequestID: result.RequestID,
	}, nil
}

// ValidateToken implements the Client interface
func (c *client) ValidateToken(ctx context.Context, req *ValidateTokenRequest) (*ValidateTokenResponse, error) {
	useCaseReq := &auth.ValidateTokenRequest{
		Token:       req.Token,
		SourceIP:    req.SourceIP,
		UserAgent:   req.UserAgent,
		ChannelType: req.ChannelType,
		Operation:   req.Operation,
	}

	result, err := c.validateTokenUseCase.Execute(ctx, useCaseReq)
	if err != nil {
		return nil, err
	}

	return &ValidateTokenResponse{
		Valid:     result.Valid,
		Token:     result.Token,
		ExpiresIn: result.ExpiresIn,
		Reason:    result.Reason,
		Username:  result.Username,
		TokenID:   result.TokenID,
		RequestID: result.RequestID,
	}, nil
}

// Inquiry implements the Client interface
func (c *client) Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error) {
	useCaseReq := &inquiry.CustomerInquiryRequest{
		CompanyCode:     req.CompanyCode,
		CustomerNumber:  req.CustomerNumber,
		RequestID:       req.RequestID,
		ChannelType:     req.ChannelType,
		TransactionDate: req.TransactionDate,
		AdditionalData:  req.AdditionalData,
		Token:           req.Token,
		SourceIP:        req.SourceIP,
		UserAgent:       req.UserAgent,
	}

	result, err := c.customerInquiryUseCase.Execute(ctx, useCaseReq)
	if err != nil {
		return nil, err
	}

	return &InquiryResponse{
		Success:       result.Success,
		Customer:      result.Customer,
		InquiryStatus: result.InquiryStatus,
		InquiryReason: result.InquiryReason,
		Message:       result.Message,
		ResponseTime:  result.ResponseTime,
		RequestID:     result.RequestID,
		TransactionID: result.TransactionID,
	}, nil
}

// Payment implements the Client interface
func (c *client) Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	useCaseReq := &payment.ProcessPaymentRequest{
		CompanyCode:     req.CompanyCode,
		CustomerNumber:  req.CustomerNumber,
		RequestID:       req.RequestID,
		ChannelType:     req.ChannelType,
		CustomerName:    req.CustomerName,
		CurrencyCode:    req.CurrencyCode,
		PaidAmount:      req.PaidAmount,
		TotalAmount:     req.TotalAmount,
		Reference:       req.Reference,
		SubCompany:      req.SubCompany,
		TransactionDate: req.TransactionDate,
		DetailBills:     req.DetailBills,
		AdditionalData:  req.AdditionalData,
		Token:           req.Token,
		SourceIP:        req.SourceIP,
		UserAgent:       req.UserAgent,
	}

	result, err := c.processPaymentUseCase.Execute(ctx, useCaseReq)
	if err != nil {
		return nil, err
	}

	return &PaymentResponse{
		Success:       result.Success,
		Payment:       result.Payment,
		Status:        result.Status,
		Reason:        result.Reason,
		TransactionID: result.TransactionID,
		Message:       result.Message,
		ResponseTime:  result.ResponseTime,
		RequestID:     result.RequestID,
	}, nil
}

// HealthCheck implements the Client interface
func (c *client) HealthCheck(ctx context.Context) (*HealthResponse, error) {
	// Simple health check implementation
	return &HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "1.0.0",
		Services: map[string]string{
			"ottopay_api": "available",
		},
	}, nil
}

// newMemoryTokenRepository creates a simple in-memory token repository
// This is a placeholder implementation for the public API
func newMemoryTokenRepository() repositories.TokenRepository {
	// For now, return nil - this would be replaced with actual implementation
	return nil
}
