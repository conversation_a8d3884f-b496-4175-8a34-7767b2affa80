# OttoPay Virtual Account Non Billing v1.1.2 Compliance

This document outlines how our implementation complies with the OttoPay Virtual Account Non Billing v1.1.2 specification.

## Key Requirements Compliance

### 1. Amount Field Formatting ✅

**Requirement**: 2 last digits for TotalAmount and PaidAmount field should be decimal point.

**Implementation**: 
- `internal/domain/valueobjects/amount.go` - `String()` method formats amounts with correct decimal places
- `internal/domain/valueobjects/currency.go` - `GetDecimalPlaces()` returns 2 for both IDR and USD
- Example: 100000.50 is formatted as "100000.50"

**Code Location**: 
```go
// String returns the string representation of the amount
func (a Amount) String() string {
    decimalPlaces := a.currency.GetDecimalPlaces()
    format := fmt.Sprintf("%%.%df", decimalPlaces)
    return fmt.Sprintf(format, a.value)
}
```

### 2. PaymentFlagStatus Codes ✅

**Requirement**: PaymentFlagStatus field values:
- 00 = Success payment flag
- 01 = Reject payment flag by co-partner
- 02 = Payment Timeout
- Other values considered as 01

**Implementation**: 
- `internal/infrastructure/external/ottopay_api_adapter.go` - Correct mapping in `ProcessPayment()`

**Code Location**:
```go
switch apiResponse.PaymentFlagStatus {
case "00":
    status = entities.PaymentStatusSuccess
    success = true
case "01":
    status = entities.PaymentStatusFailed
    success = false
case "02":
    status = entities.PaymentStatusTimeout
    success = false
default:
    status = entities.PaymentStatusFailed
    success = false
}
```

### 3. Currency Code Support ✅

**Requirement**: CurrencyCode field may vary with these values:
- IDR
- USD

**Implementation**: 
- `internal/domain/valueobjects/currency.go` - Supports IDR and USD currencies
- Both currencies configured with 2 decimal places

**Code Location**:
```go
const (
    CurrencyIDR CurrencyCode = "IDR"
    CurrencyUSD CurrencyCode = "USD"
)

func (c CurrencyCode) GetDecimalPlaces() int {
    switch c {
    case CurrencyIDR:
        return 2
    case CurrencyUSD:
        return 2
    default:
        return 2
    }
}
```

### 4. Request ID Uniqueness ✅

**Requirement**: RequestID is unique from Bank for each transaction.

**Implementation**: 
- `internal/domain/valueobjects/request_id.go` - Generates unique request IDs
- Separate generation for inquiry and payment requests

**Code Location**:
```go
func GenerateInquiryRequestID() (RequestID, error) {
    return generateRequestID("INQ")
}

func GeneratePaymentRequestID() (RequestID, error) {
    return generateRequestID("PAY")
}
```

### 5. Amount Consistency ✅

**Requirement**: 
- TotalAmount field value will be same with TotalAmount field value returned from copartners when Bank do inquiry list of bills
- PaidAmount field value will be total amount paid by customer through Bank
- CurrencyCode must be same for TotalAmount and PaidAmount

**Implementation**: 
- `internal/domain/entities/payment.go` - Payment entity maintains amount consistency
- `internal/domain/valueobjects/amount.go` - Amount validation ensures currency consistency

### 6. PaymentFlagReason Multilingual Support ✅

**Requirement**: PaymentFlagReason field value in two languages, Indonesian and English.

**Implementation**: 
- `internal/domain/entities/payment.go` - PaymentFlagReason struct with Indonesian and English fields

**Code Location**:
```go
type PaymentFlagReason struct {
    Indonesian string `json:"indonesian"`
    English    string `json:"english"`
}
```

### 7. Payment Reversal Logic ✅

**Requirement**: Only response with PaymentFlagStatus "01" will cause customer's payment to be reversed by the bank.

**Implementation**: 
- `internal/infrastructure/external/ottopay_api_adapter.go` - Correctly handles status "01" as failed payment
- Business logic distinguishes between different failure types

## API Endpoint Compliance

### Inquiry Endpoint ✅
- **Path**: `/inquiry`
- **Method**: POST
- **Request Fields**: CompanyCode, CustomerNumber, RequestID, ChannelType
- **Response Fields**: InquiryStatus, CustomerName, TotalAmount, CurrencyCode
- **Status Codes**: "00" (success), "01" (failed)

### Payment Endpoint ✅
- **Path**: `/payment`
- **Method**: POST
- **Request Fields**: CompanyCode, CustomerNumber, RequestID, CustomerName, CurrencyCode, PaidAmount, TotalAmount, Reference, ChannelType
- **Response Fields**: PaymentFlagStatus, PaymentFlagReason, PaidAmount, TotalAmount, CurrencyCode
- **Status Codes**: "00" (success), "01" (failed), "02" (timeout)

## Data Validation ✅

### Amount Validation
- Maximum 15 characters including decimal point
- Exactly 2 decimal places for IDR and USD
- Positive values only
- Proper currency code validation

### String Field Validation
- CompanyCode: 5 characters
- CustomerNumber: 10 characters
- Reference: Maximum 255 characters
- AdditionalData: Maximum 255 characters

### Request ID Validation
- Unique generation per transaction
- Proper format validation
- Separate prefixes for inquiry (INQ) and payment (PAY)

## Error Handling ✅

### Comprehensive Error Types
- Domain validation errors
- Service communication errors
- Repository operation errors
- Authentication and authorization errors

### Proper Error Responses
- Structured error messages
- Multilingual error reasons
- Appropriate HTTP status codes
- Detailed logging for debugging

## Security Compliance ✅

### Authentication
- Token-based authentication
- Secure token generation and validation
- Token expiration and refresh mechanisms

### Data Protection
- Input validation and sanitization
- Secure communication protocols
- Audit logging for all operations

## Testing Coverage ✅

### Unit Tests
- Value object validation tests
- Entity behavior tests
- Service logic tests
- Repository operation tests

### Integration Tests
- API endpoint tests
- Database operation tests
- External service integration tests

### Mock Infrastructure
- Complete mock implementations for testing
- Configurable test scenarios
- Comprehensive test fixtures

## Conclusion

Our implementation fully complies with the OttoPay Virtual Account Non Billing v1.1.2 specification. All key requirements regarding amount formatting, status codes, currency support, and business logic have been correctly implemented and tested.

The architecture follows clean architecture principles while maintaining strict compliance with the OttoPay API specification, ensuring reliable and maintainable integration with the OttoPay payment system.
