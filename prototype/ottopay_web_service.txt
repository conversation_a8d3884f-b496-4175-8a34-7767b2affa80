// Example: Web Service Integration with OttoPay VA
// This example shows how to integrate the OttoPay package into a web service
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
	"github.com/sirupsen/logrus"

	// Import the OttoPay package
	"repo.nusatek.id/sugeng/ottopay/pkg/ottopay"
)

// PaymentService handles virtual account payment operations
type PaymentService struct {
	ottoPayClient ottopay.Client
	logger        *logrus.Logger
}

// NewPaymentService creates a new payment service
func NewPaymentService(logger *logrus.Logger) (*PaymentService, error) {
	// Load configuration from environment variables
	apiKey := os.Getenv("OTTOPAY_API_KEY")
	username := os.Getenv("OTTOPAY_USERNAME")
	password := os.Getenv("OTTOPAY_PASSWORD")
	baseURL := os.Getenv("OTTOPAY_BASE_URL")

	if apiKey == "" || username == "" || password == "" {
		return nil, fmt.Errorf("missing required OttoPay credentials in environment")
	}

	if baseURL == "" {
		baseURL = "https://api.ottopay.id" // default
	}

	// Create OttoPay client with custom logger adapter
	ottoPayClient := ottopay.NewClient(
		apiKey,
		username,
		password,
		ottopay.WithBaseURL(baseURL),
		ottopay.WithTimeout(30*time.Second),
		ottopay.WithLogger(&LogrusAdapter{logger: logger}),
	)

	return &PaymentService{
		ottoPayClient: ottoPayClient,
		logger:        logger,
	}, nil
}

// LogrusAdapter adapts logrus.Logger to ottopay.Logger interface
type LogrusAdapter struct {
	logger *logrus.Logger
}

func (l *LogrusAdapter) Debugf(format string, args ...interface{}) {
	l.logger.Debugf(format, args...)
}

func (l *LogrusAdapter) Infof(format string, args ...interface{}) {
	l.logger.Infof(format, args...)
}

func (l *LogrusAdapter) Errorf(format string, args ...interface{}) {
	l.logger.Errorf(format, args...)
}

// Request/Response DTOs for the web service
type InquiryRequest struct {
	CompanyCode    string `json:"company_code" validate:"required,max=5"`
	CustomerNumber string `json:"customer_number" validate:"required,max=11"`
	ChannelType    string `json:"channel_type" validate:"required,max=50"`
}

type InquiryResponse struct {
	Success      bool   `json:"success"`
	CustomerName string `json:"customer_name,omitempty"`
	Amount       string `json:"amount,omitempty"`
	Currency     string `json:"currency,omitempty"`
	Message      string `json:"message,omitempty"`
	InquiryToken string `json:"inquiry_token,omitempty"` // For payment verification
}

type PaymentRequest struct {
	InquiryToken string `json:"inquiry_token" validate:"required"`
	Reference    string `json:"reference" validate:"required,max=255"`
	PaidAmount   string `json:"paid_amount" validate:"required,max=15"`
}

type PaymentResponse struct {
	Success       bool   `json:"success"`
	PaymentStatus string `json:"payment_status,omitempty"`
	TransactionID string `json:"transaction_id,omitempty"`
	Message       string `json:"message,omitempty"`
	PaidAmount    string `json:"paid_amount,omitempty"`
	Currency      string `json:"currency,omitempty"`
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// In-memory storage for inquiry tokens (use Redis/database in production)
type InquiryCache struct {
	inquiries map[string]*ottopay.InquiryResponse
}

func NewInquiryCache() *InquiryCache {
	return &InquiryCache{
		inquiries: make(map[string]*ottopay.InquiryResponse),
	}
}

func (c *InquiryCache) Store(token string, inquiry *ottopay.InquiryResponse) {
	c.inquiries[token] = inquiry
}

func (c *InquiryCache) Get(token string) (*ottopay.InquiryResponse, bool) {
	inquiry, exists := c.inquiries[token]
	return inquiry, exists
}

// Global cache (use proper dependency injection in production)
var inquiryCache = NewInquiryCache()

// HTTP Handlers

func (ps *PaymentService) handleInquiry(w http.ResponseWriter, r *http.Request) {
	var req InquiryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ps.respondError(w, http.StatusBadRequest, "Invalid JSON format", err.Error())
		return
	}

	// Generate unique request ID
	requestID := fmt.Sprintf("INQ-%d", time.Now().Unix())

	// Create OttoPay inquiry request
	inquiryReq := &ottopay.InquiryRequest{
		CompanyCode:     req.CompanyCode,
		CustomerNumber:  req.CustomerNumber,
		RequestID:       requestID,
		ChannelType:     req.ChannelType,
		TransactionDate: time.Now().Format("2006/01/02 15:04:05"),
	}

	// Validate request
	if err := ottopay.ValidateInquiryRequest(inquiryReq); err != nil {
		ps.respondError(w, http.StatusBadRequest, "Invalid inquiry request", err.Error())
		return
	}

	// Call OttoPay API
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	inquiryResp, err := ps.ottoPayClient.Inquiry(ctx, inquiryReq)
	if err != nil {
		ps.logger.WithError(err).Error("OttoPay inquiry failed")

		// Handle specific OttoPay errors
		if apiErr, ok := err.(*ottopay.Error); ok {
			ps.respondError(w, http.StatusBadGateway, "Payment gateway error",
				fmt.Sprintf("Code: %d, Message: %s", apiErr.Code, apiErr.Message))
			return
		}

		ps.respondError(w, http.StatusInternalServerError, "Inquiry service unavailable", err.Error())
		return
	}

	// Check inquiry success
	if !inquiryResp.IsInquirySuccess() {
		ps.respondJSON(w, http.StatusOK, InquiryResponse{
			Success: false,
			Message: inquiryResp.InquiryReason.English,
		})
		return
	}

	// Generate inquiry token for payment step
	inquiryToken := fmt.Sprintf("TOKEN-%d", time.Now().UnixNano())
	inquiryCache.Store(inquiryToken, inquiryResp)

	// Respond with success
	ps.respondJSON(w, http.StatusOK, InquiryResponse{
		Success:      true,
		CustomerName: inquiryResp.CustomerName,
		Amount:       inquiryResp.TotalAmount,
		Currency:     inquiryResp.CurrencyCode,
		Message:      "Customer found",
		InquiryToken: inquiryToken,
	})

	ps.logger.WithFields(logrus.Fields{
		"customer_number": req.CustomerNumber,
		"customer_name":   inquiryResp.CustomerName,
		"amount":          inquiryResp.TotalAmount,
	}).Info("Inquiry successful")
}

func (ps *PaymentService) handlePayment(w http.ResponseWriter, r *http.Request) {
	var req PaymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ps.respondError(w, http.StatusBadRequest, "Invalid JSON format", err.Error())
		return
	}

	// Retrieve inquiry data from cache
	inquiryResp, exists := inquiryCache.Get(req.InquiryToken)
	if !exists {
		ps.respondError(w, http.StatusBadRequest, "Invalid or expired inquiry token", "")
		return
	}

	// Generate unique payment request ID
	paymentRequestID := fmt.Sprintf("PAY-%d", time.Now().Unix())

	// Create OttoPay payment request
	paymentReq := &ottopay.PaymentRequest{
		CompanyCode:     inquiryResp.CompanyCode,
		CustomerNumber:  inquiryResp.CustomerNumber,
		RequestID:       paymentRequestID,
		ChannelType:     "WEB", // Web channel
		CustomerName:    inquiryResp.CustomerName,
		CurrencyCode:    inquiryResp.CurrencyCode,
		PaidAmount:      req.PaidAmount,
		TotalAmount:     inquiryResp.TotalAmount,
		Reference:       req.Reference,
		TransactionDate: time.Now().Format("2006/01/02 15:04:05"),
	}

	// Validate payment request
	if err := ottopay.ValidatePaymentRequest(paymentReq); err != nil {
		ps.respondError(w, http.StatusBadRequest, "Invalid payment request", err.Error())
		return
	}

	// Call OttoPay payment API
	ctx, cancel := context.WithTimeout(r.Context(), 45*time.Second)
	defer cancel()

	paymentResp, err := ps.ottoPayClient.Payment(ctx, paymentReq)
	if err != nil {
		ps.logger.WithError(err).Error("OttoPay payment failed")

		if apiErr, ok := err.(*ottopay.Error); ok {
			ps.respondError(w, http.StatusBadGateway, "Payment gateway error",
				fmt.Sprintf("Code: %d, Message: %s", apiErr.Code, apiErr.Message))
			return
		}

		ps.respondError(w, http.StatusInternalServerError, "Payment service unavailable", err.Error())
		return
	}

	// Process payment response
	response := PaymentResponse{
		TransactionID: paymentRequestID,
		PaidAmount:    paymentResp.PaidAmount,
		Currency:      paymentResp.CurrencyCode,
	}

	switch {
	case paymentResp.IsPaymentSuccess():
		response.Success = true
		response.PaymentStatus = "SUCCESS"
		response.Message = "Payment completed successfully"

		ps.logger.WithFields(logrus.Fields{
			"transaction_id":  paymentRequestID,
			"customer_number": paymentReq.CustomerNumber,
			"amount":          paymentResp.PaidAmount,
		}).Info("Payment successful")

	case paymentResp.IsPaymentFailed():
		response.Success = false
		response.PaymentStatus = "FAILED"
		response.Message = paymentResp.PaymentFlagReason.English

		ps.logger.WithFields(logrus.Fields{
			"transaction_id":  paymentRequestID,
			"customer_number": paymentReq.CustomerNumber,
			"reason":          paymentResp.PaymentFlagReason.English,
		}).Warn("Payment failed")

	case paymentResp.IsPaymentTimeout():
		response.Success = false
		response.PaymentStatus = "TIMEOUT"
		response.Message = "Payment processing timed out"

		ps.logger.WithFields(logrus.Fields{
			"transaction_id":  paymentRequestID,
			"customer_number": paymentReq.CustomerNumber,
		}).Warn("Payment timeout")

	default:
		response.Success = false
		response.PaymentStatus = "UNKNOWN"
		response.Message = "Unknown payment status"
	}

	ps.respondJSON(w, http.StatusOK, response)
}

func (ps *PaymentService) handleHealth(w http.ResponseWriter, r *http.Request) {
	// Test OttoPay connectivity
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	_, err := ps.ottoPayClient.GetToken(ctx, nil)

	health := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().ISO8601(),
		"service":   "ottopay-va-service",
	}

	if err != nil {
		health["status"] = "degraded"
		health["ottopay_status"] = "unavailable"
		health["error"] = err.Error()
		ps.respondJSON(w, http.StatusServiceUnavailable, health)
		return
	}

	health["ottopay_status"] = "available"
	ps.respondJSON(w, http.StatusOK, health)
}

// Helper methods
func (ps *PaymentService) respondJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func (ps *PaymentService) respondError(w http.ResponseWriter, status int, message, details string) {
	ps.respondJSON(w, status, ErrorResponse{
		Error:   message,
		Details: details,
	})
}

// Middleware
func loggingMiddleware(logger *logrus.Logger) mux.MiddlewareFunc {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()

			// Wrap response writer to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}

			next.ServeHTTP(wrapped, r)

			logger.WithFields(logrus.Fields{
				"method":      r.Method,
				"path":        r.URL.Path,
				"status":      wrapped.statusCode,
				"duration_ms": time.Since(start).Milliseconds(),
				"user_agent":  r.UserAgent(),
				"remote_addr": r.RemoteAddr,
			}).Info("Request processed")
		})
	}
}

type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

func main() {
	// Setup logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Set log level from environment
	if level, err := logrus.ParseLevel(os.Getenv("LOG_LEVEL")); err == nil {
		logger.SetLevel(level)
	} else {
		logger.SetLevel(logrus.InfoLevel)
	}

	// Create payment service
	paymentService, err := NewPaymentService(logger)
	if err != nil {
		log.Fatal("Failed to create payment service:", err)
	}

	// Setup routes
	r := mux.NewRouter()
	r.Use(loggingMiddleware(logger))

	// API routes
	api := r.PathPrefix("/api/v1").Subrouter()
	api.HandleFunc("/inquiry", paymentService.handleInquiry).Methods("POST")
	api.HandleFunc("/payment", paymentService.handlePayment).Methods("POST")
	api.HandleFunc("/health", paymentService.handleHealth).Methods("GET")

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"}, // Configure appropriately for production
		AllowedMethods:   []string{"GET", "POST", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	handler := c.Handler(r)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	logger.WithField("port", port).Info("Starting OttoPay VA service")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	if err := server.ListenAndServe(); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

// Example usage with curl:
/*
# 1. Health check
curl -X GET http://localhost:8080/api/v1/health

# 2. Customer inquiry
curl -X POST http://localhost:8080/api/v1/inquiry \
  -H "Content-Type: application/json" \
  -d '{
    "company_code": "12173",
    "customer_number": "56751590099",
    "channel_type": "WEB"
  }'

# Response:
# {
#   "success": true,
#   "customer_name": "John Doe",
#   "amount": "150000.00",
#   "currency": "IDR",
#   "message": "Customer found",
#   "inquiry_token": "TOKEN-**********"
# }

# 3. Process payment
curl -X POST http://localhost:8080/api/v1/payment \
  -H "Content-Type: application/json" \
  -d '{
    "inquiry_token": "TOKEN-**********",
    "reference": "PAY-REF-001",
    "paid_amount": "150000.00"
  }'

# Response:
# {
#   "success": true,
#   "payment_status": "SUCCESS",
#   "transaction_id": "PAY-**********",
#   "message": "Payment completed successfully",
#   "paid_amount": "150000.00",
#   "currency": "IDR"
# }
*/

// Environment variables needed:
// OTTOPAY_API_KEY=your-api-key
// OTTOPAY_USERNAME=your-username
// OTTOPAY_PASSWORD=your-password
// OTTOPAY_BASE_URL=https://api.ottopay.id (optional)
// LOG_LEVEL=info (optional)
// PORT=8080 (optional)
