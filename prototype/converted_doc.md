OttoPay
APIVirtualAccount
ForMerchant
Specification
v.1.1.
_RevisionDate: 26 Maret 2024_


Copyright
Copyright © 2020 OttoPay. All rights reserved. No part of this publication may be
reproducedordistributedwithoutthepriorconsentofOttoPay,Suite2103,Jl.DR.IdeAnak
AgungGdeAgungKav.E.1.2No.1&2(d/h.Jl.LingkarMegaKuningan)Jakarta12950.

Disclaimer
OttoPayprovidesthispublication _asis_ withoutwarrantyofanykind,eitherexpressedor
implied. This publication could include technical inaccuracies or typographical errors.
Changes are periodically made to the information herein. These changes will be
incorporatedinneweditionsofthepublication.

OttoPay may make improvement and/orchangesin the product(s) and/orprograms(s)
describedinthispublicationatanytime.

Trademarks
OttoPay is aregistered trademark ofOttoPay. All other brandand product names are
trademarksorregisteredtrademarksoftheirrespectivecompanies.

ContactInformation
Website:http://www.ottopay.id


Preface

Abstract

ThisdocumentdescribesthestepstousetheOttoPayVirtualAccountAPIs.

Audience

ThisdocumentisintendedforOttoPayAPIPlatformsystemintegrator.

### CHANGEHISTORY

```
Versi Tanggal DiubahOleh PenjelasanPerubahan
1.0.0 14 July 2020 AntoniusCherdy InitialVersion
1.0.1 10 June 2021 AntoniusCherdy UpdateDescription
1.0.2 10 August 2021 AntoniusCherdy AddTokenAuthorization
1.0.3 01 September
2021
```
```
EviAgustinaBr
Dongoran
```
```
UpdateFlowdiagram
```
```
1.0.4 10 January 2022 AntoniusCherdy SubprefixmovedtoCustomerNumber
1.1.0 26 Desember 2022 AntoniusCherdy EnhancementRCCode
1.1.1 19 Oktober 2023 Johanes InformationRCCodeandGetToken
```
```
1.1.2 26 Maret 2024 AhmadAbidin ReviewSpecasTech
```

## Contents

- 1. ProcessFlow
- 2. OttoPayVAtoMerchant
- 2.1GetToken
- 2.2Inquiry
- 2.3Payment


## 1. ProcessFlow


## 2. OttoPayVAtoMerchant

ThisAPIisusedforOttoPaytoconnecttoMerchantHostVA

## 2.1GetToken

**Header:**

**Name Type Description**
x-api-key String APIKEYfromMerchantHost

**Request:**

**Name Type Description**
Username String User Name from Merchant
Host
Password String PasswordfromMerchantHost
TheheaderinformationmustalwaysbeincludedinanymessagessenttoMerchantHost.

### {

"username":"ottopay",
"password":"ac7l0qf!"
}


**Response:**

**Name Type Description**
Meta status Boolean optional
code Number 200 are successes,
apart from 200 are
failures
message String
data id String Response ID from
MerchantHost
username String User Name from
MerchantHost
Id_token String Thistokenwillbeused
when OttoPay do
Inquiry and Payment
VA

### {

"meta":{
"status":true,
"code":200,
"message":"success"
},
"data":{
"id":"45c14915-f731-43ac-b25a-2182e13c98f7",
"username":"ottopay",
"id_token":
"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiNDVjMTQ5MTUtZjczMS00M2FjLWI
yNWEtMjE4MmUxM2M5OGY3IiwiYXBwX25hbWUiOiJPVFRPUEFZIiwidXNlcl9uYW1lIjoib3R
b3BheSIsImV4cCI6MTYzMDAzNzE5Mn0.hvziePJPrnXwDU2ed6kU_yNXWJIYvjKySaABkMCeFvs
"
}
}

## 2.2Inquiry

OttoPayhostcallthisendpointforinquirycustomertoMerchantHost


**POST** https://<<MerchantHostendpoint>>

**Header:
Key DataType Mandatory Description**
Authorization BearerToken Yes Valuefromid_tokenin
GetTokenResponse

**Request:
Key DataType Mandatory Description**
CompanyCode String(max5) Yes VACompanyCode
CompanyCode must
be numeric in string
format

CustomerNumber String(max11) Yes SubPrefix (optional) +
CustomerNumber /
BillID

CustomerNumber
must be numeric in
stringformat
RequestID String(max255) Yes generatedbyBank
ChannelType String(max50) Yes channel type from
bank
TransactionDate String(19) No YYYY/MM/DD
HH:MM:SS
or
YYYY-MM-DD
HH:MM:SS
AdditionalData String(max255) No Fillwithemptystring

### {

"CompanyCode":"12173",
"CustomerNumber":"***********",
"RequestID":"201507131507262221400000001975",
"ChannelType":"BINA",
"TransactionDate":"15/03/201422:07:40",
"AdditionalData":""
}


**Response:
Key DataType Mandatory Value**
CompanyCode String(max5) Yes VACompanyCode
CompanyCode must be
numericinstringformat
CustomerNumberString(max11) Yes SubPrefix (optional) +
CustomerNumber/BillID

```
CustomerNumber must be
numericinstringformat
RequestID String(max255) Yes generatedbyBank
CustomerName String(max30) Yes
CurrencyCode String(3) Yes IDR
TotalAmount String(max15) Yes
SubCompany String(max5) Yes Fillwith“00000”
DetailBills List(N) No Fillwithnull
AdditionalData String(max255) No
```
### {

```
"CompanyCode":"12173",
"CustomerNumber":"***********",
"RequestID":"201507131507262221400000001975",
"InquiryStatus":"00",
"InquiryReason":{"Indonesian":"Sukses","English":"Success"},
"CustomerName":"CustomerNameVirtualAccount",
"CurrencyCode":"IDR",
"TotalAmount":"150000.00",
"SubCompany":"00000",
"DetailBills":null,
"AdditionalData":""
}
```
Or

### {

```
"CompanyCode":"12173",
"CustomerNumber":"***********",
"RequestID":"201507131507262221400000001975",
"InquiryStatus":"01",
"InquiryReason":{
"Indonesian":"Keteranganerrordisini",
```

```
"English":"Theerrorreasongoeshere"
},
"CustomerName":"CustomerNameVirtualAccount",
"CurrencyCode":"IDR",
"TotalAmount":"0.00",
"SubCompany":"00000",
"DetailBills":null,
"FreeTexts":null,
"AdditionalData":""
}
```
## 2.3Payment

OttoPayhostcallthisendpointforpaymenttoMerchantHost.

**POST** https://<<MerchantHostEndpoint>>

**Header:
Key DataType Mandatory Description**
Token BearerToken Yes Valuefromid_tokenin
GetTokenResponse

**Request:
Key DataType Mandatory Description**
CompanyCode String(max5) Yes VACompanyCode
CustomerNumber String(max11) Yes SubPrefix (optional) +
CustomerNumber /
BillID

CustomerNumber
must be numeric in
stringformat
RequestID String(max255) Yes
ChannelType String(max50) Yes channel type from
bank
CustomerName String(max30) Yes generatedbyBank
CurrencyCode String(3) Yes IDR
PaidAmount String(max15) Yes PaymentAmount
TotalAmount String(max15) Yes BilledAmount
SubCompany String(max5) No 00000
TransactionDate String(19) No YYYY/MM/DD
HH:MM:SS
or
YYYY-MM-DD
HH:MM:SS


Reference String(max255) Yes
DetailBills List(N) No []
AdditionalData String(max255) No

### {

```
"CompanyCode":"12173",
"CustomerNumber":"***************",
"RequestID":"201507131507262221400000001975",
"ChannelType":"BINA",
"CustomerName":"CustomerVirtualAccount",
"CurrencyCode":"IDR",
"PaidAmount":"150000.00",
"TotalAmount":"150000.00",
"SubCompany":"00000",
"TransactionDate":"15/03/201422:07:40",
"Reference":"**********",
"DetailBills":[],
"Additionaldata":""
}
```
**Response:**

**Key DataType Mandatory Description**
CompanyCode String(max5) Yes VACompanyCode
CustomerNumber String(max11) Yes SubPrefix (optional) +
CustomerNumber /
BillID

CustomerNumber
must be numeric in
stringformat
RequestID String(max255) Yes generatedbybank
PaymentFlagStatus String(2) Yes 00 IfSuccess
01 ifFailed
02 IfTimeout
PaymentFlagReason Object Yes Seesamplebelow
Indonesian String(max64) Yes Sample:“Sukses”
English String(max64) Yes Sample:“Success”
CustomerName String(max30) Yes
CurrencyCode String(3) Yes IDR
PaidAmount String(max15) Yes PaymentAmount
TotalAmount String(max15) Yes BilledAmount
TransactionDate String(19) No YYYY/MM/DD
HH:MM:SS


or
YYYY-MM-DD
HH:MM:SS
DetailBills List(N) No Fillwithnull
Freetext String No Fillwithnull
AdditionalData String(max255) No Fillemptystring

### {

```
"CompanyCode":"12173",
"CustomerNumber":"***************",
"RequestID":"201507131507262221400000001975",
"PaymentFlagStatus":"00",
"PaymentFlagReason":{
"Indonesian":"Sukses",
"English":"Success"
},
"CustomerName":"CustomerVirtualAccount",
"CurrencyCode":"IDR",
"PaidAmount":"150000.00",
"TotalAmount":"150000.00",
"TransactionDate":"15/03/201422:07:40",
"DetailBills":null,
"FreeTexts":null,
"AdditionalData":""
}
```
Or

```
{
"CompanyCode":"12173",
"CustomerNumber":"************",
"RequestID":"201507131507262221400000001975",
"PaymentFlagStatus":"01",
"PaymentFlagReason":{
"Indonesian":“Jumlahpembayaransalah",
"English":"InvalidAmount"
},
"CustomerName":"CustomerVirtualAccount",
"CurrencyCode":"IDR",
"PaidAmount":"150000.00",
"TotalAmount":"150000.00",
"TransactionDate":"15/03/201422:07:40",
```

```
"DetailBills":null,
"FreeTexts":null,
"AdditionalData":""
}
```
```
Note:
```
- 2 lastdigitsforTotalAmountandPaidAmountfieldshouldbedecimalpoint.
- TotalAmountfieldvaluewillbesamewithTotalAmountfieldvaluereturnedfrom
    copartnerswhenBankdoinquirylistofbills.
- RequestIDisuniquefromBankforeachtransaction.
- PaidAmountfieldvaluewillbetotalamountpaidbycustomerthroughBank.
- CurrencyCodemustbesameforTotalAmountandPaidAmount.
    ● CurrencyCodefieldmayvarywiththesevalues:
       o IDR
       o USD
    ● PaymentFlagStatusfieldisstatusforpaymentflag.Thisfieldmayvarywiththese
       values:
          o 00 =Successpaymentflag.
          o 01 =Rejectpaymentflagbyco-partner,co-partnermustdefinedreasonfor
             thisstatusinPaymentFlagReasonfieldvalueintwolanguages,Indonesian
             andEnglish.
             Onlythisresponsethatthecustomer’spaymentwillbereversedbythebank.
          o 02 =PaymentTimeout.
          Note:paymentflagstatusotherthan00,01,and 02 willbeconsideredas 01


```
AppendixAResponseCode
```
### N

### O

```
Service
Code
```
```
Service
Name RCCode RCDescription
1 24 InquiryVA 2002400 SuccessInquiry
2 24 InquiryVA 4042412 InvalidBill
3 24 InquiryVA 4012401 InvalidToken
4 24 InquiryVA 4092401 DuplicatePartnerReferenceNumber
5 24 InquiryVA 5002400 Badrequest
6 24 InquiryVA 4002400 InternalServerError
7 25 PaymentVA 2002500 SuccessPayment
8 25 PaymentVA 4042512 InvalidBill
9 25 PaymentVA 4032500 TransactionExpired
10 25 PaymentVA 4012501 InvalidToken
11 25 PaymentVA 5002500 Badrequest
12 25 PaymentVA 4002500 InternalServerError
```
```
13 25 PaymentVA 4092501
```
```
DuplicatePartnerReferenceNumber
(payment2x)
14 25 PaymentVA 4042513 InvalidAmount
15 25 PaymentVA 4042514 PaidBill
```

