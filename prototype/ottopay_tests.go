// ottopay_test.go
package ottopay

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"
)

// MockHTTPClient for testing
type MockHTTPClient struct {
	responses map[string]*http.Response
	requests  []*http.Request
}

func NewMockHTTPClient() *MockHTTPClient {
	return &MockHTTPClient{
		responses: make(map[string]*http.Response),
		requests:  make([]*http.Request, 0),
	}
}

func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	m.requests = append(m.requests, req)
	
	// Determine response based on URL path
	path := req.URL.Path
	if resp, exists := m.responses[path]; exists {
		return resp, nil
	}
	
	// Default response
	return &http.Response{
		StatusCode: 404,
		Body:       io.NopCloser(strings.NewReader(`{"error": "not found"}`)),
	}, nil
}

func (m *MockHTTPClient) SetResponse(path string, statusCode int, body interface{}) {
	var bodyReader io.ReadCloser
	
	if body != nil {
		bodyBytes, _ := json.Marshal(body)
		bodyReader = io.NopCloser(bytes.NewReader(bodyBytes))
	} else {
		bodyReader = io.NopCloser(strings.NewReader(""))
	}
	
	m.responses[path] = &http.Response{
		StatusCode: statusCode,
		Body:       bodyReader,
		Header:     make(http.Header),
	}
}

func (m *MockHTTPClient) GetLastRequest() *http.Request {
	if len(m.requests) == 0 {
		return nil
	}
	return m.requests[len(m.requests)-1]
}

// Test Logger implementation
type TestLogger struct {
	logs []string
}

func (l *TestLogger) Debugf(format string, args ...interface{}) {
	l.logs = append(l.logs, "DEBUG: "+format)
}

func (l *TestLogger) Infof(format string, args ...interface{}) {
	l.logs = append(l.logs, "INFO: "+format)
}

func (l *TestLogger) Errorf(format string, args ...interface{}) {
	l.logs = append(l.logs, "ERROR: "+format)
}

func TestNewClient(t *testing.T) {
	client := NewClient("test-api-key", "test-user", "test-pass")
	
	if client == nil {
		t.Fatal("Expected client to be created")
	}
}

func TestNewClientWithOptions(t *testing.T) {
	logger := &TestLogger{}
	mockHTTP := NewMockHTTPClient()
	
	client := NewClient("test-api-key", "test-user", "test-pass",
		WithBaseURL("https://test.ottopay.id"),
		WithTimeout(10*time.Second),
		WithHTTPClient(mockHTTP),
		WithLogger(logger),
	)
	
	if client == nil {
		t.Fatal("Expected client to be created")
	}
}

func TestGetToken(t *testing.T) {
	mockHTTP := NewMockHTTPClient()
	logger := &TestLogger{}
	
	// Setup mock response
	tokenResponse := GetTokenResponse{
		Meta: Meta{
			Status:  true,
			Code:    200,
			Message: "success",
		},
		Data: GetTokenData{
			ID:       "test-id",
			Username: "ottopay",
			IDToken:  "test-jwt-token",
		},
	}
	
	mockHTTP.SetResponse("/token", 200, tokenResponse)
	
	client := NewClient("test-api-key", "test-user", "test-pass",
		WithBaseURL("https://test.ottopay.id"),
		WithHTTPClient(mockHTTP),
		WithLogger(logger),
	)
	
	ctx := context.Background()
	response, err := client.GetToken(ctx, nil)
	
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	
	if response.Data.IDToken != "test-jwt-token" {
		t.Errorf("Expected token 'test-jwt-token', got: %s", response.Data.IDToken)
	}
	
	// Check that request was made correctly
	lastReq := mockHTTP.GetLastRequest()
	if lastReq == nil {
		t.Fatal("Expected request to be made")
	}
	
	if lastReq.Header.Get("x-api-key") != "test-api-key" {
		t.Errorf("Expected API key header to be set")
	}
}

func TestInquiry(t *testing.T) {
	mockHTTP := NewMockHTTPClient()
	logger := &TestLogger{}
	
	// Setup token response
	tokenResponse := GetTokenResponse{
		Meta: Meta{Status: true, Code: 200, Message: "success"},
		Data: GetTokenData{IDToken: "test-jwt-token"},
	}
	mockHTTP.SetResponse("/token", 200, tokenResponse)
	
	// Setup inquiry response
	inquiryResponse := InquiryResponse{
		CompanyCode:    "12173",
		CustomerNumber: "56751590099",
		RequestID:      "test-request-id",
		InquiryStatus:  "00",
		InquiryReason: InquiryReason{
			Indonesian: "Sukses",
			English:    "Success",
		},
		CustomerName: "Test Customer",
		CurrencyCode: "IDR",
		TotalAmount:  "150000.00",
		SubCompany:   "00000",
	}
	mockHTTP.SetResponse("/inquiry", 200, inquiryResponse)
	
	client := NewClient("test-api-key", "test-user", "test-pass",
		WithBaseURL("https://test.ottopay.id"),
		WithHTTPClient(mockHTTP),
		WithLogger(logger),
	)
	
	ctx := context.Background()
	inquiryReq := &InquiryRequest{
		CompanyCode:    "12173",
		CustomerNumber: "56751590099",
		RequestID:      "test-request-id",
		ChannelType:    "BINA",
	}
	
	response, err := client.Inquiry(ctx, inquiryReq)
	
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	
	if !response.IsInquirySuccess() {
		t.Errorf("Expected successful inquiry")
	}
	
	if response.CustomerName != "Test Customer" {
		t.Errorf("Expected customer name 'Test Customer', got: %s", response.CustomerName)
	}
}

func TestPayment(t *testing.T) {
	mockHTTP := NewMockHTTPClient()
	logger := &TestLogger{}
	
	// Setup token response
	tokenResponse := GetTokenResponse{
		Meta: Meta{Status: true, Code: 200, Message: "success"},
		Data: GetTokenData{IDToken: "test-jwt-token"},
	}
	mockHTTP.SetResponse("/token", 200, tokenResponse)
	
	// Setup payment response
	paymentResponse := PaymentResponse{
		CompanyCode:       "12173",
		CustomerNumber:    "56751590099",
		RequestID:         "test-request-id",
		PaymentFlagStatus: "00",
		PaymentFlagReason: PaymentFlagReason{
			Indonesian: "Sukses",
			English:    "Success",
		},
		CustomerName: "Test Customer",
		CurrencyCode: "IDR",
		PaidAmount:   "150000.00",
		TotalAmount:  "150000.00",
	}
	mockHTTP.SetResponse("/payment", 200, paymentResponse)
	
	client := NewClient("test-api-key", "test-user", "test-pass",
		WithBaseURL("https://test.ottopay.id"),
		WithHTTPClient(mockHTTP),
		WithLogger(logger),
	)
	
	ctx := context.Background()
	paymentReq := &PaymentRequest{
		CompanyCode:    "12173",
		CustomerNumber: "56751590099",
		RequestID:      "test-request-id",
		ChannelType:    "BINA",
		CustomerName:   "Test Customer",
		CurrencyCode:   "IDR",
		PaidAmount:     "150000.00",
		TotalAmount:    "150000.00",
		Reference:      "test-ref-123",
	}
	
	response, err := client.Payment(ctx, paymentReq)
	
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	
	if !response.IsPaymentSuccess() {
		t.Errorf("Expected successful payment")
	}
	
	if response.PaidAmount != "150000.00" {
		t.Errorf("Expected paid amount '150000.00', got: %s", response.PaidAmount)
	}
}

func TestValidateInquiryRequest(t *testing.T) {
	tests := []struct {
		name    string
		req     *InquiryRequest
		wantErr bool
	}{
		{
			name: "valid request",
			req: &InquiryRequest{
				CompanyCode:    "12173",
				CustomerNumber: "56751590099",
				RequestID:      "test-request-id",
				ChannelType:    "BINA",
			},
			wantErr: false,
		},
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name: "empty company code",
			req: &InquiryRequest{
				CompanyCode:    "",
				CustomerNumber: "56751590099",
				RequestID:      "test-request-id",
				ChannelType:    "BINA",
			},
			wantErr: true,
		},
		{
			name: "company code too long",
			req: &InquiryRequest{
				CompanyCode:    "123456", // 6 characters, max is 5
				CustomerNumber: "56751590099",
				RequestID:      "test-request-id",
				ChannelType:    "BINA",
			},
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateInquiryRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateInquiryRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidatePaymentRequest(t *testing.T) {
	tests := []struct {
		name    string
		req     *PaymentRequest
		wantErr bool
	}{
		{
			name: "valid request",
			req: &PaymentRequest{
				CompanyCode:    "12173",
				CustomerNumber: "56751590099",
				RequestID:      "test-request-id",
				CustomerName:   "Test Customer",
				CurrencyCode:   "IDR",
				PaidAmount:     "150000.00",
				TotalAmount:    "150000.00",
			},
			wantErr: false,
		},
		{
			name:    "nil request",
			req:     nil,
			wantErr: true,
		},
		{
			name: "invalid currency",
			req: &PaymentRequest{
				CompanyCode:    "12173",
				CustomerNumber: "56751590099",
				RequestID:      "test-request-id",
				CustomerName:   "Test Customer",
				CurrencyCode:   "EUR", // Invalid
				PaidAmount:     "150000.00",
				TotalAmount:    "150000.00",
			},
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePaymentRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidatePaymentRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// Example usage tests
func ExampleClient_GetToken() {
	client := NewClient("your-api-key", "your-username", "your-password",
		WithBaseURL("https://api.ottopay.id"),
		WithTimeout(30*time.Second),
	)
	
	ctx := context.Background()
	response, err := client.GetToken(ctx, nil)
	if err != nil {
		panic(err)
	}
	
	if response.Meta.Code == 200 {
		// Token is automatically cached in client
		println("Token obtained successfully")
	}
}

func ExampleClient_Inquiry() {
	client := NewClient("your-api-key", "your-username", "your-password",
		WithBaseURL("https://api.ottopay.id"),
	)
	
	ctx := context.Background()
	
	// First get token (or it will be done automatically)
	_, err := client.GetToken(ctx, nil)
	if err != nil {
		panic(err)
	}
	
	// Perform inquiry
	inquiryReq := &InquiryRequest{
		CompanyCode:     "12173",
		CustomerNumber:  "56751590099",
		RequestID:       "unique-request-id-123",
		ChannelType:     "BINA",
		TransactionDate: "2024/03/26 10:30:00",
	}
	
	// Validate request
	if err := ValidateInquiryRequest(inquiryReq); err != nil {
		panic(err)
	}
	
	response, err := client.Inquiry(ctx, inquiryReq)
	if err != nil {
		panic(err)
	}
	
	if response.IsInquirySuccess() {
		println("Customer found:", response.CustomerName)
		println("Amount due:", response.TotalAmount, response.CurrencyCode)
	} else {
		println("Inquiry failed:", response.InquiryReason.English)
	}
}

func ExampleClient_Payment() {
	client := NewClient("your-api-key", "your-username", "your-password",
		WithBaseURL("https://api.ottopay.id"),
	)
	
	ctx := context.Background()
	
	// Process payment
	paymentReq := &PaymentRequest{
		CompanyCode:     "12173",
		CustomerNumber:  "56751590099",
		RequestID:       "unique-payment-id-456",
		ChannelType:     "BINA",
		CustomerName:    "John Doe",
		CurrencyCode:    CurrencyIDR,
		PaidAmount:      "150000.00",
		TotalAmount:     "150000.00",
		Reference:       "PAY-2024-001",
		TransactionDate: "2024/03/26 10:35:00",
	}
	
	// Validate request
	if err := ValidatePaymentRequest(paymentReq); err != nil {
		panic(err)
	}
	
	response, err := client.Payment(ctx, paymentReq)
	if err != nil {
		panic(err)
	}
	
	switch {
	case response.IsPaymentSuccess():
		println("Payment successful!")
		println("Paid amount:", response.PaidAmount, response.CurrencyCode)
	case response.IsPaymentFailed():
		println("Payment failed:", response.PaymentFlagReason.English)
	case response.IsPaymentTimeout():
		println("Payment timed out")
	}
}

// Benchmark tests
func BenchmarkInquiry(b *testing.B) {
	mockHTTP := NewMockHTTPClient()
	
	// Setup responses
	tokenResponse := GetTokenResponse{
		Meta: Meta{Status: true, Code: 200, Message: "success"},
		Data: GetTokenData{IDToken: "test-jwt-token"},
	}
	mockHTTP.SetResponse("/token", 200, tokenResponse)
	
	inquiryResponse := InquiryResponse{
		CompanyCode:   "12173",
		InquiryStatus: "00",
	}
	mockHTTP.SetResponse("/inquiry", 200, inquiryResponse)
	
	client := NewClient("test-api-key", "test-user", "test-pass",
		WithBaseURL("https://test.ottopay.id"),
		WithHTTPClient(mockHTTP),
	)
	
	ctx := context.Background()
	req := &InquiryRequest{
		CompanyCode:    "12173",
		CustomerNumber: "56751590099",
		RequestID:      "test-request-id",
		ChannelType:    "BINA",
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.Inquiry(ctx, req)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Integration test example (requires real endpoint)
func TestIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}
	
	// This would be used with real credentials and endpoint
	// client := NewClient("real-api-key", "real-username", "real-password",
	//     WithBaseURL("https://real-api.ottopay.id"),
	// )
	//
	// ctx := context.Background()
	// response, err := client.GetToken(ctx, nil)
	// if err != nil {
	//     t.Fatalf("Failed to get token: %v", err)
	// }
	//
	// if response.Meta.Code != 200 {
	//     t.Errorf("Expected successful token response")
	// }
}