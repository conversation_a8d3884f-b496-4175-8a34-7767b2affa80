// Package ottopay provides a Go client for OttoPay Virtual Account API
package ottopay

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// Client represents the OttoPay VA client interface
type Client interface {
	// GetToken authenticates and retrieves access token
	GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error)
	
	// Inquiry performs customer inquiry for virtual account
	Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error)
	
	// Payment processes payment for virtual account
	Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)
}

// HTTPClient interface for HTTP operations (for testing/mocking)
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// Logger interface for logging operations
type Logger interface {
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Errorf(format string, args ...interface{})
}

// NoOpLogger is a no-operation logger implementation
type NoOpLogger struct{}

func (l *NoOpLogger) Debugf(format string, args ...interface{}) {}
func (l *NoOpLogger) Infof(format string, args ...interface{})  {}
func (l *NoOpLogger) Errorf(format string, args ...interface{}) {}

// Config holds configuration for the OttoPay client
type Config struct {
	BaseURL    string
	APIKey     string
	Username   string
	Password   string
	Timeout    time.Duration
	HTTPClient HTTPClient
	Logger     Logger
}

// Option defines a functional option for configuring the client
type Option func(*Config)

// WithBaseURL sets the base URL for the API
func WithBaseURL(baseURL string) Option {
	return func(c *Config) {
		c.BaseURL = baseURL
	}
}

// WithTimeout sets the HTTP client timeout
func WithTimeout(timeout time.Duration) Option {
	return func(c *Config) {
		c.Timeout = timeout
	}
}

// WithHTTPClient sets a custom HTTP client
func WithHTTPClient(client HTTPClient) Option {
	return func(c *Config) {
		c.HTTPClient = client
	}
}

// WithLogger sets a custom logger
func WithLogger(logger Logger) Option {
	return func(c *Config) {
		c.Logger = logger
	}
}

// client is the concrete implementation of the Client interface
type client struct {
	config     *Config
	httpClient HTTPClient
	logger     Logger
	token      string // cached token
}

// NewClient creates a new OttoPay client with the provided options
func NewClient(apiKey, username, password string, opts ...Option) Client {
	config := &Config{
		APIKey:   apiKey,
		Username: username,
		Password: password,
		Timeout:  30 * time.Second,
		Logger:   &NoOpLogger{},
	}

	for _, opt := range opts {
		opt(config)
	}

	if config.HTTPClient == nil {
		config.HTTPClient = &http.Client{
			Timeout: config.Timeout,
		}
	}

	return &client{
		config:     config,
		httpClient: config.HTTPClient,
		logger:     config.Logger,
	}
}

// Error represents an API error
type Error struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Service string `json:"service,omitempty"`
}

func (e *Error) Error() string {
	return fmt.Sprintf("ottopay api error: code=%d, message=%s", e.Code, e.Message)
}

// Meta represents the metadata in API responses
type Meta struct {
	Status  bool   `json:"status"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// GetTokenRequest represents the request for getting authentication token
type GetTokenRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// GetTokenData represents the data part of get token response
type GetTokenData struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	IDToken  string `json:"id_token"`
}

// GetTokenResponse represents the response from get token endpoint
type GetTokenResponse struct {
	Meta Meta         `json:"meta"`
	Data GetTokenData `json:"data"`
}

// InquiryRequest represents the request for customer inquiry
type InquiryRequest struct {
	CompanyCode       string `json:"CompanyCode"`       // max 5, numeric string
	CustomerNumber    string `json:"CustomerNumber"`    // max 11, numeric string
	RequestID         string `json:"RequestID"`         // max 255
	ChannelType       string `json:"ChannelType"`       // max 50
	TransactionDate   string `json:"TransactionDate,omitempty"` // YYYY/MM/DD HH:MM:SS or YYYY-MM-DD HH:MM:SS
	AdditionalData    string `json:"AdditionalData,omitempty"`  // max 255
}

// InquiryReason represents the inquiry reason in multiple languages
type InquiryReason struct {
	Indonesian string `json:"Indonesian"`
	English    string `json:"English"`
}

// InquiryResponse represents the response from inquiry endpoint
type InquiryResponse struct {
	CompanyCode    string        `json:"CompanyCode"`
	CustomerNumber string        `json:"CustomerNumber"`
	RequestID      string        `json:"RequestID"`
	InquiryStatus  string        `json:"InquiryStatus"` // "00" success, "01" failed
	InquiryReason  InquiryReason `json:"InquiryReason"`
	CustomerName   string        `json:"CustomerName"`
	CurrencyCode   string        `json:"CurrencyCode"` // IDR, USD
	TotalAmount    string        `json:"TotalAmount"`
	SubCompany     string        `json:"SubCompany"`
	DetailBills    interface{}   `json:"DetailBills"`    // null or array
	FreeTexts      interface{}   `json:"FreeTexts,omitempty"`
	AdditionalData string        `json:"AdditionalData,omitempty"`
}

// PaymentRequest represents the request for payment
type PaymentRequest struct {
	CompanyCode     string      `json:"CompanyCode"`               // max 5
	CustomerNumber  string      `json:"CustomerNumber"`            // max 11, numeric string
	RequestID       string      `json:"RequestID"`                 // max 255
	ChannelType     string      `json:"ChannelType"`               // max 50
	CustomerName    string      `json:"CustomerName"`              // max 30
	CurrencyCode    string      `json:"CurrencyCode"`              // IDR, USD
	PaidAmount      string      `json:"PaidAmount"`                // max 15
	TotalAmount     string      `json:"TotalAmount"`               // max 15
	SubCompany      string      `json:"SubCompany,omitempty"`      // default "00000"
	TransactionDate string      `json:"TransactionDate,omitempty"` // YYYY/MM/DD HH:MM:SS or YYYY-MM-DD HH:MM:SS
	Reference       string      `json:"Reference"`                 // max 255
	DetailBills     interface{} `json:"DetailBills,omitempty"`     // array, default []
	AdditionalData  string      `json:"AdditionalData,omitempty"`  // max 255
}

// PaymentFlagReason represents the payment flag reason in multiple languages
type PaymentFlagReason struct {
	Indonesian string `json:"Indonesian"`
	English    string `json:"English"`
}

// PaymentResponse represents the response from payment endpoint
type PaymentResponse struct {
	CompanyCode       string            `json:"CompanyCode"`
	CustomerNumber    string            `json:"CustomerNumber"`
	RequestID         string            `json:"RequestID"`
	PaymentFlagStatus string            `json:"PaymentFlagStatus"` // "00" success, "01" failed, "02" timeout
	PaymentFlagReason PaymentFlagReason `json:"PaymentFlagReason"`
	CustomerName      string            `json:"CustomerName"`
	CurrencyCode      string            `json:"CurrencyCode"`
	PaidAmount        string            `json:"PaidAmount"`
	TotalAmount       string            `json:"TotalAmount"`
	TransactionDate   string            `json:"TransactionDate,omitempty"`
	DetailBills       interface{}       `json:"DetailBills"`
	FreeTexts         interface{}       `json:"FreeTexts"`
	AdditionalData    string            `json:"AdditionalData,omitempty"`
}

// GetToken implements the Client interface
func (c *client) GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error) {
	if req == nil {
		req = &GetTokenRequest{
			Username: c.config.Username,
			Password: c.config.Password,
		}
	}

	c.logger.Debugf("Getting token for username: %s", req.Username)

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := c.createRequest(ctx, "POST", "/token", body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	var response GetTokenResponse
	if err := c.doRequest(httpReq, &response); err != nil {
		return nil, err
	}

	if response.Meta.Code == 200 {
		c.token = response.Data.IDToken
		c.logger.Infof("Token obtained successfully")
	}

	return &response, nil
}

// Inquiry implements the Client interface
func (c *client) Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("inquiry request cannot be nil")
	}

	c.logger.Debugf("Performing inquiry for customer: %s", req.CustomerNumber)

	// Ensure we have a valid token
	if c.token == "" {
		if _, err := c.GetToken(ctx, nil); err != nil {
			return nil, fmt.Errorf("failed to get token: %w", err)
		}
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := c.createRequest(ctx, "POST", "/inquiry", body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authorization header
	httpReq.Header.Set("Authorization", "Bearer "+c.token)

	var response InquiryResponse
	if err := c.doRequest(httpReq, &response); err != nil {
		return nil, err
	}

	c.logger.Infof("Inquiry completed for customer: %s, status: %s", req.CustomerNumber, response.InquiryStatus)
	return &response, nil
}

// Payment implements the Client interface
func (c *client) Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("payment request cannot be nil")
	}

	c.logger.Debugf("Processing payment for customer: %s, amount: %s", req.CustomerNumber, req.PaidAmount)

	// Ensure we have a valid token
	if c.token == "" {
		if _, err := c.GetToken(ctx, nil); err != nil {
			return nil, fmt.Errorf("failed to get token: %w", err)
		}
	}

	// Set default values
	if req.SubCompany == "" {
		req.SubCompany = "00000"
	}
	if req.DetailBills == nil {
		req.DetailBills = []interface{}{}
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := c.createRequest(ctx, "POST", "/payment", body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authorization header
	httpReq.Header.Set("Authorization", "Bearer "+c.token)

	var response PaymentResponse
	if err := c.doRequest(httpReq, &response); err != nil {
		return nil, err
	}

	c.logger.Infof("Payment processed for customer: %s, status: %s", req.CustomerNumber, response.PaymentFlagStatus)
	return &response, nil
}

// createRequest creates an HTTP request with common headers
func (c *client) createRequest(ctx context.Context, method, path string, body []byte) (*http.Request, error) {
	baseURL := c.config.BaseURL
	if baseURL == "" {
		return nil, fmt.Errorf("base URL is required")
	}

	u, err := url.Parse(baseURL + path)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, method, u.String(), bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", c.config.APIKey)

	return req, nil
}

// doRequest executes an HTTP request and decodes the response
func (c *client) doRequest(req *http.Request, result interface{}) error {
	c.logger.Debugf("Making %s request to %s", req.Method, req.URL.String())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	c.logger.Debugf("Response status: %d, body length: %d", resp.StatusCode, len(body))

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		var apiErr Error
		if err := json.Unmarshal(body, &apiErr); err != nil {
			return &Error{
				Code:    resp.StatusCode,
				Message: string(body),
			}
		}
		return &apiErr
	}

	if result != nil {
		if err := json.Unmarshal(body, result); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// Validation helpers

// ValidateInquiryRequest validates an inquiry request
func ValidateInquiryRequest(req *InquiryRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if len(req.CompanyCode) == 0 || len(req.CompanyCode) > 5 {
		return fmt.Errorf("CompanyCode must be 1-5 characters")
	}
	if len(req.CustomerNumber) == 0 || len(req.CustomerNumber) > 11 {
		return fmt.Errorf("CustomerNumber must be 1-11 characters")
	}
	if len(req.RequestID) == 0 || len(req.RequestID) > 255 {
		return fmt.Errorf("RequestID must be 1-255 characters")
	}
	if len(req.ChannelType) == 0 || len(req.ChannelType) > 50 {
		return fmt.Errorf("ChannelType must be 1-50 characters")
	}
	return nil
}

// ValidatePaymentRequest validates a payment request
func ValidatePaymentRequest(req *PaymentRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if len(req.CompanyCode) == 0 || len(req.CompanyCode) > 5 {
		return fmt.Errorf("CompanyCode must be 1-5 characters")
	}
	if len(req.CustomerNumber) == 0 || len(req.CustomerNumber) > 11 {
		return fmt.Errorf("CustomerNumber must be 1-11 characters")
	}
	if len(req.RequestID) == 0 || len(req.RequestID) > 255 {
		return fmt.Errorf("RequestID must be 1-255 characters")
	}
	if len(req.CustomerName) == 0 || len(req.CustomerName) > 30 {
		return fmt.Errorf("CustomerName must be 1-30 characters")
	}
	if req.CurrencyCode != "IDR" && req.CurrencyCode != "USD" {
		return fmt.Errorf("CurrencyCode must be IDR or USD")
	}
	if len(req.PaidAmount) == 0 || len(req.PaidAmount) > 15 {
		return fmt.Errorf("PaidAmount must be 1-15 characters")
	}
	if len(req.TotalAmount) == 0 || len(req.TotalAmount) > 15 {
		return fmt.Errorf("TotalAmount must be 1-15 characters")
	}
	return nil
}

// Constants for response codes
const (
	// Inquiry status codes
	InquiryStatusSuccess = "00"
	InquiryStatusFailed  = "01"

	// Payment flag status codes
	PaymentStatusSuccess = "00"
	PaymentStatusFailed  = "01"
	PaymentStatusTimeout = "02"

	// Currency codes
	CurrencyIDR = "IDR"
	CurrencyUSD = "USD"
)

// Helper functions for status checks

// IsInquirySuccess checks if inquiry was successful
func (r *InquiryResponse) IsInquirySuccess() bool {
	return r.InquiryStatus == InquiryStatusSuccess
}

// IsPaymentSuccess checks if payment was successful
func (r *PaymentResponse) IsPaymentSuccess() bool {
	return r.PaymentFlagStatus == PaymentStatusSuccess
}

// IsPaymentFailed checks if payment failed
func (r *PaymentResponse) IsPaymentFailed() bool {
	return r.PaymentFlagStatus == PaymentStatusFailed
}

// IsPaymentTimeout checks if payment timed out
func (r *PaymentResponse) IsPaymentTimeout() bool {
	return r.PaymentFlagStatus == PaymentStatusTimeout
}