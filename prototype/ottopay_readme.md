# OttoPay Virtual Account Go Client

A comprehensive, idiomatic Go client library for the OttoPay Virtual Account API. This package follows SOLID principles and Go best practices to provide a robust, testable, and maintainable solution for integrating with OttoPay's virtual account services.

## Features

- ✅ **Complete API Coverage**: Support for all OttoPay VA endpoints (Token, Inquiry, Payment)
- ✅ **SOLID Principles**: Clean architecture with dependency injection and interface segregation
- ✅ **Go Idioms**: Context support, proper error handling, functional options pattern
- ✅ **Production Ready**: Comprehensive logging, validation, and error handling
- ✅ **Testable**: HTTP client abstraction for easy mocking and testing
- ✅ **Type Safe**: Strong typing for all requests and responses
- ✅ **Configurable**: Flexible configuration with sensible defaults

## Installation

```bash
go get github.com/yourusername/ottopay-go
```

## Quick Start

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/yourusername/ottopay-go"
)

func main() {
    // Create client with configuration
    client := ottopay.NewClient(
        "your-api-key",
        "your-username", 
        "your-password",
        ottopay.WithBaseURL("https://api.ottopay.id"),
        ottopay.WithTimeout(30*time.Second),
    )
    
    ctx := context.Background()
    
    // Authenticate (token is cached automatically)
    tokenResp, err := client.GetToken(ctx, nil)
    if err != nil {
        log.Fatal("Failed to get token:", err)
    }
    
    if tokenResp.Meta.Code != 200 {
        log.Fatal("Authentication failed:", tokenResp.Meta.Message)
    }
    
    fmt.Println("✅ Authentication successful")
    
    // Perform customer inquiry
    inquiryReq := &ottopay.InquiryRequest{
        CompanyCode:    "12173",
        CustomerNumber: "***********",
        RequestID:      "INQ-" + time.Now().Format("20060102150405"),
        ChannelType:    "BINA",
    }
    
    // Validate request before sending
    if err := ottopay.ValidateInquiryRequest(inquiryReq); err != nil {
        log.Fatal("Invalid inquiry request:", err)
    }
    
    inquiryResp, err := client.Inquiry(ctx, inquiryReq)
    if err != nil {
        log.Fatal("Inquiry failed:", err)
    }
    
    if inquiryResp.IsInquirySuccess() {
        fmt.Printf("✅ Customer found: %s\n", inquiryResp.CustomerName)
        fmt.Printf("💰 Amount due: %s %s\n", inquiryResp.TotalAmount, inquiryResp.CurrencyCode)
        
        // Process payment
        paymentReq := &ottopay.PaymentRequest{
            CompanyCode:    inquiryReq.CompanyCode,
            CustomerNumber: inquiryReq.CustomerNumber,
            RequestID:      "PAY-" + time.Now().Format("20060102150405"),
            ChannelType:    inquiryReq.ChannelType,
            CustomerName:   inquiryResp.CustomerName,
            CurrencyCode:   inquiryResp.CurrencyCode,
            PaidAmount:     inquiryResp.TotalAmount,
            TotalAmount:    inquiryResp.TotalAmount,
            Reference:      "REF-" + time.Now().Format("20060102150405"),
        }
        
        // Validate payment request
        if err := ottopay.ValidatePaymentRequest(paymentReq); err != nil {
            log.Fatal("Invalid payment request:", err)
        }
        
        paymentResp, err := client.Payment(ctx, paymentReq)
        if err != nil {
            log.Fatal("Payment failed:", err)
        }
        
        switch {
        case paymentResp.IsPaymentSuccess():
            fmt.Printf("✅ Payment successful! Amount: %s %s\n", 
                paymentResp.PaidAmount, paymentResp.CurrencyCode)
        case paymentResp.IsPaymentFailed():
            fmt.Printf("❌ Payment failed: %s\n", paymentResp.PaymentFlagReason.English)
        case paymentResp.IsPaymentTimeout():
            fmt.Println("⏰ Payment timed out")
        }
    } else {
        fmt.Printf("❌ Customer inquiry failed: %s\n", inquiryResp.InquiryReason.English)
    }
}
```

## Configuration Options

The client supports various configuration options using the functional options pattern:

```go
import (
    "time"
    "net/http"
    "github.com/yourusername/ottopay-go"
)

// Custom HTTP client with specific settings
httpClient := &http.Client{
    Timeout: 60 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:       10,
        IdleConnTimeout:    30 * time.Second,
        DisableCompression: true,
    },
}

// Custom logger implementation
type MyLogger struct{}
func (l *MyLogger) Debugf(format string, args ...interface{}) { /* implementation */ }
func (l *MyLogger) Infof(format string, args ...interface{})  { /* implementation */ }
func (l *MyLogger) Errorf(format string, args ...interface{}) { /* implementation */ }

// Create client with all options
client := ottopay.NewClient(
    "your-api-key",
    "your-username",
    "your-password",
    ottopay.WithBaseURL("https://api.ottopay.id"),
    ottopay.WithTimeout(30*time.Second),
    ottopay.WithHTTPClient(httpClient),
    ottopay.WithLogger(&MyLogger{}),
)
```

## API Reference

### Client Interface

```go
type Client interface {
    GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error)
    Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error)
    Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error)
}
```

### Authentication

The client automatically handles token management. Tokens are cached and reused for subsequent requests.

```go
// Explicit token request (optional - done automatically)
tokenResp, err := client.GetToken(ctx, &ottopay.GetTokenRequest{
    Username: "custom-username",
    Password: "custom-password",
})

// Or use default credentials from client configuration
tokenResp, err := client.GetToken(ctx, nil)
```

### Customer Inquiry

```go
req := &ottopay.InquiryRequest{
    CompanyCode:       "12173",          // Required: max 5 chars, numeric
    CustomerNumber:    "***********",    // Required: max 11 chars, numeric  
    RequestID:         "unique-req-id",  // Required: max 255 chars, unique per request
    ChannelType:       "BINA",           // Required: max 50 chars
    TransactionDate:   "2024/03/26 10:30:00", // Optional: YYYY/MM/DD HH:MM:SS
    AdditionalData:    "",               // Optional: max 255 chars
}

// Validate before sending
if err := ottopay.ValidateInquiryRequest(req); err != nil {
    return err
}

resp, err := client.Inquiry(ctx, req)
if err != nil {
    return err
}

// Check response status
if resp.IsInquirySuccess() {
    fmt.Printf("Customer: %s, Amount: %s %s\n", 
        resp.CustomerName, resp.TotalAmount, resp.CurrencyCode)
}
```

### Payment Processing

```go
req := &ottopay.PaymentRequest{
    CompanyCode:     "12173",
    CustomerNumber:  "***********", 
    RequestID:       "unique-payment-id",
    ChannelType:     "BINA",
    CustomerName:    "John Doe",          // Required: max 30 chars
    CurrencyCode:    ottopay.CurrencyIDR, // IDR or USD
    PaidAmount:      "150000.00",         // Required: max 15 chars
    TotalAmount:     "150000.00",         // Required: max 15 chars
    Reference:       "payment-ref-123",   // Required: max 255 chars
    SubCompany:      "00000",             // Optional: defaults to "00000"
    TransactionDate: "2024/03/26 10:35:00",
    DetailBills:     []interface{}{},     // Optional: defaults to empty array
    AdditionalData:  "",
}

// Validate before sending
if err := ottopay.ValidatePaymentRequest(req); err != nil {
    return err
}

resp, err := client.Payment(ctx, req)
if err != nil {
    return err
}

// Handle different payment statuses
switch {
case resp.IsPaymentSuccess():
    fmt.Println("Payment successful!")
case resp.IsPaymentFailed():
    fmt.Printf("Payment failed: %s\n", resp.PaymentFlagReason.English)
case resp.IsPaymentTimeout():
    fmt.Println("Payment timed out")
}
```

## Error Handling

The package provides structured error handling:

```go
resp, err := client.Inquiry(ctx, req)
if err != nil {
    // Check if it's an API error
    if apiErr, ok := err.(*ottopay.Error); ok {
        fmt.Printf("API Error - Code: %d, Message: %s\n", apiErr.Code, apiErr.Message)
        
        // Handle specific error codes
        switch apiErr.Code {
        case 4042412:
            fmt.Println("Invalid bill")
        case 4012401:
            fmt.Println("Invalid token - will retry authentication")
        default:
            fmt.Printf("Unexpected API error: %s\n", apiErr.Message)
        }
    } else {
        // Network or other errors
        fmt.Printf("Request failed: %v\n", err)
    }
    return
}

// Check business logic success
if !resp.IsInquirySuccess() {
    fmt.Printf("Inquiry failed: %s\n", resp.InquiryReason.English)
}
```

## Constants and Helpers

```go
// Currency constants
ottopay.CurrencyIDR // "IDR"
ottopay.CurrencyUSD // "USD"

// Status constants
ottopay.InquiryStatusSuccess // "00"
ottopay.InquiryStatusFailed  // "01"

ottopay.PaymentStatusSuccess // "00" 
ottopay.PaymentStatusFailed  // "01"
ottopay.PaymentStatusTimeout // "02"

// Helper methods
resp.IsInquirySuccess()  // bool
resp.IsPaymentSuccess()  // bool
resp.IsPaymentFailed()   // bool
resp.IsPaymentTimeout()  // bool

// Validation functions
ottopay.ValidateInquiryRequest(req) // error
ottopay.ValidatePaymentRequest(req) // error
```

## Testing

The package is designed to be easily testable. You can mock the HTTP client:

```go
package main

import (
    "testing"
    "github.com/yourusername/ottopay-go"
)

func TestOttoPayIntegration(t *testing.T) {
    // Create mock HTTP client
    mockHTTP := &MockHTTPClient{}
    
    // Set up expected responses
    mockHTTP.SetResponse("/token", 200, ottopay.GetTokenResponse{
        Meta: ottopay.Meta{Status: true, Code: 200, Message: "success"},
        Data: ottopay.GetTokenData{IDToken: "test-token"},
    })
    
    // Create client with mock
    client := ottopay.NewClient("test-key", "test-user", "test-pass",
        ottopay.WithBaseURL("https://test.api"),
        ottopay.WithHTTPClient(mockHTTP),
    )
    
    // Test your logic
    ctx := context.Background()
    resp, err := client.GetToken(ctx, nil)
    
    if err != nil {
        t.Fatalf("Expected no error, got: %v", err)
    }
    
    if resp.Meta.Code != 200 {
        t.Errorf("Expected success response")
    }
}
```

## Complete Example with Error Handling

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/yourusername/ottopay-go"
)

func processVirtualAccountPayment(
    client ottopay.Client,
    companyCode, customerNumber, requestID string,
) error {
    ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
    defer cancel()
    
    // Step 1: Inquiry
    inquiryReq := &ottopay.InquiryRequest{
        CompanyCode:    companyCode,
        CustomerNumber: customerNumber,
        RequestID:      requestID,
        ChannelType:    "BINA",
    }
    
    if err := ottopay.ValidateInquiryRequest(inquiryReq); err != nil {
        return fmt.Errorf("invalid inquiry request: %w", err)
    }
    
    inquiryResp, err := client.Inquiry(ctx, inquiryReq)
    if err != nil {
        return fmt.Errorf("inquiry failed: %w", err)
    }
    
    if !inquiryResp.IsInquirySuccess() {
        return fmt.Errorf("customer not found: %s", inquiryResp.InquiryReason.English)
    }
    
    fmt.Printf("Found customer: %s\n", inquiryResp.CustomerName)
    fmt.Printf("Amount due: %s %s\n", inquiryResp.TotalAmount, inquiryResp.CurrencyCode)
    
    // Step 2: Payment
    paymentReq := &ottopay.PaymentRequest{
        CompanyCode:    inquiryResp.CompanyCode,
        CustomerNumber: inquiryResp.CustomerNumber,
        RequestID:      requestID + "-PAY",
        ChannelType:    "BINA",
        CustomerName:   inquiryResp.CustomerName,
        CurrencyCode:   inquiryResp.CurrencyCode,
        PaidAmount:     inquiryResp.TotalAmount,
        TotalAmount:    inquiryResp.TotalAmount,
        Reference:      "REF-" + time.Now().Format("20060102150405"),
    }
    
    if err := ottopay.ValidatePaymentRequest(paymentReq); err != nil {
        return fmt.Errorf("invalid payment request: %w", err)
    }
    
    paymentResp, err := client.Payment(ctx, paymentReq)
    if err != nil {
        return fmt.Errorf("payment failed: %w", err)
    }
    
    switch {
    case paymentResp.IsPaymentSuccess():
        fmt.Printf("✅ Payment successful! Amount: %s %s\n", 
            paymentResp.PaidAmount, paymentResp.CurrencyCode)
        return nil
    case paymentResp.IsPaymentFailed():
        return fmt.Errorf("payment rejected: %s", paymentResp.PaymentFlagReason.English)
    case paymentResp.IsPaymentTimeout():
        return fmt.Errorf("payment timed out")
    default:
        return fmt.Errorf("unknown payment status: %s", paymentResp.PaymentFlagStatus)
    }
}

func main() {
    client := ottopay.NewClient(
        "your-api-key",
        "your-username",
        "your-password",
        ottopay.WithBaseURL("https://api.ottopay.id"),
        ottopay.WithTimeout(30*time.Second),
    )
    
    if err := processVirtualAccountPayment(client, "12173", "***********", "REQ-001"); err != nil {
        log.Fatal("Payment processing failed:", err)
    }
}
```

## Response Codes

The package includes constants for common response codes as documented in the OttoPay API:

| Service | Code | Description |
|---------|------|-------------|
| Inquiry | 2002400 | Success Inquiry |
| Inquiry | 4042412 | Invalid Bill |
| Inquiry | 4012401 | Invalid Token |
| Inquiry | 4092401 | Duplicate Partner Reference Number |
| Payment | 2002500 | Success Payment |
| Payment | 4042512 | Invalid Bill |
| Payment | 4032500 | Transaction Expired |
| Payment | 4092501 | Duplicate Partner Reference Number (payment 2x) |
| Payment | 4042513 | Invalid Amount |
| Payment | 4042514 | Paid Bill |

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Run tests (`go test ./...`)
4. Commit your changes (`git commit -am 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the [OttoPay API documentation](http://www.ottopay.id)
- Contact OttoPay support for API-specific questions

---

**Note**: This is an unofficial client library. Please refer to the official OttoPay documentation for the most up-to-date API specifications.