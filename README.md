# OttoPay Virtual Account Non Billing Integration

[![CI/CD Pipeline](https://github.com/your-org/ottopay/actions/workflows/ci.yml/badge.svg)](https://github.com/your-org/ottopay/actions/workflows/ci.yml)
[![Go Report Card](https://goreportcard.com/badge/github.com/your-org/ottopay)](https://goreportcard.com/report/github.com/your-org/ottopay)
[![codecov](https://codecov.io/gh/your-org/ottopay/branch/main/graph/badge.svg)](https://codecov.io/gh/your-org/ottopay)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A robust, production-ready Go implementation for OttoPay Virtual Account Non Billing integration, built with clean architecture principles and comprehensive testing.

## 🚀 Features

- **Clean Architecture**: Follows clean architecture principles with clear separation of concerns
- **OttoPay API Integration**: Full compliance with OttoPay Virtual Account Non Billing v1.1.2 specification
- **Comprehensive Testing**: Unit tests, integration tests, and test utilities with high coverage
- **Security First**: Token-based authentication, input validation, and audit logging
- **Production Ready**: Docker support, CI/CD pipelines, monitoring, and observability
- **Developer Friendly**: Extensive documentation, examples, and development tools

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [API Documentation](#api-documentation)
- [Configuration](#configuration)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Monitoring](#monitoring)
- [Contributing](#contributing)
- [License](#license)

## 🏃 Quick Start

### Prerequisites

- Go 1.21 or later
- PostgreSQL 15+
- Redis 7+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/ottopay.git
   cd ottopay
   ```

2. **Install dependencies**
   ```bash
   make deps
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application**
   ```bash
   make dev
   ```

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build Docker image
make docker-build
make docker-run
```

## 🏗️ Architecture

This project follows **Clean Architecture** principles with the following layers:

```
├── cmd/                    # Application entry points
├── internal/
│   ├── domain/            # Domain layer (entities, value objects, interfaces)
│   ├── usecases/          # Use cases layer (business logic)
│   ├── infrastructure/    # Infrastructure layer (external services, databases)
│   ├── interfaces/        # Interface layer (HTTP handlers, middleware)
│   └── utils/            # Shared utilities
├── examples/              # Usage examples
├── docs/                  # Documentation
└── tests/                # Integration tests
```

### Key Components

- **Domain Entities**: Customer, Payment, Token, Transaction
- **Value Objects**: Amount, CompanyCode, CustomerNumber, RequestID, CurrencyCode
- **Use Cases**: Authentication, Customer Inquiry, Payment Processing
- **Infrastructure**: OttoPay API adapter, PostgreSQL repositories, Redis cache
- **Interfaces**: REST API handlers, middleware, validation

## 📚 API Documentation

### Authentication

```bash
# Get authentication token
POST /auth/token
{
  "username": "your_username",
  "password": "your_password"
}
```

### Customer Inquiry

```bash
# Inquire customer billing information
POST /api/v1/inquiry
Authorization: Bearer <token>
{
  "companyCode": "12345",
  "customerNumber": "*********0",
  "requestId": "INQ-20240101-001",
  "channelType": "API"
}
```

### Payment Processing

```bash
# Process customer payment
POST /api/v1/payment
Authorization: Bearer <token>
{
  "companyCode": "12345",
  "customerNumber": "*********0",
  "requestId": "PAY-20240101-001",
  "customerName": "John Doe",
  "currencyCode": "IDR",
  "paidAmount": "100000.50",
  "totalAmount": "100000.50",
  "reference": "REF-*********",
  "channelType": "API"
}
```

For complete API documentation, see [API Documentation](docs/api.md).

## ⚙️ Configuration

The application uses environment variables for configuration. Copy `.env.example` to `.env` and modify as needed:

```bash
# Core configuration
APP_ENV=development
SERVER_PORT=8080
LOG_LEVEL=info

# Database
DATABASE_URL=postgres://user:pass@localhost:5432/ottopay

# OttoPay API
OTTOPAY_API_URL=https://api.ottopay.example.com
OTTOPAY_USERNAME=your_username
OTTOPAY_PASSWORD=your_password
OTTOPAY_API_KEY=your_api_key

# Security
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

For all configuration options, see [Configuration Guide](docs/configuration.md).

## 🛠️ Development

### Available Make Commands

```bash
make help           # Show all available commands
make deps           # Download dependencies
make fmt            # Format code
make lint           # Run linter
make test           # Run all tests
make test-coverage  # Run tests with coverage
make build          # Build application
make dev            # Run development server
make clean          # Clean build artifacts
```

### Code Quality

The project enforces high code quality standards:

- **Linting**: golangci-lint with comprehensive rules
- **Testing**: Minimum 80% test coverage
- **Security**: gosec security scanning
- **Formatting**: gofmt and goimports
- **Documentation**: godoc for all public APIs

### Development Workflow

1. Create a feature branch
2. Make changes with tests
3. Run quality checks: `make quality`
4. Submit pull request
5. CI/CD pipeline validates changes

## 🧪 Testing

### Running Tests

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific test suite
go test ./internal/domain/entities -v

# Run integration tests
make test-integration
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Mock Infrastructure**: Comprehensive mocks for external dependencies
- **Test Fixtures**: Reusable test data and scenarios

### Example Test

```go
func TestCustomerInquiry(t *testing.T) {
    fixtures := testutils.NewTestFixtures()
    mockService := testutils.NewMockOttoPayService()
    
    useCase := inquiry.NewCustomerInquiryUseCase(nil, mockService, nil, nil)
    
    req := &inquiry.CustomerInquiryRequest{
        CompanyCode:    fixtures.CreateValidCompanyCode(),
        CustomerNumber: fixtures.CreateValidCustomerNumber(),
        RequestID:      fixtures.CreateValidRequestID(),
        ChannelType:    "API",
    }
    
    resp, err := useCase.Execute(context.Background(), req)
    
    assert.NoError(t, err)
    assert.True(t, resp.Success)
}
```

## 🚀 Deployment

### Docker Deployment

```bash
# Build image
docker build -t ottopay:latest .

# Run container
docker run -p 8080:8080 --env-file .env ottopay:latest

# Use Docker Compose
docker-compose up -d
```

### Kubernetes Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -l app=ottopay
```

### Production Considerations

- Use environment-specific configurations
- Enable TLS/SSL encryption
- Configure proper logging and monitoring
- Set up database migrations
- Configure backup strategies
- Implement health checks and readiness probes

## 📊 Monitoring

### Health Checks

```bash
# Application health
GET /health

# Readiness check
GET /ready

# Metrics (Prometheus format)
GET /metrics
```

### Observability Stack

- **Metrics**: Prometheus + Grafana
- **Logging**: Structured JSON logging
- **Tracing**: Jaeger distributed tracing
- **Alerting**: Prometheus Alertmanager

### Key Metrics

- Request rate and latency
- Error rates by endpoint
- Database connection pool metrics
- OttoPay API response times
- Authentication success/failure rates

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards

- Follow Go best practices
- Write comprehensive tests
- Document public APIs
- Use meaningful commit messages
- Keep pull requests focused

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Examples**: [examples/](examples/)
- **Issues**: [GitHub Issues](https://github.com/your-org/ottopay/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/ottopay/discussions)

## 🙏 Acknowledgments

- OttoPay for the Virtual Account Non Billing API specification
- The Go community for excellent tools and libraries
- Contributors who help improve this project

---

**Built with ❤️ using Go and Clean Architecture principles**
