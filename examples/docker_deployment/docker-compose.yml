version: '3.8'

services:
  # OttoPay API Service
  ottopay-api:
    build:
      context: ../../
      dockerfile: examples/docker_deployment/Dockerfile
    container_name: ottopay-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Server Configuration
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      - SERVER_READ_TIMEOUT=30s
      - SERVER_WRITE_TIMEOUT=30s
      - SERVER_IDLE_TIMEOUT=60s
      
      # OttoPay API Configuration
      - OTTOPAY_BASE_URL=${OTTOPAY_BASE_URL:-https://api.ottopay.example.com}
      - OTTOPAY_USERNAME=${OTTOPAY_USERNAME:-demo_user}
      - OTTOPAY_PASSWORD=${OTTOPAY_PASSWORD:-demo_password}
      - OTTOPAY_API_KEY=${OTTOPAY_API_KEY:-demo_api_key}
      - OTTOPAY_TIMEOUT=30s
      - OTTOPAY_MAX_RETRIES=3
      
      # Database Configuration (if using external DB)
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_NAME=${DB_NAME:-ottopay}
      - DB_USER=${DB_USER:-ottopay}
      - DB_PASSWORD=${DB_PASSWORD:-ottopay123}
      - DB_SSL_MODE=${DB_SSL_MODE:-disable}
      - DB_MAX_OPEN_CONNS=25
      - DB_MAX_IDLE_CONNS=5
      - DB_CONN_MAX_LIFETIME=5m
      
      # Redis Configuration (for caching/sessions)
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_POOL_SIZE=10
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      - LOG_OUTPUT=${LOG_OUTPUT:-stdout}
      
      # Security Configuration
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - JWT_EXPIRY=${JWT_EXPIRY:-24h}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-*}
      
      # Monitoring Configuration
      - METRICS_ENABLED=${METRICS_ENABLED:-true}
      - METRICS_PORT=9090
      - HEALTH_CHECK_ENABLED=true
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    networks:
      - ottopay-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ottopay-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME:-ottopay}
      - POSTGRES_USER=${DB_USER:-ottopay}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-ottopay123}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - ottopay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-ottopay} -d ${DB_NAME:-ottopay}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ottopay-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ottopay-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ottopay-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ottopay-network
    depends_on:
      - ottopay-api
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: ottopay-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ottopay-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: ottopay-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - ottopay-network
    depends_on:
      - prometheus

  # Log Aggregation (ELK Stack - Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ottopay-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ottopay-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana Dashboard
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: ottopay-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - ottopay-network
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: ottopay-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config:/usr/share/logstash/config:ro
      - ./logs:/app/logs:ro
    ports:
      - "5044:5044"
    networks:
      - ottopay-network
    depends_on:
      - elasticsearch

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  ottopay-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
