# Multi-stage build for OttoPay service
FROM golang:1.23-alpine AS builder

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ottopay-service ./cmd/server

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S ottopay && \
    adduser -u 1001 -S ottopay -G ottopay

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/ottopay-service .

# Copy configuration files
COPY --from=builder /app/configs/ ./configs/

# Change ownership to non-root user
RUN chown -R ottopay:ottopay /app

# Switch to non-root user
USER ottopay

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./ottopay-service"]
