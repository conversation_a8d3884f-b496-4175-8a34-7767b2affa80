# OttoPay Docker Deployment

Complete Docker deployment setup for the OttoPay Virtual Account Non Billing system with full monitoring, logging, and production-ready configuration.

## Architecture

This deployment includes:

- **OttoPay API Service**: Main application server
- **PostgreSQL**: Primary database for persistent storage
- **Redis**: Caching and session storage
- **Nginx**: Reverse proxy and load balancer
- **Prometheus**: Metrics collection and monitoring
- **<PERSON>ana**: Monitoring dashboards and visualization
- **ELK Stack**: Centralized logging (Elasticsearch, Logstash, Kibana)

## Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available
- 10GB free disk space

### 1. <PERSON>lone and Setup

```bash
cd examples/docker_deployment
cp .env.example .env
```

### 2. Configure Environment

Edit the `.env` file with your settings:

```bash
# OttoPay API Configuration
OTTOPAY_BASE_URL=https://api.ottopay.production.com
OTTOPAY_USERNAME=your_username
OTTOPAY_PASSWORD=your_password
OTTOPAY_API_KEY=your_api_key

# Database Configuration
DB_PASSWORD=secure_password_here
REDIS_PASSWORD=redis_password_here

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this

# Monitoring
GRAFANA_PASSWORD=admin123
```

### 3. Start Services

```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f ottopay-api
```

### 4. Verify Deployment

```bash
# Health check
curl http://localhost/health

# API test
curl -X POST http://localhost/api/v1/auth/token \
  -H "Content-Type: application/json" \
  -d '{"username":"demo_user","password":"demo_password"}'
```

## Services Overview

### OttoPay API (Port 8080)

Main application service providing:
- RESTful API endpoints
- Authentication and authorization
- Payment processing
- Customer inquiry
- Audit logging

**Endpoints:**
- `GET /health` - Health check
- `POST /api/v1/auth/token` - Authentication
- `POST /api/v1/inquiry` - Customer inquiry
- `POST /api/v1/payment` - Payment processing

### PostgreSQL Database (Port 5432)

Primary database for:
- Customer data
- Payment records
- Transaction history
- Audit logs
- User accounts

**Default Credentials:**
- Database: `ottopay`
- Username: `ottopay`
- Password: Set in `.env` file

### Redis Cache (Port 6379)

Caching layer for:
- Session storage
- Authentication tokens
- Temporary data
- Rate limiting

### Nginx Reverse Proxy (Ports 80/443)

Load balancer and reverse proxy providing:
- SSL termination
- Request routing
- Static file serving
- Security headers
- Rate limiting

### Monitoring Stack

#### Prometheus (Port 9090)
Metrics collection and alerting:
- Application metrics
- System metrics
- Custom business metrics
- Alert rules

#### Grafana (Port 3000)
Visualization and dashboards:
- System performance
- Application metrics
- Business intelligence
- Custom dashboards

**Default Credentials:**
- Username: `admin`
- Password: Set in `.env` file

### Logging Stack (ELK)

#### Elasticsearch (Port 9200)
Log storage and search:
- Centralized log storage
- Full-text search
- Log aggregation
- Data retention

#### Kibana (Port 5601)
Log visualization:
- Log analysis
- Search interface
- Custom dashboards
- Alert management

#### Logstash (Port 5044)
Log processing:
- Log parsing
- Data transformation
- Multiple input sources
- Output routing

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OTTOPAY_BASE_URL` | OttoPay API endpoint | `https://api.ottopay.example.com` |
| `OTTOPAY_USERNAME` | API username | `demo_user` |
| `OTTOPAY_PASSWORD` | API password | `demo_password` |
| `OTTOPAY_API_KEY` | API key | `demo_api_key` |
| `DB_PASSWORD` | Database password | `ottopay123` |
| `REDIS_PASSWORD` | Redis password | (empty) |
| `JWT_SECRET` | JWT signing key | `your-super-secret-jwt-key` |
| `LOG_LEVEL` | Logging level | `info` |
| `GRAFANA_PASSWORD` | Grafana admin password | `admin123` |

### Custom Configuration Files

#### Nginx Configuration
```bash
# Edit nginx configuration
vim nginx/conf.d/ottopay.conf
```

#### Prometheus Configuration
```bash
# Edit monitoring configuration
vim monitoring/prometheus.yml
```

#### Grafana Dashboards
```bash
# Add custom dashboards
cp your-dashboard.json monitoring/grafana/dashboards/
```

## Production Deployment

### Security Hardening

1. **Change Default Passwords**
```bash
# Generate secure passwords
openssl rand -base64 32  # For JWT_SECRET
openssl rand -base64 16  # For DB_PASSWORD
```

2. **SSL Configuration**
```bash
# Add SSL certificates
mkdir -p ssl
cp your-cert.pem ssl/
cp your-key.pem ssl/
```

3. **Firewall Rules**
```bash
# Only expose necessary ports
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 5432/tcp  # Block direct DB access
```

### Resource Limits

Edit `docker-compose.yml` to add resource limits:

```yaml
services:
  ottopay-api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### Backup Strategy

#### Database Backup
```bash
# Create backup script
cat > backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T postgres pg_dump -U ottopay ottopay > backup_${DATE}.sql
gzip backup_${DATE}.sql
EOF

chmod +x backup-db.sh

# Schedule with cron
echo "0 2 * * * /path/to/backup-db.sh" | crontab -
```

#### Volume Backup
```bash
# Backup all volumes
docker run --rm -v ottopay_postgres_data:/data -v $(pwd):/backup \
  alpine tar czf /backup/postgres_backup.tar.gz -C /data .
```

## Monitoring and Alerting

### Grafana Dashboards

Access Grafana at `http://localhost:3000`:

1. **System Overview**: CPU, memory, disk usage
2. **Application Metrics**: Request rates, response times, errors
3. **Business Metrics**: Payment volumes, success rates
4. **Database Performance**: Query performance, connections

### Prometheus Alerts

Configure alerts in `monitoring/prometheus.yml`:

```yaml
rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Log Analysis

Access Kibana at `http://localhost:5601`:

1. **Error Tracking**: Monitor application errors
2. **Performance Analysis**: Slow query detection
3. **Security Monitoring**: Failed authentication attempts
4. **Business Intelligence**: Payment patterns

## Scaling

### Horizontal Scaling

Scale the API service:

```bash
# Scale to 3 instances
docker-compose up -d --scale ottopay-api=3

# Update nginx upstream configuration
vim nginx/conf.d/ottopay.conf
```

### Load Balancing

Configure nginx upstream:

```nginx
upstream ottopay_backend {
    server ottopay-api_1:8080;
    server ottopay-api_2:8080;
    server ottopay-api_3:8080;
}
```

### Database Scaling

For high-load scenarios:

1. **Read Replicas**: Add PostgreSQL read replicas
2. **Connection Pooling**: Use PgBouncer
3. **Sharding**: Implement database sharding

## Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose logs ottopay-api

# Check resource usage
docker stats

# Restart specific service
docker-compose restart ottopay-api
```

#### Database Connection Issues
```bash
# Test database connectivity
docker-compose exec ottopay-api nc -zv postgres 5432

# Check database logs
docker-compose logs postgres
```

#### Memory Issues
```bash
# Check memory usage
docker stats --no-stream

# Increase memory limits
vim docker-compose.yml  # Add memory limits
```

### Health Checks

All services include health checks:

```bash
# Check all service health
docker-compose ps

# Manual health check
curl http://localhost/health
```

### Log Analysis

```bash
# Application logs
docker-compose logs -f ottopay-api

# Database logs
docker-compose logs -f postgres

# All logs
docker-compose logs -f
```

## Maintenance

### Updates

```bash
# Pull latest images
docker-compose pull

# Restart with new images
docker-compose up -d

# Clean old images
docker image prune -f
```

### Cleanup

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: Data loss)
docker-compose down -v

# Clean system
docker system prune -f
```

### Backup and Restore

```bash
# Backup
./scripts/backup.sh

# Restore
./scripts/restore.sh backup_20240101_120000.tar.gz
```

## Development Mode

For development with hot reload:

```bash
# Use development compose file
docker-compose -f docker-compose.dev.yml up -d

# Mount source code
volumes:
  - ../../:/app
```

## Support

For deployment issues:

1. Check service logs: `docker-compose logs [service]`
2. Verify configuration: `docker-compose config`
3. Test connectivity: Health check endpoints
4. Monitor resources: `docker stats`
5. Review documentation: Service-specific READMEs
