#!/bin/bash

# Test script for OttoPay examples
set -e

echo "=== Testing OttoPay Examples ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
        exit 1
    fi
}

print_info() {
    echo -e "${YELLOW}→ $1${NC}"
}

# Test 1: Basic Usage Example
print_info "Testing Basic Usage Example"
cd basic_usage
go build -o basic-usage main.go
print_status $? "Basic usage example compiles"

./basic-usage > /dev/null
print_status $? "Basic usage example runs"
cd ..

# Test 2: CLI Tool Example
print_info "Testing CLI Tool Example"
cd cli_tool
go build -o ottopay-cli main.go
print_status $? "CLI tool compiles"

./ottopay-cli version > /dev/null
print_status $? "CLI tool version command works"

./ottopay-cli auth -username demo -password demo > /dev/null
print_status $? "CLI tool auth command works"

./ottopay-cli validate -type company -value 12173 > /dev/null
print_status $? "CLI tool validate command works"
cd ..

# Test 3: Fiber Integration Example
print_info "Testing Fiber Integration Example"
cd fiber_integration
go build -o fiber-app main.go
print_status $? "Fiber integration compiles"

# Start the server in background and test it
./fiber-app &
SERVER_PID=$!
sleep 2

# Test health endpoint
curl -s http://localhost:8080/api/v1/health > /dev/null
HEALTH_STATUS=$?

# Test auth endpoint
curl -s -X POST http://localhost:8080/api/v1/auth/token \
  -H "Content-Type: application/json" \
  -d '{"username":"demo","password":"demo"}' > /dev/null
AUTH_STATUS=$?

# Test inquiry endpoint
curl -s -X POST http://localhost:8080/api/v1/inquiry \
  -H "Content-Type: application/json" \
  -d '{"company_code":"12173","customer_number":"56751590099","channel_type":"API"}' > /dev/null
INQUIRY_STATUS=$?

# Test payment endpoint
curl -s -X POST http://localhost:8080/api/v1/payment \
  -H "Content-Type: application/json" \
  -d '{"company_code":"12173","customer_number":"56751590099","customer_name":"Test","currency_code":"IDR","paid_amount":150000,"total_amount":150000,"reference":"REF123","channel_type":"API"}' > /dev/null
PAYMENT_STATUS=$?

# Kill the server
kill $SERVER_PID 2>/dev/null || true
wait $SERVER_PID 2>/dev/null || true

print_status $HEALTH_STATUS "Fiber health endpoint works"
print_status $AUTH_STATUS "Fiber auth endpoint works"
print_status $INQUIRY_STATUS "Fiber inquiry endpoint works"
print_status $PAYMENT_STATUS "Fiber payment endpoint works"
cd ..

# Test 4: Simple Payment Example (if exists)
if [ -d "simple_payment" ]; then
    print_info "Testing Simple Payment Example"
    cd simple_payment
    go build -o simple-payment main.go
    print_status $? "Simple payment example compiles"
    
    ./simple-payment > /dev/null
    print_status $? "Simple payment example runs"
    cd ..
fi

echo ""
echo -e "${GREEN}=== All Examples Tests Passed! ===${NC}"
echo ""
echo "Available examples:"
echo "  1. basic_usage/        - Mock demonstration of intended API usage"
echo "  2. cli_tool/          - Command-line interface with multiple output formats"
echo "  3. fiber_integration/ - REST API server using Fiber v2 framework"
if [ -d "simple_payment" ]; then
    echo "  4. simple_payment/    - Basic payment processing example"
fi
echo ""
echo "To run examples:"
echo "  cd basic_usage && ./basic-usage"
echo "  cd cli_tool && ./ottopay-cli help"
echo "  cd fiber_integration && ./fiber-app"
echo ""
