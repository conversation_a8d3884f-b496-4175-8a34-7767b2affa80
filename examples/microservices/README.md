# OttoPay Microservices Architecture Example

This example demonstrates how to decompose the OttoPay system into microservices for scalability, maintainability, and independent deployment.

## Architecture Overview

The OttoPay system is split into the following microservices:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Load Balancer  │    │   Web Client    │
│     (Nginx)     │◄───┤     (HAProxy)   │◄───┤    (React)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Auth Service    │    │ Inquiry Service │    │ Payment Service │
│   (Port 8081)   │    │   (Port 8082)   │    │   (Port 8083)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Token Store   │    │  Customer DB    │    │  Payment DB     │
│    (Redis)      │    │ (PostgreSQL)    │    │ (PostgreSQL)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │  Audit Service  │
                    │   (Port 8084)   │
                    └─────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │   Audit DB      │
                    │ (PostgreSQL)    │
                    └─────────────────┘
```

## Services

### 1. Authentication Service (Port 8081)

**Responsibilities:**
- User authentication and authorization
- JWT token generation and validation
- Token refresh and revocation
- User session management

**Endpoints:**
- `POST /auth/login` - User login
- `POST /auth/token` - Get access token
- `POST /auth/refresh` - Refresh token
- `DELETE /auth/logout` - User logout
- `GET /auth/validate` - Validate token

**Database:** Redis (token storage)

### 2. Customer Inquiry Service (Port 8082)

**Responsibilities:**
- Customer information retrieval
- Account balance inquiries
- Customer validation
- OttoPay API integration for inquiries

**Endpoints:**
- `GET /customers/{company_code}/{customer_number}` - Get customer info
- `POST /inquiries` - Perform customer inquiry
- `GET /inquiries/{request_id}` - Get inquiry status

**Database:** PostgreSQL (customer cache, inquiry history)

### 3. Payment Processing Service (Port 8083)

**Responsibilities:**
- Payment transaction processing
- Payment validation and verification
- OttoPay API integration for payments
- Transaction state management

**Endpoints:**
- `POST /payments` - Process payment
- `GET /payments/{payment_id}` - Get payment status
- `PUT /payments/{payment_id}/status` - Update payment status
- `GET /payments/customer/{company_code}/{customer_number}` - Get customer payments

**Database:** PostgreSQL (payment records, transaction history)

### 4. Audit Service (Port 8084)

**Responsibilities:**
- Centralized audit logging
- Event tracking and monitoring
- Compliance reporting
- Log aggregation from all services

**Endpoints:**
- `POST /audit/events` - Log audit event
- `GET /audit/events` - Query audit events
- `GET /audit/reports` - Generate audit reports

**Database:** PostgreSQL (audit logs, events)

## Service Communication

### Synchronous Communication
- **HTTP/REST**: Direct service-to-service calls
- **gRPC**: High-performance internal communication
- **Circuit Breaker**: Resilience patterns

### Asynchronous Communication
- **Message Queue**: RabbitMQ for event-driven communication
- **Event Sourcing**: Domain events for state changes
- **Pub/Sub**: Redis for real-time notifications

## Implementation

### Service Structure

Each service follows the same structure:

```
service-name/
├── cmd/
│   └── server/
│       └── main.go
├── internal/
│   ├── api/
│   │   ├── handlers/
│   │   ├── middleware/
│   │   └── routes/
│   ├── domain/
│   │   ├── entities/
│   │   ├── repositories/
│   │   └── services/
│   ├── infrastructure/
│   │   ├── database/
│   │   ├── messaging/
│   │   └── external/
│   └── config/
├── pkg/
│   ├── client/
│   └── proto/
├── deployments/
│   ├── docker/
│   └── k8s/
└── tests/
```

### Shared Libraries

Common functionality is extracted into shared libraries:

```
shared/
├── auth/           # Authentication utilities
├── logging/        # Structured logging
├── monitoring/     # Metrics and tracing
├── messaging/      # Message queue clients
├── database/       # Database utilities
├── validation/     # Request validation
└── errors/         # Error handling
```

## Development Setup

### Prerequisites

- Docker and Docker Compose
- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- RabbitMQ 3.12+

### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd examples/microservices

# Start infrastructure services
docker-compose -f docker-compose.infrastructure.yml up -d

# Start all microservices
docker-compose up -d

# Check service health
./scripts/health-check.sh
```

### Individual Service Development

```bash
# Start infrastructure only
docker-compose -f docker-compose.infrastructure.yml up -d

# Run auth service locally
cd auth-service
go run cmd/server/main.go

# Run inquiry service locally
cd inquiry-service
go run cmd/server/main.go

# Run payment service locally
cd payment-service
go run cmd/server/main.go

# Run audit service locally
cd audit-service
go run cmd/server/main.go
```

## Configuration

### Environment Variables

Each service uses environment variables for configuration:

```bash
# Common configuration
SERVICE_NAME=auth-service
SERVICE_PORT=8081
LOG_LEVEL=info
LOG_FORMAT=json

# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ottopay_auth
DB_USER=ottopay
DB_PASSWORD=password

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Message queue configuration
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Service discovery
CONSUL_URL=http://localhost:8500
ETCD_ENDPOINTS=http://localhost:2379

# Monitoring
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_ENDPOINT=http://localhost:9090
```

### Service Discovery

Services register themselves with Consul:

```go
// Service registration
func registerService(consul *api.Client, service *api.AgentServiceRegistration) error {
    return consul.Agent().ServiceRegister(service)
}

// Service discovery
func discoverService(consul *api.Client, serviceName string) ([]*api.ServiceEntry, error) {
    return consul.Health().Service(serviceName, "", true, nil)
}
```

## API Gateway

### Nginx Configuration

```nginx
upstream auth_service {
    server auth-service:8081;
}

upstream inquiry_service {
    server inquiry-service:8082;
}

upstream payment_service {
    server payment-service:8083;
}

upstream audit_service {
    server audit-service:8084;
}

server {
    listen 80;
    server_name api.ottopay.local;

    # Authentication endpoints
    location /api/v1/auth/ {
        proxy_pass http://auth_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Inquiry endpoints
    location /api/v1/inquiry/ {
        proxy_pass http://inquiry_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Require authentication
        auth_request /auth/validate;
    }

    # Payment endpoints
    location /api/v1/payment/ {
        proxy_pass http://payment_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Require authentication
        auth_request /auth/validate;
    }

    # Audit endpoints (admin only)
    location /api/v1/audit/ {
        proxy_pass http://audit_service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Require admin authentication
        auth_request /auth/validate-admin;
    }

    # Authentication validation
    location = /auth/validate {
        internal;
        proxy_pass http://auth_service/validate;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
    }
}
```

## Inter-Service Communication

### HTTP Client with Circuit Breaker

```go
type ServiceClient struct {
    client   *http.Client
    breaker  *gobreaker.CircuitBreaker
    baseURL  string
}

func (c *ServiceClient) Call(ctx context.Context, method, path string, body interface{}) (*http.Response, error) {
    return c.breaker.Execute(func() (interface{}, error) {
        req, err := c.buildRequest(ctx, method, path, body)
        if err != nil {
            return nil, err
        }
        
        return c.client.Do(req)
    })
}
```

### gRPC Communication

```protobuf
// auth.proto
service AuthService {
    rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
    rpc GetUserInfo(GetUserInfoRequest) returns (GetUserInfoResponse);
}

// inquiry.proto
service InquiryService {
    rpc InquireCustomer(InquireCustomerRequest) returns (InquireCustomerResponse);
}

// payment.proto
service PaymentService {
    rpc ProcessPayment(ProcessPaymentRequest) returns (ProcessPaymentResponse);
}
```

### Event-Driven Communication

```go
// Event publishing
type EventPublisher struct {
    channel *amqp.Channel
}

func (p *EventPublisher) PublishEvent(event DomainEvent) error {
    body, _ := json.Marshal(event)
    return p.channel.Publish(
        "ottopay.events", // exchange
        event.Type(),     // routing key
        false,           // mandatory
        false,           // immediate
        amqp.Publishing{
            ContentType: "application/json",
            Body:        body,
        },
    )
}

// Event handling
type EventHandler struct {
    handlers map[string]func(DomainEvent) error
}

func (h *EventHandler) Handle(eventType string, data []byte) error {
    if handler, exists := h.handlers[eventType]; exists {
        var event DomainEvent
        json.Unmarshal(data, &event)
        return handler(event)
    }
    return nil
}
```

## Monitoring and Observability

### Distributed Tracing

```go
// Jaeger tracing
func initTracing(serviceName string) io.Closer {
    cfg := jaegerconfig.Configuration{
        ServiceName: serviceName,
        Sampler: &jaegerconfig.SamplerConfig{
            Type:  jaeger.SamplerTypeConst,
            Param: 1,
        },
        Reporter: &jaegerconfig.ReporterConfig{
            LogSpans: true,
        },
    }
    
    tracer, closer, _ := cfg.NewTracer()
    opentracing.SetGlobalTracer(tracer)
    return closer
}
```

### Metrics Collection

```go
// Prometheus metrics
var (
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"service", "method", "endpoint", "status"},
    )
    
    requestCount = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"service", "method", "endpoint", "status"},
    )
)
```

### Health Checks

```go
// Health check endpoint
func healthHandler(w http.ResponseWriter, r *http.Request) {
    health := map[string]interface{}{
        "status":    "healthy",
        "timestamp": time.Now(),
        "service":   serviceName,
        "version":   version,
        "checks": map[string]string{
            "database": checkDatabase(),
            "redis":    checkRedis(),
            "rabbitmq": checkRabbitMQ(),
        },
    }
    
    json.NewEncoder(w).Encode(health)
}
```

## Testing

### Unit Testing

```go
func TestPaymentService_ProcessPayment(t *testing.T) {
    // Setup
    mockRepo := &MockPaymentRepository{}
    mockOttoPay := &MockOttoPayService{}
    service := NewPaymentService(mockRepo, mockOttoPay)
    
    // Test
    result, err := service.ProcessPayment(ctx, request)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, expected, result)
}
```

### Integration Testing

```go
func TestPaymentAPI_Integration(t *testing.T) {
    // Start test containers
    testDB := testcontainers.StartPostgreSQL(t)
    testRedis := testcontainers.StartRedis(t)
    
    // Setup service
    service := setupPaymentService(testDB, testRedis)
    
    // Test API endpoints
    response := httptest.NewRecorder()
    request := httptest.NewRequest("POST", "/payments", body)
    
    service.ServeHTTP(response, request)
    
    assert.Equal(t, http.StatusOK, response.Code)
}
```

### End-to-End Testing

```go
func TestOttoPay_E2E(t *testing.T) {
    // Start all services
    compose := testcontainers.NewDockerCompose("docker-compose.test.yml")
    compose.Up(t)
    defer compose.Down(t)
    
    // Test complete workflow
    token := authenticate(t)
    customer := inquireCustomer(t, token)
    payment := processPayment(t, token, customer)
    
    assert.NotNil(t, payment)
}
```

## Deployment

### Docker Compose

```yaml
version: '3.8'
services:
  auth-service:
    build: ./auth-service
    ports:
      - "8081:8081"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  inquiry-service:
    build: ./inquiry-service
    ports:
      - "8082:8082"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  payment-service:
    build: ./payment-service
    ports:
      - "8083:8083"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis

  audit-service:
    build: ./audit-service
    ports:
      - "8084:8084"
    environment:
      - DB_HOST=postgres
    depends_on:
      - postgres
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: ottopay/auth-service:latest
        ports:
        - containerPort: 8081
        env:
        - name: DB_HOST
          value: postgres-service
        - name: REDIS_HOST
          value: redis-service
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Security

### Service-to-Service Authentication

```go
// JWT-based service authentication
func serviceAuthMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        token := r.Header.Get("X-Service-Token")
        if !validateServiceToken(token) {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        next.ServeHTTP(w, r)
    })
}
```

### Network Security

```yaml
# Network policies for Kubernetes
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ottopay-network-policy
spec:
  podSelector:
    matchLabels:
      app: ottopay
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8080
```

## Benefits

### Scalability
- Independent scaling of services
- Resource optimization per service
- Horizontal scaling capabilities

### Maintainability
- Clear service boundaries
- Independent development teams
- Technology diversity

### Reliability
- Fault isolation
- Circuit breaker patterns
- Graceful degradation

### Deployment
- Independent deployments
- Blue-green deployments
- Canary releases

## Challenges and Solutions

### Data Consistency
- **Problem**: Distributed transactions
- **Solution**: Saga pattern, eventual consistency

### Service Discovery
- **Problem**: Dynamic service locations
- **Solution**: Consul, Kubernetes service discovery

### Configuration Management
- **Problem**: Distributed configuration
- **Solution**: Consul KV, Kubernetes ConfigMaps

### Monitoring Complexity
- **Problem**: Distributed tracing
- **Solution**: Jaeger, centralized logging

## Best Practices

1. **Single Responsibility**: Each service has one business capability
2. **Database per Service**: No shared databases
3. **API Versioning**: Backward compatibility
4. **Circuit Breakers**: Resilience patterns
5. **Centralized Logging**: Structured logging
6. **Health Checks**: Comprehensive monitoring
7. **Security**: Service-to-service authentication
8. **Testing**: Comprehensive test strategy
