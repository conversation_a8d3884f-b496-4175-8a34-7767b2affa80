package main

import (
	"context"
	"fmt"
	"log"
	"time"
)

// <PERSON><PERSON> types to demonstrate the intended API usage
type OttoPayClient struct {
	apiKey   string
	username string
	password string
	baseURL  string
	timeout  time.Duration
}

type GetTokenRequest struct {
	Username    string `json:"username,omitempty"`
	Password    string `json:"password,omitempty"`
	ChannelType string `json:"channel_type,omitempty"`
	SourceIP    string `json:"source_ip,omitempty"`
	UserAgent   string `json:"user_agent,omitempty"`
}

type GetTokenResponse struct {
	Success   bool          `json:"success"`
	Token     string        `json:"token,omitempty"`
	ExpiresIn time.Duration `json:"expires_in"`
	Message   string        `json:"message"`
	RequestID string        `json:"request_id"`
}

type InquiryRequest struct {
	CompanyCode    string `json:"company_code"`
	CustomerNumber string `json:"customer_number"`
	ChannelType    string `json:"channel_type"`
	Token          string `json:"token"`
	SourceIP       string `json:"source_ip,omitempty"`
	UserAgent      string `json:"user_agent,omitempty"`
}

type InquiryResponse struct {
	Success       bool   `json:"success"`
	InquiryStatus string `json:"inquiry_status"`
	Message       string `json:"message"`
	RequestID     string `json:"request_id"`
	ResponseTime  int64  `json:"response_time"`
	Customer      *struct {
		Name           string `json:"name"`
		CompanyCode    string `json:"company_code"`
		CustomerNumber string `json:"customer_number"`
		CurrencyCode   string `json:"currency_code"`
		TotalAmount    string `json:"total_amount"`
	} `json:"customer,omitempty"`
}

type PaymentRequest struct {
	CompanyCode    string `json:"company_code"`
	CustomerNumber string `json:"customer_number"`
	CustomerName   string `json:"customer_name"`
	CurrencyCode   string `json:"currency_code"`
	PaidAmount     string `json:"paid_amount"`
	TotalAmount    string `json:"total_amount"`
	Reference      string `json:"reference"`
	ChannelType    string `json:"channel_type"`
	Token          string `json:"token"`
	SourceIP       string `json:"source_ip,omitempty"`
	UserAgent      string `json:"user_agent,omitempty"`
}

type PaymentResponse struct {
	Success       bool   `json:"success"`
	Status        string `json:"status"`
	Message       string `json:"message"`
	RequestID     string `json:"request_id"`
	TransactionID string `json:"transaction_id"`
	ResponseTime  int64  `json:"response_time"`
	Payment       *struct {
		ID           string `json:"id"`
		CustomerName string `json:"customer_name"`
		PaidAmount   string `json:"paid_amount"`
		TotalAmount  string `json:"total_amount"`
		Reference    string `json:"reference"`
		Status       string `json:"status"`
	} `json:"payment,omitempty"`
}

// Mock client methods
func NewOttoPayClient(apiKey, username, password, baseURL string, timeout time.Duration) *OttoPayClient {
	return &OttoPayClient{
		apiKey:   apiKey,
		username: username,
		password: password,
		baseURL:  baseURL,
		timeout:  timeout,
	}
}

func (c *OttoPayClient) GetToken(ctx context.Context, req *GetTokenRequest) (*GetTokenResponse, error) {
	// Mock implementation - in real usage this would call the actual API
	return &GetTokenResponse{
		Success:   true,
		Token:     "mock_token_123456789",
		ExpiresIn: 24 * time.Hour,
		Message:   "Authentication successful",
		RequestID: "TOK202401011200000001",
	}, nil
}

func (c *OttoPayClient) Inquiry(ctx context.Context, req *InquiryRequest) (*InquiryResponse, error) {
	// Mock implementation - in real usage this would call the actual API
	return &InquiryResponse{
		Success:       true,
		InquiryStatus: "SUCCESS",
		Message:       "Customer inquiry completed successfully",
		RequestID:     "INQ202401011200000001",
		ResponseTime:  1250,
		Customer: &struct {
			Name           string `json:"name"`
			CompanyCode    string `json:"company_code"`
			CustomerNumber string `json:"customer_number"`
			CurrencyCode   string `json:"currency_code"`
			TotalAmount    string `json:"total_amount"`
		}{
			Name:           "Customer Virtual Account",
			CompanyCode:    req.CompanyCode,
			CustomerNumber: req.CustomerNumber,
			CurrencyCode:   "IDR",
			TotalAmount:    "150000.00",
		},
	}, nil
}

func (c *OttoPayClient) Payment(ctx context.Context, req *PaymentRequest) (*PaymentResponse, error) {
	// Mock implementation - in real usage this would call the actual API
	return &PaymentResponse{
		Success:       true,
		Status:        "SUCCESS",
		Message:       "Payment processed successfully",
		RequestID:     "PAY202401011200000001",
		TransactionID: "TXN202401011200000001",
		ResponseTime:  2100,
		Payment: &struct {
			ID           string `json:"id"`
			CustomerName string `json:"customer_name"`
			PaidAmount   string `json:"paid_amount"`
			TotalAmount  string `json:"total_amount"`
			Reference    string `json:"reference"`
			Status       string `json:"status"`
		}{
			ID:           "PAY202401011200000001",
			CustomerName: req.CustomerName,
			PaidAmount:   req.PaidAmount,
			TotalAmount:  req.TotalAmount,
			Reference:    req.Reference,
			Status:       "SUCCESS",
		},
	}, nil
}

func main() {
	fmt.Println("=== OttoPay Basic Usage Example ===")
	fmt.Println("Note: This is a mock example demonstrating the intended API usage")

	// Create OttoPay client
	client := NewOttoPayClient(
		"demo_api_key",
		"demo_user",
		"demo_password",
		"https://api.ottopay.example.com",
		30*time.Second,
	)

	ctx := context.Background()

	// Example 1: Get Authentication Token
	fmt.Println("\n=== Authentication Example ===")

	tokenReq := &GetTokenRequest{
		Username:    "demo_user",
		Password:    "demo_password",
		ChannelType: "API",
		SourceIP:    "127.0.0.1",
		UserAgent:   "OttoPay-Example/1.0",
	}

	tokenResp, err := client.GetToken(ctx, tokenReq)
	if err != nil {
		log.Printf("Authentication failed: %v", err)
	} else {
		fmt.Printf("Authentication Result:\n")
		fmt.Printf("  Success: %v\n", tokenResp.Success)
		fmt.Printf("  Message: %s\n", tokenResp.Message)
		fmt.Printf("  Request ID: %s\n", tokenResp.RequestID)
		fmt.Printf("  Token: %s\n", tokenResp.Token)
		fmt.Printf("  Expires In: %v\n", tokenResp.ExpiresIn)
	}

	// Example 2: Customer Inquiry
	fmt.Println("\n=== Customer Inquiry Example ===")

	inquiryReq := &InquiryRequest{
		CompanyCode:    "12173",
		CustomerNumber: "***********",
		ChannelType:    "API",
		Token:          tokenResp.Token, // Use token from authentication
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	inquiryResp, err := client.Inquiry(ctx, inquiryReq)
	if err != nil {
		log.Printf("Inquiry failed: %v", err)
	} else {
		fmt.Printf("Inquiry Result:\n")
		fmt.Printf("  Success: %v\n", inquiryResp.Success)
		fmt.Printf("  Status: %s\n", inquiryResp.InquiryStatus)
		fmt.Printf("  Message: %s\n", inquiryResp.Message)
		fmt.Printf("  Request ID: %s\n", inquiryResp.RequestID)
		fmt.Printf("  Response Time: %dms\n", inquiryResp.ResponseTime)

		if inquiryResp.Customer != nil {
			fmt.Printf("  Customer:\n")
			fmt.Printf("    Name: %s\n", inquiryResp.Customer.Name)
			fmt.Printf("    Company Code: %s\n", inquiryResp.Customer.CompanyCode)
			fmt.Printf("    Customer Number: %s\n", inquiryResp.Customer.CustomerNumber)
			fmt.Printf("    Currency: %s\n", inquiryResp.Customer.CurrencyCode)
			fmt.Printf("    Total Amount: %s\n", inquiryResp.Customer.TotalAmount)
		}
	}

	// Example 3: Payment Processing
	fmt.Println("\n=== Payment Processing Example ===")

	paymentReq := &PaymentRequest{
		CompanyCode:    "12173",
		CustomerNumber: "***********",
		CustomerName:   "Customer Virtual Account",
		CurrencyCode:   "IDR",
		PaidAmount:     "150000.00",
		TotalAmount:    "150000.00",
		Reference:      "REF123456789",
		ChannelType:    "API",
		Token:          tokenResp.Token, // Use token from authentication
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	paymentResp, err := client.Payment(ctx, paymentReq)
	if err != nil {
		log.Printf("Payment failed: %v", err)
	} else {
		fmt.Printf("Payment Result:\n")
		fmt.Printf("  Success: %v\n", paymentResp.Success)
		fmt.Printf("  Status: %s\n", paymentResp.Status)
		fmt.Printf("  Message: %s\n", paymentResp.Message)
		fmt.Printf("  Request ID: %s\n", paymentResp.RequestID)
		fmt.Printf("  Transaction ID: %s\n", paymentResp.TransactionID)
		fmt.Printf("  Response Time: %dms\n", paymentResp.ResponseTime)

		if paymentResp.Payment != nil {
			fmt.Printf("  Payment:\n")
			fmt.Printf("    ID: %s\n", paymentResp.Payment.ID)
			fmt.Printf("    Customer Name: %s\n", paymentResp.Payment.CustomerName)
			fmt.Printf("    Paid Amount: %s\n", paymentResp.Payment.PaidAmount)
			fmt.Printf("    Total Amount: %s\n", paymentResp.Payment.TotalAmount)
			fmt.Printf("    Reference: %s\n", paymentResp.Payment.Reference)
			fmt.Printf("    Status: %s\n", paymentResp.Payment.Status)
		}
	}

	// Example 4: Error Handling
	fmt.Println("\n=== Error Handling Example ===")

	// Try inquiry with invalid data to demonstrate error handling
	invalidInquiryReq := &InquiryRequest{
		CompanyCode:    "00000", // Invalid company code
		CustomerNumber: "0000000000",
		ChannelType:    "API",
		Token:          "invalid_token",
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	// In a real implementation, this would return an error or failed response
	fmt.Printf("Simulating error handling for invalid request:\n")
	fmt.Printf("  Company Code: %s (invalid)\n", invalidInquiryReq.CompanyCode)
	fmt.Printf("  Customer Number: %s (invalid)\n", invalidInquiryReq.CustomerNumber)
	fmt.Printf("  Token: %s (invalid)\n", invalidInquiryReq.Token)
	fmt.Printf("  Expected Result: Authentication failed or Customer not found\n")

	fmt.Println("\n=== OttoPay Basic Usage Example Completed ===")
	fmt.Println("\nNote: This example demonstrates the intended API structure.")
	fmt.Println("In a real implementation:")
	fmt.Println("  1. Replace mock client with actual OttoPay client")
	fmt.Println("  2. Handle real API responses and errors")
	fmt.Println("  3. Implement proper authentication token management")
	fmt.Println("  4. Add retry logic and error handling")
	fmt.Println("  5. Use environment variables for configuration")
}
