package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"repo.nusatek.id/sugeng/ottopay/pkg/ottopay"
)

func main() {
	// Initialize OttoPay client configuration
	config := &ottopay.Config{
		BaseURL:    "https://api.ottopay.example.com",
		APIKey:     "demo_api_key",
		Username:   "demo_user",
		Password:   "demo_password",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
	}

	// Create OttoPay client
	client, err := ottopay.NewClient(config)
	if err != nil {
		log.Fatalf("Failed to create OttoPay client: %v", err)
	}

	ctx := context.Background()

	// Example 1: Get Authentication Token
	fmt.Println("=== Authentication Example ===")
	
	tokenReq := &ottopay.GetTokenRequest{
		Username:    "demo_user",
		Password:    "demo_password",
		ChannelType: "API",
		SourceIP:    "127.0.0.1",
		UserAgent:   "OttoPay-Example/1.0",
	}

	tokenResp, err := client.GetToken(ctx, tokenReq)
	if err != nil {
		log.Printf("Authentication failed: %v", err)
	} else {
		fmt.Printf("Authentication Result:\n")
		fmt.Printf("  Success: %v\n", tokenResp.Success)
		fmt.Printf("  Message: %s\n", tokenResp.Message)
		fmt.Printf("  Request ID: %s\n", tokenResp.RequestID)
		if tokenResp.Token != nil {
			fmt.Printf("  Token ID: %s\n", tokenResp.Token.ID)
			fmt.Printf("  Expires In: %v\n", tokenResp.ExpiresIn)
		}
	}

	// Example 2: Customer Inquiry
	fmt.Println("\n=== Customer Inquiry Example ===")

	inquiryReq := &ottopay.InquiryRequest{
		CompanyCode:    "12173",
		CustomerNumber: "***********",
		ChannelType:    "API",
		Token:          "demo_token", // In real usage, use token from authentication
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	inquiryResp, err := client.Inquiry(ctx, inquiryReq)
	if err != nil {
		log.Printf("Inquiry failed: %v", err)
	} else {
		fmt.Printf("Inquiry Result:\n")
		fmt.Printf("  Success: %v\n", inquiryResp.Success)
		fmt.Printf("  Status: %s\n", inquiryResp.InquiryStatus)
		fmt.Printf("  Message: %s\n", inquiryResp.Message)
		fmt.Printf("  Request ID: %s\n", inquiryResp.RequestID)
		fmt.Printf("  Response Time: %dms\n", inquiryResp.ResponseTime)

		if inquiryResp.Customer != nil {
			fmt.Printf("  Customer:\n")
			fmt.Printf("    Name: %s\n", inquiryResp.Customer.Name)
			fmt.Printf("    Company Code: %s\n", inquiryResp.Customer.CompanyCode)
			fmt.Printf("    Customer Number: %s\n", inquiryResp.Customer.CustomerNumber)
			fmt.Printf("    Currency: %s\n", inquiryResp.Customer.CurrencyCode)
			fmt.Printf("    Total Amount: %s\n", inquiryResp.Customer.TotalAmount)
		}
	}

	// Example 3: Payment Processing
	fmt.Println("\n=== Payment Processing Example ===")

	paymentReq := &ottopay.PaymentRequest{
		CompanyCode:    "12173",
		CustomerNumber: "***********",
		CustomerName:   "Customer Virtual Account",
		CurrencyCode:   "IDR",
		PaidAmount:     "150000.00",
		TotalAmount:    "150000.00",
		Reference:      "REF123456789",
		ChannelType:    "API",
		Token:          "demo_token", // In real usage, use token from authentication
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	paymentResp, err := client.Payment(ctx, paymentReq)
	if err != nil {
		log.Printf("Payment failed: %v", err)
	} else {
		fmt.Printf("Payment Result:\n")
		fmt.Printf("  Success: %v\n", paymentResp.Success)
		fmt.Printf("  Status: %s\n", paymentResp.Status)
		fmt.Printf("  Message: %s\n", paymentResp.Message)
		fmt.Printf("  Request ID: %s\n", paymentResp.RequestID)
		fmt.Printf("  Transaction ID: %s\n", paymentResp.TransactionID)
		fmt.Printf("  Response Time: %dms\n", paymentResp.ResponseTime)

		if paymentResp.Payment != nil {
			fmt.Printf("  Payment:\n")
			fmt.Printf("    ID: %s\n", paymentResp.Payment.ID)
			fmt.Printf("    Customer Name: %s\n", paymentResp.Payment.CustomerName)
			fmt.Printf("    Paid Amount: %s\n", paymentResp.Payment.PaidAmount)
			fmt.Printf("    Total Amount: %s\n", paymentResp.Payment.TotalAmount)
			fmt.Printf("    Reference: %s\n", paymentResp.Payment.Reference)
			fmt.Printf("    Status: %s\n", paymentResp.Payment.Status)
		}
	}

	// Example 4: Token Validation
	fmt.Println("\n=== Token Validation Example ===")

	validateReq := &ottopay.ValidateTokenRequest{
		Token:       "demo_token",
		ChannelType: "API",
		Operation:   "validate",
		SourceIP:    "127.0.0.1",
		UserAgent:   "OttoPay-Example/1.0",
	}

	validateResp, err := client.ValidateToken(ctx, validateReq)
	if err != nil {
		log.Printf("Token validation failed: %v", err)
	} else {
		fmt.Printf("Token Validation Result:\n")
		fmt.Printf("  Valid: %v\n", validateResp.Valid)
		fmt.Printf("  Username: %s\n", validateResp.Username)
		fmt.Printf("  Token ID: %s\n", validateResp.TokenID)
		fmt.Printf("  Expires In: %v\n", validateResp.ExpiresIn)
		fmt.Printf("  Request ID: %s\n", validateResp.RequestID)
		if validateResp.Reason != "" {
			fmt.Printf("  Reason: %s\n", validateResp.Reason)
		}
	}

	// Example 5: Health Check
	fmt.Println("\n=== Health Check Example ===")

	healthResp, err := client.HealthCheck(ctx)
	if err != nil {
		log.Printf("Health check failed: %v", err)
	} else {
		fmt.Printf("Health Check Result:\n")
		fmt.Printf("  Status: %s\n", healthResp.Status)
		fmt.Printf("  Version: %s\n", healthResp.Version)
		fmt.Printf("  Timestamp: %s\n", healthResp.Timestamp.Format(time.RFC3339))
		fmt.Printf("  Uptime: %v\n", healthResp.Uptime)

		if len(healthResp.Dependencies) > 0 {
			fmt.Printf("  Dependencies:\n")
			for name, status := range healthResp.Dependencies {
				fmt.Printf("    %s: %s\n", name, status)
			}
		}
	}

	// Example 6: Error Handling
	fmt.Println("\n=== Error Handling Example ===")

	// Try inquiry with invalid data
	invalidInquiryReq := &ottopay.InquiryRequest{
		CompanyCode:    "00000", // Invalid company code
		CustomerNumber: "**********",
		ChannelType:    "API",
		Token:          "invalid_token",
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	invalidInquiryResp, err := client.Inquiry(ctx, invalidInquiryReq)
	if err != nil {
		fmt.Printf("Expected error occurred: %v\n", err)
	} else if invalidInquiryResp != nil && !invalidInquiryResp.Success {
		fmt.Printf("Inquiry failed as expected:\n")
		fmt.Printf("  Status: %s\n", invalidInquiryResp.InquiryStatus)
		fmt.Printf("  Message: %s\n", invalidInquiryResp.Message)
		if invalidInquiryResp.InquiryReason != nil {
			fmt.Printf("  Reason (EN): %s\n", invalidInquiryResp.InquiryReason.English)
			fmt.Printf("  Reason (ID): %s\n", invalidInquiryResp.InquiryReason.Indonesian)
		}
	}

	fmt.Println("\n=== OttoPay Example Completed ===")
}
