# Basic OttoPay Usage Example

This example demonstrates the basic usage of the OttoPay client library using the public API.

## Features

- **Simple Integration**: Uses the public `ottopay` package
- **Complete Workflow**: Authentication, inquiry, payment, and validation
- **Error Handling**: Demonstrates proper error handling patterns
- **Health Checks**: Shows how to monitor service health
- **Clean API**: Uses the simplified public interface

## Usage

### 1. Install Dependencies

```bash
cd examples/basic_usage
go mod tidy
```

### 2. Run the Example

```bash
go run main.go
```

## Example Output

```
=== Authentication Example ===
Authentication Result:
  Success: true
  Message: Authentication successful
  Request ID: TOK202401011200000001
  Token ID: token-123
  Expires In: 24h0m0s

=== Customer Inquiry Example ===
Inquiry Result:
  Success: true
  Status: SUCCESS
  Message: Customer inquiry completed successfully
  Request ID: INQ202401011200000001
  Response Time: 1250ms
  Customer:
    Name: Customer Virtual Account
    Company Code: 12173
    Customer Number: ***********
    Currency: IDR
    Total Amount: 150000.00

=== Payment Processing Example ===
Payment Result:
  Success: true
  Status: SUCCESS
  Message: Payment processed successfully
  Request ID: PAY202401011200000001
  Transaction ID: TXN202401011200000001
  Response Time: 2100ms
  Payment:
    ID: PAY202401011200000001
    Customer Name: Customer Virtual Account
    Paid Amount: 150000.00
    Total Amount: 150000.00
    Reference: REF123456789
    Status: SUCCESS

=== Token Validation Example ===
Token Validation Result:
  Valid: true
  Username: demo_user
  Token ID: token-123
  Expires In: 23h59m30s
  Request ID: VAL202401011200000001

=== Health Check Example ===
Health Check Result:
  Status: healthy
  Version: 1.0.0
  Timestamp: 2024-01-01T12:00:00Z
  Uptime: 2h30m15s
  Dependencies:
    database: healthy
    redis: healthy
    ottopay_api: healthy

=== Error Handling Example ===
Inquiry failed as expected:
  Status: CUSTOMER_NOT_FOUND
  Message: Customer inquiry failed
  Reason (EN): Customer not found
  Reason (ID): Pelanggan tidak ditemukan
```

## Code Structure

### Client Initialization

```go
config := &ottopay.Config{
    BaseURL:    "https://api.ottopay.example.com",
    APIKey:     "your_api_key",
    Username:   "your_username",
    Password:   "your_password",
    Timeout:    30 * time.Second,
    MaxRetries: 3,
}

client, err := ottopay.NewClient(config)
if err != nil {
    log.Fatalf("Failed to create client: %v", err)
}
```

### Authentication

```go
tokenReq := &ottopay.GetTokenRequest{
    Username:    "demo_user",
    Password:    "demo_password",
    ChannelType: "API",
    SourceIP:    "127.0.0.1",
    UserAgent:   "OttoPay-Example/1.0",
}

tokenResp, err := client.GetToken(ctx, tokenReq)
```

### Customer Inquiry

```go
inquiryReq := &ottopay.InquiryRequest{
    CompanyCode:    "12173",
    CustomerNumber: "***********",
    ChannelType:    "API",
    Token:          token,
    SourceIP:       "127.0.0.1",
    UserAgent:      "OttoPay-Example/1.0",
}

inquiryResp, err := client.Inquiry(ctx, inquiryReq)
```

### Payment Processing

```go
paymentReq := &ottopay.PaymentRequest{
    CompanyCode:    "12173",
    CustomerNumber: "***********",
    CustomerName:   "Customer Virtual Account",
    CurrencyCode:   "IDR",
    PaidAmount:     "150000.00",
    TotalAmount:    "150000.00",
    Reference:      "REF123456789",
    ChannelType:    "API",
    Token:          token,
    SourceIP:       "127.0.0.1",
    UserAgent:      "OttoPay-Example/1.0",
}

paymentResp, err := client.Payment(ctx, paymentReq)
```

## Configuration

### Environment Variables

You can configure the client using environment variables:

```bash
export OTTOPAY_BASE_URL="https://api.ottopay.production.com"
export OTTOPAY_API_KEY="your_api_key"
export OTTOPAY_USERNAME="your_username"
export OTTOPAY_PASSWORD="your_password"
```

### Configuration File

Create a `config.json` file:

```json
{
  "base_url": "https://api.ottopay.production.com",
  "api_key": "your_api_key",
  "username": "your_username",
  "password": "your_password",
  "timeout": "30s",
  "max_retries": 3
}
```

## Error Handling

The example demonstrates several error handling patterns:

### Network Errors
```go
tokenResp, err := client.GetToken(ctx, tokenReq)
if err != nil {
    // Handle network or client errors
    log.Printf("Authentication failed: %v", err)
    return
}
```

### Business Logic Errors
```go
if !inquiryResp.Success {
    // Handle business logic failures
    fmt.Printf("Inquiry failed: %s\n", inquiryResp.Message)
    if inquiryResp.InquiryReason != nil {
        fmt.Printf("Reason: %s\n", inquiryResp.InquiryReason.English)
    }
}
```

### Validation Errors
```go
// The client automatically validates input parameters
// Invalid data will return validation errors
```

## Best Practices

### 1. Use Context with Timeout
```go
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

response, err := client.Inquiry(ctx, request)
```

### 2. Handle All Error Cases
```go
response, err := client.Payment(ctx, request)
if err != nil {
    // Network or client error
    return handleNetworkError(err)
}

if !response.Success {
    // Business logic error
    return handleBusinessError(response)
}

// Success case
return handleSuccess(response)
```

### 3. Log Important Operations
```go
log.Printf("Processing payment for customer %s, amount %s", 
    request.CustomerNumber, request.PaidAmount)

response, err := client.Payment(ctx, request)
if err != nil {
    log.Printf("Payment failed: %v", err)
} else {
    log.Printf("Payment successful: %s", response.TransactionID)
}
```

### 4. Use Proper Request IDs
```go
// Request IDs are automatically generated by the client
// They ensure idempotency and help with tracking
```

## Integration Tips

### Production Configuration
```go
config := &ottopay.Config{
    BaseURL:    os.Getenv("OTTOPAY_BASE_URL"),
    APIKey:     os.Getenv("OTTOPAY_API_KEY"),
    Username:   os.Getenv("OTTOPAY_USERNAME"),
    Password:   os.Getenv("OTTOPAY_PASSWORD"),
    Timeout:    30 * time.Second,
    MaxRetries: 3,
}
```

### Retry Logic
The client includes built-in retry logic for transient failures. Configure `MaxRetries` based on your requirements.

### Rate Limiting
Respect API rate limits by implementing appropriate delays between requests in high-volume scenarios.

### Monitoring
Use the health check endpoint to monitor service availability:

```go
health, err := client.HealthCheck(ctx)
if err != nil || health.Status != "healthy" {
    // Handle service unavailability
}
```

## Support

For questions about this example:

1. Check the main OttoPay documentation
2. Review the public API documentation in `pkg/ottopay/`
3. Examine the test files for additional patterns
4. Refer to other examples for specific use cases
