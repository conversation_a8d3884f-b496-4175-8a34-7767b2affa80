package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/external"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/auth"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/inquiry"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/payment"
	"repo.nusatek.id/sugeng/ottopay/internal/utils"
)

// OttoPayService wraps all OttoPay use cases
type OttoPayService struct {
	InquiryUseCase *inquiry.CustomerInquiryUseCase
	PaymentUseCase *payment.ProcessPaymentUseCase
	AuthUseCase    *auth.GetTokenUseCase
}

// InquiryRequest represents the HTTP request for customer inquiry
type InquiryRequest struct {
	CompanyCode    string `json:"company_code" validate:"required,len=5"`
	CustomerNumber string `json:"customer_number" validate:"required,max=11"`
	ChannelType    string `json:"channel_type" validate:"required"`
	SourceIP       string `json:"source_ip,omitempty"`
	UserAgent      string `json:"user_agent,omitempty"`
}

// PaymentRequest represents the HTTP request for payment processing
type PaymentRequest struct {
	CompanyCode    string  `json:"company_code" validate:"required,len=5"`
	CustomerNumber string  `json:"customer_number" validate:"required,max=11"`
	CustomerName   string  `json:"customer_name" validate:"required"`
	CurrencyCode   string  `json:"currency_code" validate:"required,len=3"`
	PaidAmount     float64 `json:"paid_amount" validate:"required,gt=0"`
	TotalAmount    float64 `json:"total_amount" validate:"required,gt=0"`
	Reference      string  `json:"reference" validate:"required"`
	ChannelType    string  `json:"channel_type" validate:"required"`
	SourceIP       string  `json:"source_ip,omitempty"`
	UserAgent      string  `json:"user_agent,omitempty"`
}

// AuthRequest represents the HTTP request for authentication
type AuthRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// ErrorResponse represents error response format
type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
}

func main() {
	// Initialize logger
	logger := utils.NewDefaultLogger()
	logger.Info("Starting OttoPay Fiber integration example")

	// Initialize OttoPay service
	ottoPayService := initializeOttoPayService(logger)

	// Create Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "OttoPay Integration Example",
		ServerHeader: "OttoPay-Fiber/1.0",
		ErrorHandler: errorHandler,
	})

	// Add middleware
	app.Use(recover.New())
	app.Use(requestid.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Setup routes
	setupRoutes(app, ottoPayService)

	// Start server in goroutine
	go func() {
		if err := app.Listen(":8080"); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	logger.Info("Server started on :8080")
	logger.Info("Available endpoints:")
	logger.Info("  POST /api/v1/auth/token - Get authentication token")
	logger.Info("  POST /api/v1/inquiry - Customer inquiry")
	logger.Info("  POST /api/v1/payment - Process payment")
	logger.Info("  GET /api/v1/health - Health check")

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := app.ShutdownWithContext(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}

	logger.Info("Server exited")
}

func initializeOttoPayService(logger utils.Logger) *OttoPayService {
	// Initialize OttoPay API configuration
	config := &external.OttoPayAPIConfig{
		BaseURL:  getEnv("OTTOPAY_BASE_URL", "https://api.ottopay.example.com"),
		Username: getEnv("OTTOPAY_USERNAME", "demo_user"),
		Password: getEnv("OTTOPAY_PASSWORD", "demo_password"),
		APIKey:   getEnv("OTTOPAY_API_KEY", "demo_api_key"),
		Timeout:  30 * time.Second,
		Logger:   logger,
	}

	// Initialize external services
	apiAdapter := external.NewOttoPayAPIAdapter(config)

	// Initialize repositories (using in-memory for this example)
	auditRepo := repositories.NewInMemoryAuditRepository()
	tokenRepo := repositories.NewInMemoryTokenRepository()

	// Initialize validation service
	validationService := external.NewValidationService()

	// Initialize use cases
	inquiryUseCase := inquiry.NewCustomerInquiryUseCase(
		apiAdapter,
		validationService,
		auditRepo,
		tokenRepo,
	)

	paymentUseCase := payment.NewProcessPaymentUseCase(
		repositories.NewInMemoryPaymentRepository(),
		apiAdapter,
		validationService,
		auditRepo,
		tokenRepo,
	)

	authUseCase := auth.NewGetTokenUseCase(
		external.NewAuthenticationService(),
		tokenRepo,
		auditRepo,
	)

	return &OttoPayService{
		InquiryUseCase: inquiryUseCase,
		PaymentUseCase: paymentUseCase,
		AuthUseCase:    authUseCase,
	}
}

func setupRoutes(app *fiber.App, service *OttoPayService) {
	// API v1 group
	api := app.Group("/api/v1")

	// Health check endpoint
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"service":   "ottopay-integration",
		})
	})

	// Authentication endpoints
	auth := api.Group("/auth")
	auth.Post("/token", handleGetToken(service))

	// OttoPay endpoints
	api.Post("/inquiry", handleInquiry(service))
	api.Post("/payment", handlePayment(service))
}

func handleGetToken(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req AuthRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Generate request ID
		requestID, _ := valueobjects.GenerateTokenRequestID()

		// Create auth request
		authReq := &auth.GetTokenRequest{
			Username:    req.Username,
			Password:    req.Password,
			RequestID:   requestID,
			ChannelType: "API",
			SourceIP:    c.IP(),
			UserAgent:   c.Get("User-Agent"),
		}

		// Execute authentication
		ctx := c.Context()
		response, err := service.AuthUseCase.Execute(ctx, authReq)
		if err != nil {
			return c.Status(500).JSON(ErrorResponse{
				Success: false,
				Error:   "Authentication failed",
				Code:    "AUTH_ERROR",
			})
		}

		return c.JSON(response)
	}
}

func handleInquiry(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req InquiryRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Validate and create value objects
		companyCode, err := valueobjects.NewCompanyCode(req.CompanyCode)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid company code: " + err.Error(),
				Code:    "INVALID_COMPANY_CODE",
			})
		}

		customerNumber, err := valueobjects.NewCustomerNumber(req.CustomerNumber)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid customer number: " + err.Error(),
				Code:    "INVALID_CUSTOMER_NUMBER",
			})
		}

		// Generate request ID
		requestID, _ := valueobjects.GenerateInquiryRequestID()

		// Get token from Authorization header
		token := extractTokenFromHeader(c.Get("Authorization"))

		// Create inquiry request
		inquiryReq := &inquiry.CustomerInquiryRequest{
			CompanyCode:    companyCode,
			CustomerNumber: customerNumber,
			RequestID:      requestID,
			ChannelType:    req.ChannelType,
			Token:          token,
			SourceIP:       getSourceIP(c, req.SourceIP),
			UserAgent:      getUserAgent(c, req.UserAgent),
		}

		// Execute inquiry
		ctx := c.Context()
		response, err := service.InquiryUseCase.Execute(ctx, inquiryReq)
		if err != nil {
			return c.Status(500).JSON(ErrorResponse{
				Success: false,
				Error:   "Inquiry failed: " + err.Error(),
				Code:    "INQUIRY_ERROR",
			})
		}

		return c.JSON(response)
	}
}

func handlePayment(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req PaymentRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Validate and create value objects
		companyCode, err := valueobjects.NewCompanyCode(req.CompanyCode)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid company code: " + err.Error(),
				Code:    "INVALID_COMPANY_CODE",
			})
		}

		customerNumber, err := valueobjects.NewCustomerNumber(req.CustomerNumber)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid customer number: " + err.Error(),
				Code:    "INVALID_CUSTOMER_NUMBER",
			})
		}

		currencyCode, err := valueobjects.NewCurrencyCode(req.CurrencyCode)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid currency code: " + err.Error(),
				Code:    "INVALID_CURRENCY_CODE",
			})
		}

		paidAmount, err := valueobjects.NewAmount(req.PaidAmount, currencyCode)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid paid amount: " + err.Error(),
				Code:    "INVALID_PAID_AMOUNT",
			})
		}

		totalAmount, err := valueobjects.NewAmount(req.TotalAmount, currencyCode)
		if err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid total amount: " + err.Error(),
				Code:    "INVALID_TOTAL_AMOUNT",
			})
		}

		// Generate request ID
		requestID, _ := valueobjects.GeneratePaymentRequestID()

		// Get token from Authorization header
		token := extractTokenFromHeader(c.Get("Authorization"))

		// Create payment request
		paymentReq := &payment.ProcessPaymentRequest{
			CompanyCode:    companyCode,
			CustomerNumber: customerNumber,
			RequestID:      requestID,
			CustomerName:   req.CustomerName,
			CurrencyCode:   currencyCode,
			PaidAmount:     paidAmount,
			TotalAmount:    totalAmount,
			Reference:      req.Reference,
			ChannelType:    req.ChannelType,
			Token:          token,
			SourceIP:       getSourceIP(c, req.SourceIP),
			UserAgent:      getUserAgent(c, req.UserAgent),
		}

		// Execute payment
		ctx := c.Context()
		response, err := service.PaymentUseCase.Execute(ctx, paymentReq)
		if err != nil {
			return c.Status(500).JSON(ErrorResponse{
				Success: false,
				Error:   "Payment failed: " + err.Error(),
				Code:    "PAYMENT_ERROR",
			})
		}

		return c.JSON(response)
	}
}

// Helper functions
func errorHandler(c *fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError
	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
	}

	return c.Status(code).JSON(ErrorResponse{
		Success: false,
		Error:   err.Error(),
		Code:    "INTERNAL_ERROR",
	})
}

func extractTokenFromHeader(authHeader string) string {
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		return authHeader[7:]
	}
	return authHeader
}

func getSourceIP(c *fiber.Ctx, requestIP string) string {
	if requestIP != "" {
		return requestIP
	}
	return c.IP()
}

func getUserAgent(c *fiber.Ctx, requestUA string) string {
	if requestUA != "" {
		return requestUA
	}
	return c.Get("User-Agent")
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
