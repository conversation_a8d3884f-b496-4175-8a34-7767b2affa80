package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/requestid"
)

// Mock <PERSON>ay service for demonstration
type OttoPayService struct {
	apiKey   string
	username string
	password string
	baseURL  string
}

// Request/Response types
type InquiryRequest struct {
	CompanyCode    string `json:"company_code" validate:"required,len=5"`
	CustomerNumber string `json:"customer_number" validate:"required,max=11"`
	ChannelType    string `json:"channel_type" validate:"required"`
	SourceIP       string `json:"source_ip,omitempty"`
	UserAgent      string `json:"user_agent,omitempty"`
}

type PaymentRequest struct {
	CompanyCode    string  `json:"company_code" validate:"required,len=5"`
	CustomerNumber string  `json:"customer_number" validate:"required,max=11"`
	CustomerName   string  `json:"customer_name" validate:"required"`
	CurrencyCode   string  `json:"currency_code" validate:"required,len=3"`
	PaidAmount     float64 `json:"paid_amount" validate:"required,gt=0"`
	TotalAmount    float64 `json:"total_amount" validate:"required,gt=0"`
	Reference      string  `json:"reference" validate:"required"`
	ChannelType    string  `json:"channel_type" validate:"required"`
	SourceIP       string  `json:"source_ip,omitempty"`
	UserAgent      string  `json:"user_agent,omitempty"`
}

type AuthRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
}

type AuthResponse struct {
	Success   bool   `json:"success"`
	Token     string `json:"token,omitempty"`
	ExpiresIn string `json:"expires_in,omitempty"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
}

type InquiryResponse struct {
	Success       bool   `json:"success"`
	InquiryStatus string `json:"inquiry_status"`
	Message       string `json:"message"`
	RequestID     string `json:"request_id"`
	ResponseTime  int64  `json:"response_time"`
	Customer      *struct {
		Name           string `json:"name"`
		CompanyCode    string `json:"company_code"`
		CustomerNumber string `json:"customer_number"`
		CurrencyCode   string `json:"currency_code"`
		TotalAmount    string `json:"total_amount"`
	} `json:"customer,omitempty"`
}

type PaymentResponse struct {
	Success       bool   `json:"success"`
	Status        string `json:"status"`
	Message       string `json:"message"`
	RequestID     string `json:"request_id"`
	TransactionID string `json:"transaction_id"`
	ResponseTime  int64  `json:"response_time"`
	Payment       *struct {
		ID           string `json:"id"`
		CustomerName string `json:"customer_name"`
		PaidAmount   string `json:"paid_amount"`
		TotalAmount  string `json:"total_amount"`
		Reference    string `json:"reference"`
		Status       string `json:"status"`
	} `json:"payment,omitempty"`
}

func main() {
	// Initialize OttoPay service
	ottoPayService := initializeOttoPayService()

	// Create Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "OttoPay Integration Example",
		ServerHeader: "OttoPay-Fiber/1.0",
		ErrorHandler: errorHandler,
	})

	// Add middleware
	app.Use(recover.New())
	app.Use(requestid.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Setup routes
	setupRoutes(app, ottoPayService)

	// Start server in goroutine
	go func() {
		if err := app.Listen(":8080"); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Println("Server started on :8080")
	log.Println("Available endpoints:")
	log.Println("  POST /api/v1/auth/token - Get authentication token")
	log.Println("  POST /api/v1/inquiry - Customer inquiry")
	log.Println("  POST /api/v1/payment - Process payment")
	log.Println("  GET /api/v1/health - Health check")

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := app.ShutdownWithContext(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

func initializeOttoPayService() *OttoPayService {
	return &OttoPayService{
		apiKey:   getEnv("OTTOPAY_API_KEY", "demo_api_key"),
		username: getEnv("OTTOPAY_USERNAME", "demo_user"),
		password: getEnv("OTTOPAY_PASSWORD", "demo_password"),
		baseURL:  getEnv("OTTOPAY_BASE_URL", "https://api.ottopay.example.com"),
	}
}

func setupRoutes(app *fiber.App, service *OttoPayService) {
	// API v1 group
	api := app.Group("/api/v1")

	// Health check endpoint
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"service":   "ottopay-integration",
		})
	})

	// Authentication endpoints
	auth := api.Group("/auth")
	auth.Post("/token", handleGetToken(service))

	// OttoPay endpoints
	api.Post("/inquiry", handleInquiry(service))
	api.Post("/payment", handlePayment(service))
}

func handleGetToken(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req AuthRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Mock authentication
		response := &AuthResponse{
			Success:   true,
			Token:     "mock_token_" + fmt.Sprintf("%d", time.Now().Unix()),
			ExpiresIn: "24h",
			Message:   "Authentication successful",
			RequestID: "TOK" + time.Now().Format("**************") + "001",
		}

		return c.JSON(response)
	}
}

func handleInquiry(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req InquiryRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Basic validation
		if len(req.CompanyCode) != 5 {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Company code must be exactly 5 digits",
				Code:    "INVALID_COMPANY_CODE",
			})
		}

		if len(req.CustomerNumber) > 11 || len(req.CustomerNumber) == 0 {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Customer number must be 1-11 characters",
				Code:    "INVALID_CUSTOMER_NUMBER",
			})
		}

		// Mock inquiry response
		response := &InquiryResponse{
			Success:       true,
			InquiryStatus: "SUCCESS",
			Message:       "Customer inquiry completed successfully",
			RequestID:     "INQ" + time.Now().Format("**************") + "001",
			ResponseTime:  1250,
			Customer: &struct {
				Name           string `json:"name"`
				CompanyCode    string `json:"company_code"`
				CustomerNumber string `json:"customer_number"`
				CurrencyCode   string `json:"currency_code"`
				TotalAmount    string `json:"total_amount"`
			}{
				Name:           "Customer Virtual Account",
				CompanyCode:    req.CompanyCode,
				CustomerNumber: req.CustomerNumber,
				CurrencyCode:   "IDR",
				TotalAmount:    "150000.00",
			},
		}

		return c.JSON(response)
	}
}

func handlePayment(service *OttoPayService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var req PaymentRequest
		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Invalid request format",
				Code:    "INVALID_REQUEST",
			})
		}

		// Basic validation
		if len(req.CompanyCode) != 5 {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Company code must be exactly 5 digits",
				Code:    "INVALID_COMPANY_CODE",
			})
		}

		if req.CurrencyCode != "IDR" && req.CurrencyCode != "USD" {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Currency must be IDR or USD",
				Code:    "INVALID_CURRENCY_CODE",
			})
		}

		if req.PaidAmount <= 0 || req.TotalAmount <= 0 {
			return c.Status(400).JSON(ErrorResponse{
				Success: false,
				Error:   "Amounts must be positive",
				Code:    "INVALID_AMOUNT",
			})
		}

		// Mock payment response
		response := &PaymentResponse{
			Success:       true,
			Status:        "SUCCESS",
			Message:       "Payment processed successfully",
			RequestID:     "PAY" + time.Now().Format("**************") + "001",
			TransactionID: "TXN" + time.Now().Format("**************") + "001",
			ResponseTime:  2100,
			Payment: &struct {
				ID           string `json:"id"`
				CustomerName string `json:"customer_name"`
				PaidAmount   string `json:"paid_amount"`
				TotalAmount  string `json:"total_amount"`
				Reference    string `json:"reference"`
				Status       string `json:"status"`
			}{
				ID:           "PAY" + time.Now().Format("**************") + "001",
				CustomerName: req.CustomerName,
				PaidAmount:   strconv.FormatFloat(req.PaidAmount, 'f', 2, 64),
				TotalAmount:  strconv.FormatFloat(req.TotalAmount, 'f', 2, 64),
				Reference:    req.Reference,
				Status:       "SUCCESS",
			},
		}

		return c.JSON(response)
	}
}

func errorHandler(c *fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError
	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
	}

	return c.Status(code).JSON(ErrorResponse{
		Success: false,
		Error:   err.Error(),
		Code:    "INTERNAL_ERROR",
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
