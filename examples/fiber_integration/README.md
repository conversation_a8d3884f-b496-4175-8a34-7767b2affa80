# OttoPay Fiber v2 Integration Example

This example demonstrates how to integrate OttoPay Virtual Account Non Billing with an existing Fiber v2 web server application.

## Features

- **RESTful API**: Complete REST API endpoints for OttoPay operations
- **Fiber v2 Integration**: Uses the latest Fiber v2 framework
- **Middleware Support**: Includes CORS, logging, recovery, and request ID middleware
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Authentication**: Token-based authentication with Bearer token support
- **Validation**: Request validation with proper error responses
- **Graceful Shutdown**: Proper server shutdown handling
- **Environment Configuration**: Configurable via environment variables

## API Endpoints

### Authentication
- `POST /api/v1/auth/token` - Get authentication token

### OttoPay Operations
- `POST /api/v1/inquiry` - Customer inquiry
- `POST /api/v1/payment` - Process payment

### Health Check
- `GET /api/v1/health` - Health check endpoint

## Quick Start

### 1. Install Dependencies

```bash
cd examples/fiber_integration
go mod tidy
```

### 2. Set Environment Variables (Optional)

```bash
export OTTOPAY_BASE_URL="https://api.ottopay.production.com"
export OTTOPAY_USERNAME="your_username"
export OTTOPAY_PASSWORD="your_password"
export OTTOPAY_API_KEY="your_api_key"
```

### 3. Run the Server

```bash
go run main.go
```

The server will start on `http://localhost:8080`

## API Usage Examples

### 1. Get Authentication Token

```bash
curl -X POST http://localhost:8080/api/v1/auth/token \
  -H "Content-Type: application/json" \
  -d '{
    "username": "demo_user",
    "password": "demo_password"
  }'
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_at": "2024-01-01T12:00:00Z",
  "request_id": "TOK202401011200000001"
}
```

### 2. Customer Inquiry

```bash
curl -X POST http://localhost:8080/api/v1/inquiry \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "company_code": "12173",
    "customer_number": "***********",
    "channel_type": "API"
  }'
```

**Response:**
```json
{
  "success": true,
  "inquiry_status": "SUCCESS",
  "message": "Customer inquiry completed successfully",
  "request_id": "INQ202401011200000001",
  "response_time": 1250,
  "customer": {
    "company_code": "12173",
    "customer_number": "***********",
    "name": "Customer Virtual Account",
    "currency_code": "IDR",
    "total_amount": "150000.00"
  }
}
```

### 3. Process Payment

```bash
curl -X POST http://localhost:8080/api/v1/payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "company_code": "12173",
    "customer_number": "***********",
    "customer_name": "Customer Virtual Account",
    "currency_code": "IDR",
    "paid_amount": 150000.00,
    "total_amount": 150000.00,
    "reference": "REF123456789",
    "channel_type": "API"
  }'
```

**Response:**
```json
{
  "success": true,
  "status": "SUCCESS",
  "message": "Payment processed successfully",
  "request_id": "PAY202401011200000001",
  "transaction_id": "TXN202401011200000001",
  "response_time": 2100,
  "payment": {
    "id": "PAY202401011200000001",
    "company_code": "12173",
    "customer_number": "***********",
    "customer_name": "Customer Virtual Account",
    "currency_code": "IDR",
    "paid_amount": "150000.00",
    "total_amount": "150000.00",
    "reference": "REF123456789",
    "status": "SUCCESS"
  }
}
```

### 4. Health Check

```bash
curl http://localhost:8080/api/v1/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "ottopay-integration"
}
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": "Invalid company code: company code cannot be empty",
  "code": "INVALID_COMPANY_CODE"
}
```

### Common Error Codes

- `INVALID_REQUEST` - Malformed request body
- `INVALID_COMPANY_CODE` - Invalid company code format
- `INVALID_CUSTOMER_NUMBER` - Invalid customer number format
- `INVALID_CURRENCY_CODE` - Invalid currency code
- `INVALID_PAID_AMOUNT` - Invalid paid amount
- `INVALID_TOTAL_AMOUNT` - Invalid total amount
- `AUTH_ERROR` - Authentication failed
- `INQUIRY_ERROR` - Customer inquiry failed
- `PAYMENT_ERROR` - Payment processing failed
- `INTERNAL_ERROR` - Internal server error

## Integration with Existing Fiber App

To integrate OttoPay into your existing Fiber v2 application:

### 1. Initialize OttoPay Service

```go
func initializeOttoPayService() *OttoPayService {
    // Configure OttoPay
    config := &external.OttoPayAPIConfig{
        BaseURL:  os.Getenv("OTTOPAY_BASE_URL"),
        Username: os.Getenv("OTTOPAY_USERNAME"),
        Password: os.Getenv("OTTOPAY_PASSWORD"),
        APIKey:   os.Getenv("OTTOPAY_API_KEY"),
        Timeout:  30 * time.Second,
    }

    // Initialize services and use cases
    apiAdapter := external.NewOttoPayAPIAdapter(config)
    // ... initialize other dependencies
    
    return &OttoPayService{
        InquiryUseCase: inquiryUseCase,
        PaymentUseCase: paymentUseCase,
        AuthUseCase:    authUseCase,
    }
}
```

### 2. Add Routes to Existing App

```go
func addOttoPayRoutes(app *fiber.App, service *OttoPayService) {
    ottopay := app.Group("/ottopay")
    
    ottopay.Post("/inquiry", handleInquiry(service))
    ottopay.Post("/payment", handlePayment(service))
}
```

### 3. Use Middleware

```go
// Add authentication middleware
ottopay.Use(authMiddleware())

// Add rate limiting
ottopay.Use(limiter.New(limiter.Config{
    Max:        100,
    Expiration: 1 * time.Minute,
}))
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OTTOPAY_BASE_URL` | OttoPay API base URL | `https://api.ottopay.example.com` |
| `OTTOPAY_USERNAME` | OttoPay API username | `demo_user` |
| `OTTOPAY_PASSWORD` | OttoPay API password | `demo_password` |
| `OTTOPAY_API_KEY` | OttoPay API key | `demo_api_key` |

### Production Configuration

```go
config := &external.OttoPayAPIConfig{
    BaseURL:    "https://api.ottopay.production.com",
    Username:   os.Getenv("OTTOPAY_USERNAME"),
    Password:   os.Getenv("OTTOPAY_PASSWORD"),
    APIKey:     os.Getenv("OTTOPAY_API_KEY"),
    Timeout:    30 * time.Second,
    MaxRetries: 3,
    Logger:     logger,
}
```

## Security Considerations

1. **Environment Variables**: Store sensitive credentials in environment variables
2. **HTTPS**: Use HTTPS in production
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Input Validation**: Validate all input parameters
5. **Error Handling**: Don't expose internal errors to clients
6. **Logging**: Log all transactions for audit purposes

## Testing

Test the endpoints using the provided curl examples or tools like Postman. The example includes:

- Successful request/response flows
- Error handling scenarios
- Input validation
- Authentication flows

## Monitoring and Logging

The example includes:

- Request/response logging
- Error logging
- Performance metrics (response times)
- Health check endpoint for monitoring

## Support

For questions about this integration example:

1. Check the main OttoPay documentation
2. Review the Fiber v2 documentation
3. Examine the test files for additional patterns
4. Refer to the domain models for business rules
