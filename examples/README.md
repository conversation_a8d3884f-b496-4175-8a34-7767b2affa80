# OttoPay Examples

This directory contains comprehensive examples demonstrating how to use the OttoPay Virtual Account Non Billing system in various scenarios and architectures.

## Available Examples

### 1. Simple Payment Example (`simple_payment/`)

A basic example showing fundamental OttoPay operations:
- Initialize the OttoPay client
- Perform customer inquiry
- Process a payment
- Handle responses and errors

**Key Features:**
- Minimal setup required
- Clear step-by-step implementation
- Error handling examples
- Response parsing

**Usage:**
```bash
cd simple_payment
go run main.go
```

### 2. Fiber v2 Integration (`fiber_integration/`)

Complete REST API server using Fiber v2 web framework:
- RESTful API endpoints for all OttoPay operations
- Authentication with Bearer tokens
- Request validation and error handling
- Middleware integration (CORS, logging, recovery)
- Graceful shutdown and health checks

**Key Features:**
- Production-ready web server
- Comprehensive API documentation
- Environment-based configuration
- Structured error responses
- Integration with existing Fiber applications

**Usage:**
```bash
cd fiber_integration
go mod tidy
go run main.go
```

**Endpoints:**
- `POST /api/v1/auth/token` - Authentication
- `POST /api/v1/inquiry` - Customer inquiry
- `POST /api/v1/payment` - Payment processing
- `GET /api/v1/health` - Health check

### 3. CLI Tool (`cli_tool/`)

Command-line interface for OttoPay operations:
- Interactive CLI with multiple commands
- Support for JSON, table, and CSV output formats
- Configuration via environment variables or config files
- Batch processing capabilities
- Value object validation

**Key Features:**
- Multiple output formats
- Comprehensive validation
- Pipeline integration support
- Verbose logging for debugging
- Shell script integration

**Usage:**
```bash
cd cli_tool
go build -o ottopay-cli main.go

# Authentication
./ottopay-cli auth -username demo_user -password demo_password

# Customer inquiry
./ottopay-cli inquiry -company 12173 -customer 56751590099

# Payment processing
./ottopay-cli payment -company 12173 -customer 56751590099 \
  -name "John Doe" -currency IDR -paid 150000 -total 150000 \
  -reference REF123456
```

### 4. Docker Deployment (`docker_deployment/`)

Complete containerized deployment with monitoring and logging:
- Multi-service Docker Compose setup
- PostgreSQL and Redis integration
- Nginx reverse proxy and load balancing
- Prometheus monitoring and Grafana dashboards
- ELK stack for centralized logging
- Production-ready configuration

**Key Features:**
- Full observability stack
- Scalable architecture
- Security hardening
- Backup and recovery procedures
- Health checks and monitoring

**Services:**
- OttoPay API service
- PostgreSQL database
- Redis cache
- Nginx reverse proxy
- Prometheus + Grafana monitoring
- Elasticsearch + Kibana logging

**Usage:**
```bash
cd docker_deployment
cp .env.example .env
# Edit .env with your configuration
docker-compose up -d
```

### 5. Microservices Architecture (`microservices/`)

Decomposed microservices architecture for enterprise deployment:
- Service-oriented architecture (SOA)
- Independent service scaling
- Inter-service communication patterns
- Event-driven architecture
- Service discovery and configuration
- Distributed tracing and monitoring

**Services:**
- Authentication Service (Port 8081)
- Customer Inquiry Service (Port 8082)
- Payment Processing Service (Port 8083)
- Audit Service (Port 8084)

**Key Features:**
- Independent deployments
- Technology diversity
- Fault isolation
- Circuit breaker patterns
- Event sourcing
- CQRS implementation

## Integration Scenarios

### Web Applications
- **Fiber Integration**: For Go web applications using Fiber framework
- **Microservices**: For large-scale enterprise applications
- **Docker Deployment**: For containerized web services

### Command Line Tools
- **CLI Tool**: For automation, scripting, and batch processing
- **Simple Payment**: For basic integration testing

### Enterprise Deployment
- **Docker Deployment**: Complete production environment
- **Microservices**: Scalable enterprise architecture
- **Monitoring**: Full observability stack

## Getting Started

### Quick Start (Simple Integration)
```bash
cd simple_payment
go run main.go
```

### Web Server (Fiber v2)
```bash
cd fiber_integration
go mod tidy
go run main.go
# Server starts on http://localhost:8080
```

### Command Line Tool
```bash
cd cli_tool
go build -o ottopay-cli main.go
./ottopay-cli help
```

### Production Deployment
```bash
cd docker_deployment
cp .env.example .env
# Configure .env file
docker-compose up -d
```

## Prerequisites

### Basic Requirements
- Go 1.21 or later
- Valid OttoPay API credentials
- Network access to OttoPay API endpoints

### Docker Examples
- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ RAM available
- 10GB+ free disk space

### Microservices
- Kubernetes cluster (optional)
- Service mesh (Istio/Linkerd) (optional)
- Message queue (RabbitMQ/Apache Kafka)

## Configuration

### Environment Variables
```bash
# OttoPay API Configuration
export OTTOPAY_BASE_URL="https://api.ottopay.production.com"
export OTTOPAY_USERNAME="your_username"
export OTTOPAY_PASSWORD="your_password"
export OTTOPAY_API_KEY="your_api_key"

# Database Configuration (for Docker/Microservices)
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_NAME="ottopay"
export DB_USER="ottopay"
export DB_PASSWORD="secure_password"

# Redis Configuration
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export REDIS_PASSWORD=""

# Security
export JWT_SECRET="your-super-secret-jwt-key"
```

### Configuration Files
Each example supports configuration files:
- `config.json` - JSON configuration
- `.env` - Environment variables
- `docker-compose.yml` - Container configuration

## Example Structure

```
examples/
├── simple_payment/          # Basic integration example
│   ├── main.go
│   ├── go.mod
│   └── README.md
├── fiber_integration/        # Fiber v2 web server
│   ├── main.go
│   ├── go.mod
│   └── README.md
├── cli_tool/                # Command-line interface
│   ├── main.go
│   ├── go.mod
│   └── README.md
├── docker_deployment/       # Containerized deployment
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── nginx/
│   ├── monitoring/
│   └── README.md
├── microservices/           # Microservices architecture
│   ├── auth-service/
│   ├── inquiry-service/
│   ├── payment-service/
│   ├── audit-service/
│   └── README.md
└── README.md               # This file
```

## Testing Examples

### Unit Testing
```bash
# Run tests for specific example
cd fiber_integration
go test ./...

# Run with coverage
go test -cover ./...
```

### Integration Testing
```bash
# Start test environment
cd docker_deployment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
go test -tags=integration ./...
```

### End-to-End Testing
```bash
# Test complete workflow
cd cli_tool
./ottopay-cli auth
./ottopay-cli inquiry -company 12173 -customer 56751590099
./ottopay-cli payment -company 12173 -customer 56751590099 \
  -name "Test Customer" -currency IDR -paid 150000 -total 150000 \
  -reference TEST123
```

## Performance Benchmarks

### Load Testing
```bash
# Install hey (HTTP load testing tool)
go install github.com/rakyll/hey@latest

# Test Fiber integration
hey -n 1000 -c 10 -m POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{"company_code":"12173","customer_number":"56751590099","channel_type":"API"}' \
  http://localhost:8080/api/v1/inquiry
```

### Monitoring
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **Jaeger**: Distributed tracing
- **ELK Stack**: Centralized logging

## Security Considerations

### Production Deployment
1. **Environment Variables**: Store sensitive data securely
2. **HTTPS**: Use SSL/TLS in production
3. **Authentication**: Implement proper token validation
4. **Rate Limiting**: Prevent API abuse
5. **Input Validation**: Validate all inputs
6. **Network Security**: Use firewalls and VPNs

### Development
1. **Secrets Management**: Use tools like HashiCorp Vault
2. **Code Scanning**: Regular security audits
3. **Dependency Updates**: Keep dependencies current
4. **Access Control**: Limit service permissions

## Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Test connectivity
curl -v https://api.ottopay.example.com/health

# Check DNS resolution
nslookup api.ottopay.example.com
```

#### Authentication Issues
```bash
# Validate credentials
./ottopay-cli auth -verbose

# Check token expiration
./ottopay-cli validate -type token -value your_token
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats

# Check logs
docker-compose logs -f ottopay-api
```

### Debug Mode
Enable verbose logging in all examples:
```bash
# Environment variable
export LOG_LEVEL=debug

# CLI flag
./ottopay-cli inquiry -company 12173 -customer 56751590099 -verbose

# Docker environment
docker-compose up -d -e LOG_LEVEL=debug
```

## Contributing

### Adding New Examples
1. Create new directory under `examples/`
2. Follow the standard structure
3. Include comprehensive README
4. Add tests and documentation
5. Update this main README

### Example Guidelines
- **Clear Documentation**: Comprehensive README files
- **Error Handling**: Proper error handling patterns
- **Configuration**: Environment-based configuration
- **Testing**: Include test examples
- **Security**: Follow security best practices

## Support

For help with examples:

1. **Individual READMEs**: Check example-specific documentation
2. **Main Documentation**: Review project documentation
3. **Test Files**: Examine test patterns
4. **Issues**: Create GitHub issues for bugs
5. **Discussions**: Use GitHub discussions for questions

### Resources
- [OttoPay API Documentation](../docs/api.md)
- [Domain Model Documentation](../docs/domain.md)
- [Architecture Guide](../docs/architecture.md)
- [Testing Guide](../docs/testing.md)
