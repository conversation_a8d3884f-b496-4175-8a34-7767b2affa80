# OttoPay Examples

This directory contains example applications demonstrating how to use the OttoPay Virtual Account Non Billing integration.

## Examples

### 1. Simple Payment Example

**Location**: `simple_payment/main.go`

This example demonstrates the basic flow of customer inquiry and payment processing using the OttoPay API.

**Features Demonstrated**:
- Customer inquiry with company code and customer number
- Payment processing with proper amount formatting
- Error handling for invalid customer data
- Amount validation and currency operations
- Proper use of value objects and domain entities

**To Run**:
```bash
cd examples/simple_payment
go run main.go
```

**Expected Output**:
```
=== Customer Inquiry Example ===
Inquiry Result:
  Success: true
  Status: 00
  Message: Customer inquiry successful
  Customer: John Doe
  Amount: 100000.50 IDR

=== Payment Processing Example ===
Payment Result:
  Success: true
  Status: SUCCESS
  Message: Payment processed successfully
  Transaction ID: TXN-*********
  Paid Amount: 100000.50 IDR
  Reference: REF-PAY-*********

=== Error Handling Example ===
Inquiry failed as expected:
  Status: 01
  Reason (EN): Customer not found
  Reason (ID): Pelanggan tidak ditemukan

=== Amount Validation Example ===
Valid amount: 100000.50
Expected validation error: amount has too many decimal places
Expected validation error: amount cannot be negative

=== Currency Operations Example ===
IDR Amount: 100000.00 IDR
USD Amount: 1000.00 USD
IDR Amount with symbol: Rp100000.00
USD Amount with symbol: $1000.00
Expected currency mismatch error: cannot perform operation on different currencies
Double IDR amount: 200000.00 IDR
```

## Key Concepts Demonstrated

### 1. Value Objects
- **CompanyCode**: 5-character company identifier
- **CustomerNumber**: 10-character customer identifier
- **RequestID**: Unique request identifier with prefixes (INQ/PAY)
- **Amount**: Decimal amount with currency validation
- **CurrencyCode**: Supported currencies (IDR, USD)

### 2. Domain Entities
- **Customer**: Customer information with billing details
- **Payment**: Payment transaction with status tracking
- **Token**: Authentication token management

### 3. Use Cases
- **CustomerInquiry**: Query customer billing information
- **ProcessPayment**: Process customer payment
- **Authentication**: Token-based authentication

### 4. Infrastructure
- **OttoPayAPIAdapter**: External API communication
- **Repositories**: Data persistence abstraction
- **Services**: Domain services for validation and encryption

## Configuration

The examples use mock configuration. For real usage, configure the following:

```go
config := &external.OttoPayConfig{
    BaseURL:    "https://api.ottopay.production.com", // Production URL
    Username:   "your_username",
    Password:   "your_password", 
    APIKey:     "your_api_key",
    Timeout:    30 * time.Second,
    MaxRetries: 3,
}
```

## Error Handling

The examples demonstrate proper error handling for:

### Domain Validation Errors
- Invalid amount formats
- Currency mismatches
- Required field validation
- Business rule violations

### API Communication Errors
- Network timeouts
- Authentication failures
- Invalid responses
- Service unavailability

### Example Error Responses

**Invalid Customer**:
```json
{
  "success": false,
  "inquiryStatus": "01",
  "inquiryReason": {
    "indonesian": "Pelanggan tidak ditemukan",
    "english": "Customer not found"
  }
}
```

**Payment Failure**:
```json
{
  "success": false,
  "paymentFlagStatus": "01",
  "paymentFlagReason": {
    "indonesian": "Pembayaran ditolak",
    "english": "Payment rejected"
  }
}
```

## Best Practices

### 1. Always Validate Input
```go
// Validate company code
companyCode, err := valueobjects.NewCompanyCode("12345")
if err != nil {
    return fmt.Errorf("invalid company code: %w", err)
}

// Validate amount
amount, err := valueobjects.NewAmount(100000.50, valueobjects.CurrencyIDR)
if err != nil {
    return fmt.Errorf("invalid amount: %w", err)
}
```

### 2. Handle Errors Gracefully
```go
inquiryResp, err := inquiryUseCase.Execute(ctx, req)
if err != nil {
    // Log error and return appropriate response
    logger.Errorf("Inquiry failed: %v", err)
    return handleInquiryError(err)
}

if !inquiryResp.Success {
    // Handle business logic failure
    return handleBusinessError(inquiryResp.InquiryReason)
}
```

### 3. Use Proper Context
```go
// Set timeout for operations
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

// Pass context to all operations
result, err := useCase.Execute(ctx, request)
```

### 4. Log Important Events
```go
logger.Infof("Processing payment for customer %s, amount %s", 
    customerNumber, amount.StringWithCurrency())

if err != nil {
    logger.Errorf("Payment failed for customer %s: %v", customerNumber, err)
}
```

## Testing

Each example can be tested with different scenarios:

### Successful Flow
- Valid company code and customer number
- Proper amount formatting
- Successful inquiry followed by payment

### Error Scenarios
- Invalid customer number
- Insufficient balance
- Network timeouts
- Authentication failures

### Edge Cases
- Maximum amount values
- Different currency codes
- Special characters in customer names
- Long reference numbers

## Integration Notes

When integrating with the actual OttoPay API:

1. **Environment Configuration**: Use different configurations for development, staging, and production
2. **Security**: Store credentials securely (environment variables, secret management)
3. **Monitoring**: Implement proper logging and monitoring for production use
4. **Rate Limiting**: Respect API rate limits and implement backoff strategies
5. **Idempotency**: Ensure payment operations are idempotent using unique request IDs

## Support

For questions about the examples or integration:

1. Check the main documentation in `/docs`
2. Review the test files for additional usage patterns
3. Examine the domain models for business rules
4. Refer to the OttoPay API specification for detailed requirements
