package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/external"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/inquiry"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/payment"
	"repo.nusatek.id/sugeng/ottopay/internal/utils"
)

func main() {
	// Initialize logger
	logger := utils.NewDefaultLogger()
	logger.Info("Starting OttoPay example application")

	// Initialize configuration
	config := &external.OttoPayAPIConfig{
		BaseURL:  "https://api.ottopay.example.com",
		Username: "demo_user",
		Password: "demo_password",
		APIKey:   "demo_api_key",
		Timeout:  30 * time.Second,
		Logger:   logger,
	}

	// Initialize OttoPay API adapter
	apiAdapter := external.NewOttoPayAPIAdapter(config)

	// Initialize validation service
	validationService := external.NewValidationService()

	// Initialize repositories (using in-memory for this example)
	auditRepo := repositories.NewInMemoryAuditRepository()
	tokenRepo := repositories.NewInMemoryTokenRepository()

	// Initialize use cases
	inquiryUseCase := inquiry.NewCustomerInquiryUseCase(
		apiAdapter,        // ottoPayService
		validationService, // validationService
		auditRepo,         // auditRepo
		tokenRepo,         // tokenRepo
	)

	paymentUseCase := payment.NewProcessPaymentUseCase(
		repositories.NewInMemoryPaymentRepository(), // paymentRepo
		apiAdapter,        // ottoPayService
		validationService, // validationService
		auditRepo,         // auditRepo
		tokenRepo,         // tokenRepo
	)

	ctx := context.Background()

	// Example 1: Customer Inquiry
	fmt.Println("\n=== Customer Inquiry Example ===")

	companyCode, _ := valueobjects.NewCompanyCode("12345")
	customerNumber, _ := valueobjects.NewCustomerNumber("1234567890")
	inquiryRequestID, _ := valueobjects.GenerateInquiryRequestID()

	inquiryReq := &inquiry.CustomerInquiryRequest{
		CompanyCode:    companyCode,
		CustomerNumber: customerNumber,
		RequestID:      inquiryRequestID,
		ChannelType:    "API",
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	inquiryResp, err := inquiryUseCase.Execute(ctx, inquiryReq)
	if err != nil {
		log.Printf("Inquiry failed: %v", err)
	} else {
		fmt.Printf("Inquiry Result:\n")
		fmt.Printf("  Success: %v\n", inquiryResp.Success)
		fmt.Printf("  Status: %s\n", inquiryResp.InquiryStatus)
		fmt.Printf("  Message: %s\n", inquiryResp.Message)

		if inquiryResp.Customer != nil {
			fmt.Printf("  Customer: %s\n", inquiryResp.Customer.Name)
			fmt.Printf("  Amount: %s %s\n",
				inquiryResp.Customer.TotalAmount.String(),
				inquiryResp.Customer.CurrencyCode.String())
		}
	}

	// Example 2: Payment Processing (only if inquiry was successful)
	if inquiryResp != nil && inquiryResp.Success && inquiryResp.Customer != nil {
		fmt.Println("\n=== Payment Processing Example ===")

		paymentRequestID, _ := valueobjects.GeneratePaymentRequestID()

		paymentReq := &payment.ProcessPaymentRequest{
			CompanyCode:    companyCode,
			CustomerNumber: customerNumber,
			RequestID:      paymentRequestID,
			CustomerName:   inquiryResp.Customer.Name,
			CurrencyCode:   inquiryResp.Customer.CurrencyCode,
			PaidAmount:     inquiryResp.Customer.TotalAmount, // Pay full amount
			TotalAmount:    inquiryResp.Customer.TotalAmount,
			Reference:      "REF-" + paymentRequestID.String(),
			ChannelType:    "API",
			SourceIP:       "127.0.0.1",
			UserAgent:      "OttoPay-Example/1.0",
		}

		paymentResp, err := paymentUseCase.Execute(ctx, paymentReq)
		if err != nil {
			log.Printf("Payment failed: %v", err)
		} else {
			fmt.Printf("Payment Result:\n")
			fmt.Printf("  Success: %v\n", paymentResp.Success)
			fmt.Printf("  Status: %s\n", paymentResp.Status)
			fmt.Printf("  Message: %s\n", paymentResp.Message)
			fmt.Printf("  Transaction ID: %s\n", paymentResp.TransactionID)

			if paymentResp.Payment != nil {
				fmt.Printf("  Paid Amount: %s %s\n",
					paymentResp.Payment.PaidAmount.String(),
					paymentResp.Payment.CurrencyCode.String())
				fmt.Printf("  Reference: %s\n", paymentResp.Payment.Reference)
			}
		}
	}

	// Example 3: Error Handling
	fmt.Println("\n=== Error Handling Example ===")

	// Try inquiry with invalid customer number
	invalidCustomerNumber, _ := valueobjects.NewCustomerNumber("0000000000")
	invalidInquiryRequestID, _ := valueobjects.GenerateInquiryRequestID()

	invalidInquiryReq := &inquiry.CustomerInquiryRequest{
		CompanyCode:    companyCode,
		CustomerNumber: invalidCustomerNumber,
		RequestID:      invalidInquiryRequestID,
		ChannelType:    "API",
		SourceIP:       "127.0.0.1",
		UserAgent:      "OttoPay-Example/1.0",
	}

	invalidInquiryResp, err := inquiryUseCase.Execute(ctx, invalidInquiryReq)
	if err != nil {
		fmt.Printf("Expected error occurred: %v\n", err)
	} else if invalidInquiryResp != nil && !invalidInquiryResp.Success {
		fmt.Printf("Inquiry failed as expected:\n")
		fmt.Printf("  Status: %s\n", invalidInquiryResp.InquiryStatus)
		fmt.Printf("  Reason (EN): %s\n", invalidInquiryResp.InquiryReason.English)
		fmt.Printf("  Reason (ID): %s\n", invalidInquiryResp.InquiryReason.Indonesian)
	}

	// Example 4: Amount Validation
	fmt.Println("\n=== Amount Validation Example ===")

	// Valid amount
	validAmount, err := valueobjects.NewAmount(100000.50, valueobjects.CurrencyIDR)
	if err != nil {
		fmt.Printf("Unexpected error: %v\n", err)
	} else {
		fmt.Printf("Valid amount: %s\n", validAmount.String())
	}

	// Invalid amount (too many decimal places)
	_, err = valueobjects.NewAmount(100000.567, valueobjects.CurrencyIDR)
	if err != nil {
		fmt.Printf("Expected validation error: %v\n", err)
	}

	// Invalid amount (negative)
	_, err = valueobjects.NewAmount(-100.00, valueobjects.CurrencyIDR)
	if err != nil {
		fmt.Printf("Expected validation error: %v\n", err)
	}

	// Example 5: Currency Operations
	fmt.Println("\n=== Currency Operations Example ===")

	idrAmount, _ := valueobjects.NewAmount(100000.00, valueobjects.CurrencyIDR)
	usdAmount, _ := valueobjects.NewAmount(1000.00, valueobjects.CurrencyUSD)

	fmt.Printf("IDR Amount: %s\n", idrAmount.StringWithCurrency())
	fmt.Printf("USD Amount: %s\n", usdAmount.StringWithCurrency())
	fmt.Printf("IDR Amount with symbol: %s\n", idrAmount.StringWithSymbol())
	fmt.Printf("USD Amount with symbol: %s\n", usdAmount.StringWithSymbol())

	// Try to add different currencies (should fail)
	_, err = idrAmount.Add(usdAmount)
	if err != nil {
		fmt.Printf("Expected currency mismatch error: %v\n", err)
	}

	// Add same currencies
	doubleIDR, err := idrAmount.Add(idrAmount)
	if err != nil {
		fmt.Printf("Unexpected error: %v\n", err)
	} else {
		fmt.Printf("Double IDR amount: %s\n", doubleIDR.StringWithCurrency())
	}

	logger.Info("OttoPay example application completed")
}
