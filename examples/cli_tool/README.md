# OttoPay CLI Tool

A command-line interface for interacting with the OttoPay Virtual Account Non Billing system. This tool provides easy access to all OttoPay operations from the terminal.

## Features

- **Authentication**: Get and manage authentication tokens
- **Customer Inquiry**: Query customer information and account details
- **Payment Processing**: Process payments with full validation
- **Value Object Validation**: Validate company codes, customer numbers, currencies, and amounts
- **Multiple Output Formats**: Support for table, JSON, and CSV output formats
- **Configuration Management**: Environment variables and config file support
- **Verbose Logging**: Optional detailed logging for debugging

## Installation

### Build from Source

```bash
cd examples/cli_tool
go build -o ottopay-cli main.go
```

### Install Globally

```bash
go install
```

## Configuration

### Environment Variables

Set these environment variables for default configuration:

```bash
export OTTOPAY_BASE_URL="https://api.ottopay.production.com"
export OTTOPAY_USERNAME="your_username"
export OTTOPAY_PASSWORD="your_password"
export OTTOPAY_API_KEY="your_api_key"
```

### Configuration File

Create a `config.json` file in the same directory:

```json
{
  "base_url": "https://api.ottopay.production.com",
  "username": "your_username",
  "password": "your_password",
  "api_key": "your_api_key",
  "timeout": 30
}
```

## Usage

### Global Options

- `-config`: Configuration file path (default: config.json)
- `-verbose`: Enable verbose output
- `-format`: Output format: json, table, csv (default: table)
- `-timeout`: Request timeout in seconds (default: 30)

### Commands

#### 1. Authentication

Get an authentication token:

```bash
# Using default credentials
./ottopay-cli auth

# Using custom credentials
./ottopay-cli auth -username myuser -password mypass

# JSON output
./ottopay-cli auth -format json
```

**Example Output:**
```
Authentication Result:
  Success: true
  Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  Expires At: 2024-01-01T13:00:00Z
  Request ID: TOK202401011200000001
```

#### 2. Customer Inquiry

Query customer information:

```bash
# Basic inquiry
./ottopay-cli inquiry -company 12173 -customer ***********

# With authentication token
./ottopay-cli inquiry -company 12173 -customer *********** -token "your_token"

# CSV output
./ottopay-cli inquiry -company 12173 -customer *********** -format csv
```

**Example Output:**
```
Inquiry Result:
  Success: true
  Status: SUCCESS
  Message: Customer inquiry completed successfully
  Request ID: INQ202401011200000001
  Response Time: 1250ms
  Customer:
    Name: Customer Virtual Account
    Amount: 150000.00 IDR
```

#### 3. Payment Processing

Process a payment:

```bash
# Full payment
./ottopay-cli payment \
  -company 12173 \
  -customer *********** \
  -name "John Doe" \
  -currency IDR \
  -paid 150000 \
  -total 150000 \
  -reference REF123456 \
  -token "your_token"

# Partial payment
./ottopay-cli payment \
  -company 12173 \
  -customer *********** \
  -name "John Doe" \
  -currency IDR \
  -paid 100000 \
  -total 150000 \
  -reference REF123456 \
  -token "your_token"
```

**Example Output:**
```
Payment Result:
  Success: true
  Status: SUCCESS
  Message: Payment processed successfully
  Request ID: PAY202401011200000001
  Transaction ID: TXN202401011200000001
  Response Time: 2100ms
  Payment:
    Paid Amount: 150000.00 IDR
    Reference: REF123456
```

#### 4. Value Object Validation

Validate various value objects:

```bash
# Validate company code
./ottopay-cli validate -type company -value 12173

# Validate customer number
./ottopay-cli validate -type customer -value ***********

# Validate currency code
./ottopay-cli validate -type currency -value IDR

# Validate amount
./ottopay-cli validate -type amount -value 150000 -currency IDR
```

**Example Output:**
```
Validation Result:
  Type: Company Code
  Value: 12173
  Valid: true
  Message: Valid
```

### Output Formats

#### Table Format (Default)

Human-readable table format with clear labels and structure.

#### JSON Format

Machine-readable JSON format for integration with other tools:

```bash
./ottopay-cli inquiry -company 12173 -customer *********** -format json
```

```json
{
  "success": true,
  "inquiry_status": "SUCCESS",
  "message": "Customer inquiry completed successfully",
  "request_id": "INQ202401011200000001",
  "response_time": 1250,
  "customer": {
    "company_code": "12173",
    "customer_number": "***********",
    "name": "Customer Virtual Account",
    "currency_code": "IDR",
    "total_amount": "150000.00"
  }
}
```

#### CSV Format

Comma-separated values for spreadsheet import:

```bash
./ottopay-cli inquiry -company 12173 -customer *********** -format csv
```

```csv
success,status,message,customer_name,amount,currency
true,SUCCESS,Customer inquiry completed successfully,Customer Virtual Account,150000.00,IDR
```

## Advanced Usage

### Batch Processing

Process multiple operations using shell scripts:

```bash
#!/bin/bash

# Get token
TOKEN=$(./ottopay-cli auth -format json | jq -r '.token')

# Process multiple inquiries
for customer in *********** *********** ***********; do
  ./ottopay-cli inquiry -company 12173 -customer $customer -token "$TOKEN" -format csv
done
```

### Pipeline Integration

Use with other command-line tools:

```bash
# Extract customer names
./ottopay-cli inquiry -company 12173 -customer *********** -format json | \
  jq -r '.customer.name'

# Count successful payments
./ottopay-cli payment -company 12173 -customer *********** \
  -name "John Doe" -currency IDR -paid 150000 -total 150000 \
  -reference REF123456 -format json | \
  jq -r '.success'
```

### Error Handling

The CLI tool returns appropriate exit codes:

- `0`: Success
- `1`: Error (invalid arguments, API failure, etc.)

```bash
# Check if operation succeeded
if ./ottopay-cli inquiry -company 12173 -customer ***********; then
  echo "Inquiry successful"
else
  echo "Inquiry failed"
fi
```

## Examples

### Complete Workflow

```bash
# 1. Get authentication token
TOKEN=$(./ottopay-cli auth -username demo_user -password demo_password -format json | jq -r '.token')

# 2. Perform customer inquiry
./ottopay-cli inquiry -company 12173 -customer *********** -token "$TOKEN"

# 3. Process payment
./ottopay-cli payment \
  -company 12173 \
  -customer *********** \
  -name "Customer Virtual Account" \
  -currency IDR \
  -paid 150000 \
  -total 150000 \
  -reference REF$(date +%s) \
  -token "$TOKEN"
```

### Validation Examples

```bash
# Validate multiple company codes
for code in 12173 12174 12175; do
  ./ottopay-cli validate -type company -value $code
done

# Validate amounts in different currencies
./ottopay-cli validate -type amount -value 150000 -currency IDR
./ottopay-cli validate -type amount -value 100.50 -currency USD
```

### Monitoring and Logging

```bash
# Enable verbose logging
./ottopay-cli inquiry -company 12173 -customer *********** -verbose

# Log to file
./ottopay-cli payment \
  -company 12173 \
  -customer *********** \
  -name "John Doe" \
  -currency IDR \
  -paid 150000 \
  -total 150000 \
  -reference REF123456 \
  -verbose 2>&1 | tee payment.log
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```bash
   # Check credentials
   ./ottopay-cli auth -username your_user -password your_pass -verbose
   ```

2. **Invalid Company Code**
   ```bash
   # Validate format
   ./ottopay-cli validate -type company -value 12173
   ```

3. **Network Timeout**
   ```bash
   # Increase timeout
   ./ottopay-cli inquiry -company 12173 -customer *********** -timeout 60
   ```

4. **Configuration Issues**
   ```bash
   # Check configuration
   cat config.json
   
   # Use environment variables
   export OTTOPAY_USERNAME="your_username"
   ./ottopay-cli auth
   ```

### Debug Mode

Enable verbose output for detailed debugging:

```bash
./ottopay-cli inquiry -company 12173 -customer *********** -verbose
```

This will show:
- Request/response details
- Validation steps
- Error stack traces
- Timing information

## Integration

### Shell Scripts

```bash
#!/bin/bash
# payment_processor.sh

COMPANY_CODE="12173"
CURRENCY="IDR"
TOKEN=$(./ottopay-cli auth -format json | jq -r '.token')

while IFS=',' read -r customer_number customer_name amount reference; do
  ./ottopay-cli payment \
    -company "$COMPANY_CODE" \
    -customer "$customer_number" \
    -name "$customer_name" \
    -currency "$CURRENCY" \
    -paid "$amount" \
    -total "$amount" \
    -reference "$reference" \
    -token "$TOKEN" \
    -format csv >> payments.csv
done < payments_input.csv
```

### Makefile Integration

```makefile
# Makefile
.PHONY: build test-auth test-inquiry test-payment

build:
	go build -o ottopay-cli main.go

test-auth:
	./ottopay-cli auth -verbose

test-inquiry:
	./ottopay-cli inquiry -company 12173 -customer *********** -verbose

test-payment:
	./ottopay-cli payment \
		-company 12173 \
		-customer *********** \
		-name "Test Customer" \
		-currency IDR \
		-paid 150000 \
		-total 150000 \
		-reference TEST$(shell date +%s) \
		-verbose
```

## Support

For help with the CLI tool:

1. Use the built-in help: `./ottopay-cli help`
2. Check command-specific help: `./ottopay-cli [command] -help`
3. Enable verbose mode for debugging: `-verbose`
4. Refer to the main OttoPay documentation
5. Check the examples directory for more usage patterns
