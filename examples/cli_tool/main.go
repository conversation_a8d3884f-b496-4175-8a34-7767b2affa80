package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strconv"
	"time"
)

const (
	version = "1.0.0"
	usage   = `OttoPay CLI Tool v%s

USAGE:
    ottopay-cli [COMMAND] [OPTIONS]

COMMANDS:
    auth        Get authentication token
    inquiry     Perform customer inquiry
    payment     Process payment
    validate    Validate value objects
    help        Show this help message
    version     Show version information

GLOBAL OPTIONS:
    -config     Configuration file path (default: config.json)
    -verbose    Enable verbose output
    -format     Output format: json, table, csv (default: table)
    -timeout    Request timeout in seconds (default: 30)

EXAMPLES:
    # Get authentication token
    ottopay-cli auth -username demo_user -password demo_password

    # Customer inquiry
    ottopay-cli inquiry -company 12173 -customer 56751590099

    # Process payment
    ottopay-cli payment -company 12173 -customer 56751590099 \
        -name "<PERSON> Doe" -currency IDR -paid 150000 -total 150000 \
        -reference REF123456

    # Validate company code
    ottopay-cli validate -type company -value 12173

For detailed help on a specific command, use:
    ottopay-cli [COMMAND] -help
`
)

// Mock types for demonstration
type Config struct {
	BaseURL  string `json:"base_url"`
	Username string `json:"username"`
	Password string `json:"password"`
	APIKey   string `json:"api_key"`
	Timeout  int    `json:"timeout"`
}

type CLIApp struct {
	config  *Config
	verbose bool
	format  string
	timeout time.Duration
}

// Mock response types
type AuthResponse struct {
	Success   bool   `json:"success"`
	Token     string `json:"token"`
	ExpiresAt string `json:"expires_at"`
	RequestID string `json:"request_id"`
}

type InquiryResponse struct {
	Success      bool   `json:"success"`
	Status       string `json:"status"`
	Message      string `json:"message"`
	CustomerName string `json:"customer_name"`
	Amount       string `json:"amount"`
	Currency     string `json:"currency"`
}

type PaymentResponse struct {
	Success       bool   `json:"success"`
	Status        string `json:"status"`
	Message       string `json:"message"`
	TransactionID string `json:"transaction_id"`
	PaidAmount    string `json:"paid_amount"`
	Currency      string `json:"currency"`
}

type ValidationResult struct {
	Type    string `json:"type"`
	Value   string `json:"value"`
	Valid   bool   `json:"valid"`
	Message string `json:"message"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Printf(usage, version)
		os.Exit(1)
	}

	command := os.Args[1]
	args := os.Args[2:]

	app := &CLIApp{
		format:  "table",
		timeout: 30 * time.Second,
	}

	// Parse global flags
	app.parseGlobalFlags(args)

	// Load configuration
	app.loadConfig()

	// Execute command
	switch command {
	case "auth":
		app.handleAuth(args)
	case "inquiry":
		app.handleInquiry(args)
	case "payment":
		app.handlePayment(args)
	case "validate":
		app.handleValidate(args)
	case "version":
		fmt.Printf("OttoPay CLI Tool v%s\n", version)
	case "help", "-h", "--help":
		fmt.Printf(usage, version)
	default:
		fmt.Printf("Unknown command: %s\n\n", command)
		fmt.Printf(usage, version)
		os.Exit(1)
	}
}

func (app *CLIApp) parseGlobalFlags(args []string) {
	for i, arg := range args {
		switch arg {
		case "-verbose":
			app.verbose = true
		case "-format":
			if i+1 < len(args) {
				app.format = args[i+1]
			}
		case "-timeout":
			if i+1 < len(args) {
				if timeout, err := strconv.Atoi(args[i+1]); err == nil {
					app.timeout = time.Duration(timeout) * time.Second
				}
			}
		}
	}
}

func (app *CLIApp) loadConfig() {
	// Default configuration
	app.config = &Config{
		BaseURL:  getEnv("OTTOPAY_BASE_URL", "https://api.ottopay.example.com"),
		Username: getEnv("OTTOPAY_USERNAME", "demo_user"),
		Password: getEnv("OTTOPAY_PASSWORD", "demo_password"),
		APIKey:   getEnv("OTTOPAY_API_KEY", "demo_api_key"),
		Timeout:  30,
	}

	// Try to load from config file
	if data, err := os.ReadFile("config.json"); err == nil {
		json.Unmarshal(data, app.config)
	}
}

func (app *CLIApp) handleAuth(args []string) {
	fs := flag.NewFlagSet("auth", flag.ExitOnError)
	username := fs.String("username", app.config.Username, "Username for authentication")
	password := fs.String("password", app.config.Password, "Password for authentication")
	fs.Parse(args)

	if *username == "" || *password == "" {
		fmt.Println("Error: username and password are required")
		fs.Usage()
		os.Exit(1)
	}

	// Mock authentication
	response := &AuthResponse{
		Success:   true,
		Token:     "mock_token_" + fmt.Sprintf("%d", time.Now().Unix()),
		ExpiresAt: time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		RequestID: "TOK" + time.Now().Format("20060102150405") + "001",
	}

	app.printAuthResponse(response)
}

func (app *CLIApp) handleInquiry(args []string) {
	fs := flag.NewFlagSet("inquiry", flag.ExitOnError)
	companyCode := fs.String("company", "", "Company code (5 digits)")
	customerNumber := fs.String("customer", "", "Customer number (max 11 digits)")
	_ = fs.String("token", "", "Authentication token")
	fs.Parse(args)

	if *companyCode == "" || *customerNumber == "" {
		fmt.Println("Error: company code and customer number are required")
		fs.Usage()
		os.Exit(1)
	}

	// Mock validation
	if len(*companyCode) != 5 {
		app.printError("Invalid company code", fmt.Errorf("company code must be 5 digits"))
		os.Exit(1)
	}

	if len(*customerNumber) > 11 {
		app.printError("Invalid customer number", fmt.Errorf("customer number must be max 11 digits"))
		os.Exit(1)
	}

	// Mock inquiry
	response := &InquiryResponse{
		Success:      true,
		Status:       "SUCCESS",
		Message:      "Customer inquiry completed successfully",
		CustomerName: "Customer Virtual Account",
		Amount:       "150000.00",
		Currency:     "IDR",
	}

	app.printInquiryResponse(response)
}

func (app *CLIApp) handlePayment(args []string) {
	fs := flag.NewFlagSet("payment", flag.ExitOnError)
	companyCode := fs.String("company", "", "Company code (5 digits)")
	customerNumber := fs.String("customer", "", "Customer number (max 11 digits)")
	customerName := fs.String("name", "", "Customer name")
	currencyCode := fs.String("currency", "IDR", "Currency code (IDR, USD)")
	paidAmount := fs.Float64("paid", 0, "Paid amount")
	totalAmount := fs.Float64("total", 0, "Total amount")
	reference := fs.String("reference", "", "Payment reference")
	_ = fs.String("token", "", "Authentication token")
	fs.Parse(args)

	if *companyCode == "" || *customerNumber == "" || *customerName == "" ||
		*paidAmount <= 0 || *totalAmount <= 0 || *reference == "" {
		fmt.Println("Error: all payment fields are required")
		fs.Usage()
		os.Exit(1)
	}

	// Mock validation
	if len(*companyCode) != 5 {
		app.printError("Invalid company code", fmt.Errorf("company code must be 5 digits"))
		os.Exit(1)
	}

	if *currencyCode != "IDR" && *currencyCode != "USD" {
		app.printError("Invalid currency code", fmt.Errorf("currency must be IDR or USD"))
		os.Exit(1)
	}

	// Mock payment
	response := &PaymentResponse{
		Success:       true,
		Status:        "SUCCESS",
		Message:       "Payment processed successfully",
		TransactionID: "TXN" + time.Now().Format("20060102150405") + "001",
		PaidAmount:    fmt.Sprintf("%.2f", *paidAmount),
		Currency:      *currencyCode,
	}

	app.printPaymentResponse(response)
}

func (app *CLIApp) handleValidate(args []string) {
	fs := flag.NewFlagSet("validate", flag.ExitOnError)
	valueType := fs.String("type", "", "Value type: company, customer, currency, amount")
	value := fs.String("value", "", "Value to validate")
	_ = fs.String("currency", "IDR", "Currency for amount validation")
	fs.Parse(args)

	if *valueType == "" || *value == "" {
		fmt.Println("Error: type and value are required")
		fs.Usage()
		os.Exit(1)
	}

	var valid bool
	var message string

	switch *valueType {
	case "company":
		valid = len(*value) == 5
		if valid {
			message = "Valid company code"
		} else {
			message = "Company code must be exactly 5 digits"
		}
	case "customer":
		valid = len(*value) <= 11 && len(*value) > 0
		if valid {
			message = "Valid customer number"
		} else {
			message = "Customer number must be 1-11 characters"
		}
	case "currency":
		valid = *value == "IDR" || *value == "USD"
		if valid {
			message = "Valid currency code"
		} else {
			message = "Currency must be IDR or USD"
		}
	case "amount":
		amount, err := strconv.ParseFloat(*value, 64)
		valid = err == nil && amount > 0
		if valid {
			message = "Valid amount"
		} else {
			message = "Amount must be a positive number"
		}
	default:
		fmt.Printf("Unknown validation type: %s\n", *valueType)
		fmt.Println("Supported types: company, customer, currency, amount")
		os.Exit(1)
	}

	result := &ValidationResult{
		Type:    *valueType,
		Value:   *value,
		Valid:   valid,
		Message: message,
	}

	app.printValidationResult(result)
}

func (app *CLIApp) printAuthResponse(response *AuthResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,token,expires_at,request_id")
		fmt.Printf("%v,%s,%s,%s\n", response.Success, response.Token, response.ExpiresAt, response.RequestID)
	default: // table
		fmt.Println("Authentication Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Token: %s\n", response.Token)
		fmt.Printf("  Expires At: %s\n", response.ExpiresAt)
		fmt.Printf("  Request ID: %s\n", response.RequestID)
	}
}

func (app *CLIApp) printInquiryResponse(response *InquiryResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,status,message,customer_name,amount,currency")
		fmt.Printf("%v,%s,%s,%s,%s,%s\n", response.Success, response.Status,
			response.Message, response.CustomerName, response.Amount, response.Currency)
	default: // table
		fmt.Println("Inquiry Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Status: %s\n", response.Status)
		fmt.Printf("  Message: %s\n", response.Message)
		fmt.Printf("  Customer Name: %s\n", response.CustomerName)
		fmt.Printf("  Amount: %s %s\n", response.Amount, response.Currency)
	}
}

func (app *CLIApp) printPaymentResponse(response *PaymentResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,status,message,transaction_id,paid_amount,currency")
		fmt.Printf("%v,%s,%s,%s,%s,%s\n", response.Success, response.Status,
			response.Message, response.TransactionID, response.PaidAmount, response.Currency)
	default: // table
		fmt.Println("Payment Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Status: %s\n", response.Status)
		fmt.Printf("  Message: %s\n", response.Message)
		fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
		fmt.Printf("  Paid Amount: %s %s\n", response.PaidAmount, response.Currency)
	}
}

func (app *CLIApp) printValidationResult(result *ValidationResult) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(result, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("type,value,valid,message")
		fmt.Printf("%s,%s,%v,%s\n", result.Type, result.Value, result.Valid, result.Message)
	default: // table
		fmt.Printf("Validation Result:\n")
		fmt.Printf("  Type: %s\n", result.Type)
		fmt.Printf("  Value: %s\n", result.Value)
		fmt.Printf("  Valid: %v\n", result.Valid)
		fmt.Printf("  Message: %s\n", result.Message)
	}
}

func (app *CLIApp) printError(message string, err error) {
	fmt.Printf("Error: %s\n", message)
	if app.verbose && err != nil {
		fmt.Printf("Details: %v\n", err)
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
