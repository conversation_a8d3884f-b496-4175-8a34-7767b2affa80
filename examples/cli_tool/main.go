package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strconv"
	"time"

	"repo.nusatek.id/sugeng/ottopay/internal/domain/valueobjects"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/external"
	"repo.nusatek.id/sugeng/ottopay/internal/infrastructure/repositories"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/auth"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/inquiry"
	"repo.nusatek.id/sugeng/ottopay/internal/usecases/payment"
	"repo.nusatek.id/sugeng/ottopay/internal/utils"
)

const (
	version = "1.0.0"
	usage   = `OttoPay CLI Tool v%s

USAGE:
    ottopay-cli [COMMAND] [OPTIONS]

COMMANDS:
    auth        Get authentication token
    inquiry     Perform customer inquiry
    payment     Process payment
    validate    Validate value objects
    help        Show this help message
    version     Show version information

GLOBAL OPTIONS:
    -config     Configuration file path (default: config.json)
    -verbose    Enable verbose output
    -format     Output format: json, table, csv (default: table)
    -timeout    Request timeout in seconds (default: 30)

EXAMPLES:
    # Get authentication token
    ottopay-cli auth -username demo_user -password demo_password

    # Customer inquiry
    ottopay-cli inquiry -company 12173 -customer 56751590099

    # Process payment
    ottopay-cli payment -company 12173 -customer 56751590099 \
        -name "John Doe" -currency IDR -paid 150000 -total 150000 \
        -reference REF123456

    # Validate company code
    ottopay-cli validate -type company -value 12173

For detailed help on a specific command, use:
    ottopay-cli [COMMAND] -help
`
)

type Config struct {
	BaseURL  string `json:"base_url"`
	Username string `json:"username"`
	Password string `json:"password"`
	APIKey   string `json:"api_key"`
	Timeout  int    `json:"timeout"`
}

type CLIApp struct {
	config  *Config
	logger  utils.Logger
	verbose bool
	format  string
	timeout time.Duration
}

func main() {
	if len(os.Args) < 2 {
		fmt.Printf(usage, version)
		os.Exit(1)
	}

	command := os.Args[1]
	args := os.Args[2:]

	app := &CLIApp{
		format:  "table",
		timeout: 30 * time.Second,
	}

	// Parse global flags
	app.parseGlobalFlags(args)

	// Initialize logger
	app.logger = utils.NewDefaultLogger()
	if !app.verbose {
		app.logger = utils.NewSilentLogger()
	}

	// Load configuration
	app.loadConfig()

	// Execute command
	switch command {
	case "auth":
		app.handleAuth(args)
	case "inquiry":
		app.handleInquiry(args)
	case "payment":
		app.handlePayment(args)
	case "validate":
		app.handleValidate(args)
	case "version":
		fmt.Printf("OttoPay CLI Tool v%s\n", version)
	case "help", "-h", "--help":
		fmt.Printf(usage, version)
	default:
		fmt.Printf("Unknown command: %s\n\n", command)
		fmt.Printf(usage, version)
		os.Exit(1)
	}
}

func (app *CLIApp) parseGlobalFlags(args []string) {
	configFile := "config.json"

	for i, arg := range args {
		switch arg {
		case "-config":
			if i+1 < len(args) {
				configFile = args[i+1]
			}
		case "-verbose":
			app.verbose = true
		case "-format":
			if i+1 < len(args) {
				app.format = args[i+1]
			}
		case "-timeout":
			if i+1 < len(args) {
				if timeout, err := strconv.Atoi(args[i+1]); err == nil {
					app.timeout = time.Duration(timeout) * time.Second
				}
			}
		}
	}
}

func (app *CLIApp) loadConfig() {
	// Default configuration
	app.config = &Config{
		BaseURL:  getEnv("OTTOPAY_BASE_URL", "https://api.ottopay.example.com"),
		Username: getEnv("OTTOPAY_USERNAME", "demo_user"),
		Password: getEnv("OTTOPAY_PASSWORD", "demo_password"),
		APIKey:   getEnv("OTTOPAY_API_KEY", "demo_api_key"),
		Timeout:  30,
	}

	// Try to load from config file
	if data, err := os.ReadFile("config.json"); err == nil {
		json.Unmarshal(data, app.config)
	}
}

func (app *CLIApp) handleAuth(args []string) {
	fs := flag.NewFlagSet("auth", flag.ExitOnError)
	username := fs.String("username", app.config.Username, "Username for authentication")
	password := fs.String("password", app.config.Password, "Password for authentication")
	fs.Parse(args)

	if *username == "" || *password == "" {
		fmt.Println("Error: username and password are required")
		fs.Usage()
		os.Exit(1)
	}

	// Initialize services
	authService := external.NewAuthenticationService()
	tokenRepo := repositories.NewInMemoryTokenRepository()
	auditRepo := repositories.NewInMemoryAuditRepository()

	authUseCase := auth.NewGetTokenUseCase(authService, tokenRepo, auditRepo)

	// Generate request ID
	requestID, _ := valueobjects.GenerateTokenRequestID()

	// Create request
	req := &auth.GetTokenRequest{
		Username:    *username,
		Password:    *password,
		RequestID:   requestID,
		ChannelType: "CLI",
		SourceIP:    "127.0.0.1",
		UserAgent:   fmt.Sprintf("OttoPay-CLI/%s", version),
	}

	// Execute
	ctx, cancel := context.WithTimeout(context.Background(), app.timeout)
	defer cancel()

	response, err := authUseCase.Execute(ctx, req)
	if err != nil {
		app.printError("Authentication failed", err)
		os.Exit(1)
	}

	app.printAuthResponse(response)
}

func (app *CLIApp) handleInquiry(args []string) {
	fs := flag.NewFlagSet("inquiry", flag.ExitOnError)
	companyCode := fs.String("company", "", "Company code (5 digits)")
	customerNumber := fs.String("customer", "", "Customer number (max 11 digits)")
	token := fs.String("token", "", "Authentication token")
	fs.Parse(args)

	if *companyCode == "" || *customerNumber == "" {
		fmt.Println("Error: company code and customer number are required")
		fs.Usage()
		os.Exit(1)
	}

	// Validate and create value objects
	company, err := valueobjects.NewCompanyCode(*companyCode)
	if err != nil {
		app.printError("Invalid company code", err)
		os.Exit(1)
	}

	customer, err := valueobjects.NewCustomerNumber(*customerNumber)
	if err != nil {
		app.printError("Invalid customer number", err)
		os.Exit(1)
	}

	// Initialize services
	config := &external.OttoPayAPIConfig{
		BaseURL:  app.config.BaseURL,
		Username: app.config.Username,
		Password: app.config.Password,
		APIKey:   app.config.APIKey,
		Timeout:  app.timeout,
		Logger:   app.logger,
	}

	apiAdapter := external.NewOttoPayAPIAdapter(config)
	validationService := external.NewValidationService()
	auditRepo := repositories.NewInMemoryAuditRepository()
	tokenRepo := repositories.NewInMemoryTokenRepository()

	inquiryUseCase := inquiry.NewCustomerInquiryUseCase(
		apiAdapter,
		validationService,
		auditRepo,
		tokenRepo,
	)

	// Generate request ID
	requestID, _ := valueobjects.GenerateInquiryRequestID()

	// Create request
	req := &inquiry.CustomerInquiryRequest{
		CompanyCode:    company,
		CustomerNumber: customer,
		RequestID:      requestID,
		ChannelType:    "CLI",
		Token:          *token,
		SourceIP:       "127.0.0.1",
		UserAgent:      fmt.Sprintf("OttoPay-CLI/%s", version),
	}

	// Execute
	ctx, cancel := context.WithTimeout(context.Background(), app.timeout)
	defer cancel()

	response, err := inquiryUseCase.Execute(ctx, req)
	if err != nil {
		app.printError("Inquiry failed", err)
		os.Exit(1)
	}

	app.printInquiryResponse(response)
}

func (app *CLIApp) handlePayment(args []string) {
	fs := flag.NewFlagSet("payment", flag.ExitOnError)
	companyCode := fs.String("company", "", "Company code (5 digits)")
	customerNumber := fs.String("customer", "", "Customer number (max 11 digits)")
	customerName := fs.String("name", "", "Customer name")
	currencyCode := fs.String("currency", "IDR", "Currency code (IDR, USD)")
	paidAmount := fs.Float64("paid", 0, "Paid amount")
	totalAmount := fs.Float64("total", 0, "Total amount")
	reference := fs.String("reference", "", "Payment reference")
	token := fs.String("token", "", "Authentication token")
	fs.Parse(args)

	if *companyCode == "" || *customerNumber == "" || *customerName == "" ||
		*paidAmount <= 0 || *totalAmount <= 0 || *reference == "" {
		fmt.Println("Error: all payment fields are required")
		fs.Usage()
		os.Exit(1)
	}

	// Validate and create value objects
	company, err := valueobjects.NewCompanyCode(*companyCode)
	if err != nil {
		app.printError("Invalid company code", err)
		os.Exit(1)
	}

	customer, err := valueobjects.NewCustomerNumber(*customerNumber)
	if err != nil {
		app.printError("Invalid customer number", err)
		os.Exit(1)
	}

	currency, err := valueobjects.NewCurrencyCode(*currencyCode)
	if err != nil {
		app.printError("Invalid currency code", err)
		os.Exit(1)
	}

	paid, err := valueobjects.NewAmount(*paidAmount, currency)
	if err != nil {
		app.printError("Invalid paid amount", err)
		os.Exit(1)
	}

	total, err := valueobjects.NewAmount(*totalAmount, currency)
	if err != nil {
		app.printError("Invalid total amount", err)
		os.Exit(1)
	}

	// Initialize services
	config := &external.OttoPayAPIConfig{
		BaseURL:  app.config.BaseURL,
		Username: app.config.Username,
		Password: app.config.Password,
		APIKey:   app.config.APIKey,
		Timeout:  app.timeout,
		Logger:   app.logger,
	}

	apiAdapter := external.NewOttoPayAPIAdapter(config)
	validationService := external.NewValidationService()
	auditRepo := repositories.NewInMemoryAuditRepository()
	tokenRepo := repositories.NewInMemoryTokenRepository()
	paymentRepo := repositories.NewInMemoryPaymentRepository()

	paymentUseCase := payment.NewProcessPaymentUseCase(
		paymentRepo,
		apiAdapter,
		validationService,
		auditRepo,
		tokenRepo,
	)

	// Generate request ID
	requestID, _ := valueobjects.GeneratePaymentRequestID()

	// Create request
	req := &payment.ProcessPaymentRequest{
		CompanyCode:    company,
		CustomerNumber: customer,
		RequestID:      requestID,
		CustomerName:   *customerName,
		CurrencyCode:   currency,
		PaidAmount:     paid,
		TotalAmount:    total,
		Reference:      *reference,
		ChannelType:    "CLI",
		Token:          *token,
		SourceIP:       "127.0.0.1",
		UserAgent:      fmt.Sprintf("OttoPay-CLI/%s", version),
	}

	// Execute
	ctx, cancel := context.WithTimeout(context.Background(), app.timeout)
	defer cancel()

	response, err := paymentUseCase.Execute(ctx, req)
	if err != nil {
		app.printError("Payment failed", err)
		os.Exit(1)
	}

	app.printPaymentResponse(response)
}

func (app *CLIApp) handleValidate(args []string) {
	fs := flag.NewFlagSet("validate", flag.ExitOnError)
	valueType := fs.String("type", "", "Value type: company, customer, currency, amount")
	value := fs.String("value", "", "Value to validate")
	currency := fs.String("currency", "IDR", "Currency for amount validation")
	fs.Parse(args)

	if *valueType == "" || *value == "" {
		fmt.Println("Error: type and value are required")
		fs.Usage()
		os.Exit(1)
	}

	switch *valueType {
	case "company":
		if _, err := valueobjects.NewCompanyCode(*value); err != nil {
			app.printValidationResult("Company Code", *value, false, err.Error())
		} else {
			app.printValidationResult("Company Code", *value, true, "Valid")
		}
	case "customer":
		if _, err := valueobjects.NewCustomerNumber(*value); err != nil {
			app.printValidationResult("Customer Number", *value, false, err.Error())
		} else {
			app.printValidationResult("Customer Number", *value, true, "Valid")
		}
	case "currency":
		if _, err := valueobjects.NewCurrencyCode(*value); err != nil {
			app.printValidationResult("Currency Code", *value, false, err.Error())
		} else {
			app.printValidationResult("Currency Code", *value, true, "Valid")
		}
	case "amount":
		currencyObj, err := valueobjects.NewCurrencyCode(*currency)
		if err != nil {
			app.printError("Invalid currency", err)
			os.Exit(1)
		}

		amount, err := strconv.ParseFloat(*value, 64)
		if err != nil {
			app.printValidationResult("Amount", *value, false, "Invalid number format")
		} else if _, err := valueobjects.NewAmount(amount, currencyObj); err != nil {
			app.printValidationResult("Amount", *value, false, err.Error())
		} else {
			app.printValidationResult("Amount", *value, true, "Valid")
		}
	default:
		fmt.Printf("Unknown validation type: %s\n", *valueType)
		fmt.Println("Supported types: company, customer, currency, amount")
		os.Exit(1)
	}
}

func (app *CLIApp) printAuthResponse(response *auth.GetTokenResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,token,expires_at,request_id")
		fmt.Printf("%v,%s,%s,%s\n", response.Success, response.Token, response.ExpiresAt, response.RequestID)
	default: // table
		fmt.Println("Authentication Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Token: %s\n", response.Token)
		fmt.Printf("  Expires At: %s\n", response.ExpiresAt)
		fmt.Printf("  Request ID: %s\n", response.RequestID)
	}
}

func (app *CLIApp) printInquiryResponse(response *inquiry.CustomerInquiryResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,status,message,customer_name,amount,currency")
		customerName := ""
		amount := ""
		currency := ""
		if response.Customer != nil {
			customerName = response.Customer.Name
			amount = response.Customer.TotalAmount.String()
			currency = response.Customer.CurrencyCode.String()
		}
		fmt.Printf("%v,%s,%s,%s,%s,%s\n", response.Success, response.InquiryStatus,
			response.Message, customerName, amount, currency)
	default: // table
		fmt.Println("Inquiry Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Status: %s\n", response.InquiryStatus)
		fmt.Printf("  Message: %s\n", response.Message)
		fmt.Printf("  Request ID: %s\n", response.RequestID)
		fmt.Printf("  Response Time: %dms\n", response.ResponseTime)

		if response.Customer != nil {
			fmt.Println("  Customer:")
			fmt.Printf("    Name: %s\n", response.Customer.Name)
			fmt.Printf("    Amount: %s %s\n", response.Customer.TotalAmount.String(),
				response.Customer.CurrencyCode.String())
		}
	}
}

func (app *CLIApp) printPaymentResponse(response *payment.ProcessPaymentResponse) {
	switch app.format {
	case "json":
		data, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("success,status,message,transaction_id,paid_amount,currency")
		paidAmount := ""
		currency := ""
		if response.Payment != nil {
			paidAmount = response.Payment.PaidAmount.String()
			currency = response.Payment.CurrencyCode.String()
		}
		fmt.Printf("%v,%s,%s,%s,%s,%s\n", response.Success, response.Status,
			response.Message, response.TransactionID, paidAmount, currency)
	default: // table
		fmt.Println("Payment Result:")
		fmt.Printf("  Success: %v\n", response.Success)
		fmt.Printf("  Status: %s\n", response.Status)
		fmt.Printf("  Message: %s\n", response.Message)
		fmt.Printf("  Request ID: %s\n", response.RequestID)
		fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
		fmt.Printf("  Response Time: %dms\n", response.ResponseTime)

		if response.Payment != nil {
			fmt.Println("  Payment:")
			fmt.Printf("    Paid Amount: %s %s\n", response.Payment.PaidAmount.String(),
				response.Payment.CurrencyCode.String())
			fmt.Printf("    Reference: %s\n", response.Payment.Reference)
		}
	}
}

func (app *CLIApp) printValidationResult(valueType, value string, valid bool, message string) {
	switch app.format {
	case "json":
		result := map[string]interface{}{
			"type":    valueType,
			"value":   value,
			"valid":   valid,
			"message": message,
		}
		data, _ := json.MarshalIndent(result, "", "  ")
		fmt.Println(string(data))
	case "csv":
		fmt.Println("type,value,valid,message")
		fmt.Printf("%s,%s,%v,%s\n", valueType, value, valid, message)
	default: // table
		fmt.Printf("Validation Result:\n")
		fmt.Printf("  Type: %s\n", valueType)
		fmt.Printf("  Value: %s\n", value)
		fmt.Printf("  Valid: %v\n", valid)
		fmt.Printf("  Message: %s\n", message)
	}
}

func (app *CLIApp) printError(message string, err error) {
	fmt.Printf("Error: %s\n", message)
	if app.verbose && err != nil {
		fmt.Printf("Details: %v\n", err)
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
